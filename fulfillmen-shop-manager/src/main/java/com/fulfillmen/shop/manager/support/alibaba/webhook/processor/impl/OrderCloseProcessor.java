/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.domain.entity.enums.TzOrderItemStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderPurchaseStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.TzOrderSupplierStatusEnum;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.exception.WmsApiException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单关闭事件处理器
 * <p>
 * 策略实现类，专门处理各种订单关闭事件，如买家取消、卖家取消等。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCloseProcessor implements OrderEventProcessor {

    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final IWmsManager wmsManager;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单关闭事件: orderId={}", orderId);

        try {
            updateLocalOrderStatusToCancelled(context);
            cancelWmsOrder(context);

            // 关键业务点：处理已付款订单的退款逻辑
            handleRefundForClosedOrder(context);

            log.info("订单关闭事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单关闭事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单关闭事件失败: orderId=" + orderId, e);
        }
    }

    /**
     * 更新本地数据库中的订单状态为“已取消”。
     */
    private void updateLocalOrderStatusToCancelled(OrderContextRecord context) {
        log.debug("更新本地订单状态为已取消: orderId={}", context.getAlibabaOrderIdStr());

        TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(context.getAlibabaOrderIdStr());
        if (orderSupplier.getStatus() == TzOrderSupplierStatusEnum.CANCELLED) {
            log.warn("供应商订单 [{}] 状态已经为'已取消'，跳过数据库更新。", orderSupplier.getPlatformOrderId());
            return;
        }

        TzOrderPurchase purchase = context.tzOrderPurchase();

        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                // 1. 更新供应商订单状态
                orderSupplier.setStatus(TzOrderSupplierStatusEnum.CANCELLED);
                this.tzOrderSupplierMapper.updateById(orderSupplier);

                // 2. 更新该供应商订单下所有订单项的状态
                context.getOrderItemsBySupplierOrderId(orderSupplier.getId())
                    .forEach(item -> item.setStatus(TzOrderItemStatusEnum.CANCELLED));
                this.tzOrderItemMapper.updateBatchById(context.tzOrderItems());

                // 3. 检查是否所有供应商订单都已取消，如果是，则取消整个采购订单
                boolean allSuppliersCancelled = context.tzOrderSuppliers().stream()
                    .allMatch(s -> s.getId().equals(orderSupplier.getId())
                        || s.getStatus() == TzOrderSupplierStatusEnum.CANCELLED);

                if (allSuppliersCancelled && purchase.getOrderStatus() != TzOrderPurchaseStatusEnum.ORDER_CANCELLED) {
                    log.info("采购订单 [{}] 的所有供应商订单均已取消，将更新采购订单状态为已取消。", purchase.getPurchaseOrderNo());
                    purchase.setOrderStatus(TzOrderPurchaseStatusEnum.ORDER_CANCELLED);
                    this.tzOrderPurchaseMapper.updateById(purchase);
                }

            } catch (Exception e) {
                log.error("更新订单关闭状态到数据库时失败: purchaseNo={}, error={}", purchase.getPurchaseOrderNo(), e.getMessage(), e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 尝试取消WMS中的采购订单。
     */
    private void cancelWmsOrder(OrderContextRecord context) {
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = context.getWmsPurchaseOrderDetail();
        if (wmsPurchaseOrderDetail.getStatus() == WmsOrderStatusEnum.CANCELED) {
            log.warn("WMS订单 [{}] 状态已经为'已取消'，跳过WMS取消操作。", wmsPurchaseOrderDetail.getPurchaseNo());
            return;
        }

        log.debug("尝试取消WMS订单: wmsPurchaseNo={}", wmsPurchaseOrderDetail.getPurchaseNo());
        try {
            this.wmsManager.cancelWmsPurchaseOrder(wmsPurchaseOrderDetail.getPurchaseNo(),
                wmsPurchaseOrderDetail.getCusCode());
        } catch (WmsApiException e) {
            // 如果WMS取消失败（例如已经开始拣货），我们只记录日志，不中断主流程
            log.error("调用WMS取消订单接口失败: wmsPurchaseNo={}, error={}", wmsPurchaseOrderDetail.getPurchaseNo(),
                e.getMessage());
        }
    }

    /**
     * 处理已付款订单在关闭后的退款逻辑。
     */
    private void handleRefundForClosedOrder(OrderContextRecord context) {
        TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(context.getAlibabaOrderIdStr());
        // 检查订单是否已付款（通过检查付款时间是否存在）
        if (orderSupplier.getPaymentDate() != null) {
            String orderId = context.getAlibabaOrderIdStr();
            log.info("检测到已付款订单被关闭，需要处理退款。orderId={}", orderId);
            // FIXME: 此处需要调用退款服务或创建退款任务
            // refundService.createRefundForCancelledOrder(context);
            // 或者 eventPublisher.publishEvent(new RefundRequiredEvent(this, context));
            log.warn("FIXBUG: 1688订单 [{}] 已付款但被取消，退款逻辑尚未实现！", orderId);
        }
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE,
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE,
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE);
    }
}
