/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.alibaba.IOrderManager;
import com.fulfillmen.shop.manager.support.alibaba.service.ManualOrderSyncService;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.notification.service.WeChatNotificationService;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.request.order.OrderDetailRequestRecord;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.enums.OrderStatusEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;
import com.fulfillmen.support.wms.dto.request.PurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 手动订单同步服务实现
 *
 * <AUTHOR>
 * @date 2025/08/09 12:10
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ManualOrderSyncServiceImpl implements ManualOrderSyncService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final TzOrderPurchaseMapper orderPurchaseMapper;
    private final TzOrderSupplierMapper orderSupplierMapper;
    private final IOrderManager orderManager;
    private final IWmsManager wmsManager;
    private final OrderWebhookService orderWebhookService;
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final WeChatNotificationService weChatNotificationService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public OrderSyncResult syncOrder(String orderIdentifier, String operatorName) {
        long startTime = System.currentTimeMillis();

        if (!StringUtils.hasText(orderIdentifier)) {
            return OrderSyncResult.failure("", "未知", "订单标识符不能为空", "请提供有效的订单号",
                    System.currentTimeMillis() - startTime);
        }

        String trimmedIdentifier = orderIdentifier.trim();
        OrderIdentifierType orderType = OrderIdentifierType.detectType(trimmedIdentifier);

        log.info("开始手动同步订单: identifier={}, type={}, operator={}",
                trimmedIdentifier, orderType.getDescription(), operatorName);

        try {
            // 根据订单类型查找1688订单ID
            String alibabaOrderId = findAlibabaOrderId(trimmedIdentifier, orderType);
            if (alibabaOrderId == null) {
                long processingTime = System.currentTimeMillis() - startTime;
                return OrderSyncResult.notFound(trimmedIdentifier, processingTime);
            }

            // 获取1688订单详情
            OrderDetailResponse.OrderDetail orderDetail = getAlibabaOrderDetail(Long.valueOf(alibabaOrderId));
            if (orderDetail == null) {
                return OrderSyncResult.failure(trimmedIdentifier, orderType.getDescription(),
                        "无法获取1688订单详情", "订单ID: " + alibabaOrderId,
                        System.currentTimeMillis() - startTime);
            }

            // 执行订单同步处理
            return performOrderSync(trimmedIdentifier, alibabaOrderId, orderDetail, orderType,
                    operatorName, startTime);

        } catch (Exception e) {
            log.error("手动同步订单异常: identifier={}, operator={}", trimmedIdentifier, operatorName, e);
            long processingTime = System.currentTimeMillis() - startTime;

            // 发送异常通知
            sendSyncNotification(operatorName, trimmedIdentifier, false,
                    "同步异常: " + e.getMessage());

            return OrderSyncResult.failure(trimmedIdentifier, orderType.getDescription(),
                    "同步处理异常: " + e.getMessage(), getExceptionDetails(e), processingTime);
        }
    }

    @Override
    public BatchOrderSyncResult batchSyncOrder(String[] orderIdentifiers, String operatorName) {
        long startTime = System.currentTimeMillis();

        if (orderIdentifiers == null || orderIdentifiers.length == 0) {
            return new BatchOrderSyncResult(0, 0, 0, new OrderSyncResult[0],
                    System.currentTimeMillis() - startTime);
        }

        log.info("开始批量手动同步订单: count={}, operator={}", orderIdentifiers.length, operatorName);

        List<CompletableFuture<OrderSyncResult>> futures = new ArrayList<>();
        for (String identifier : orderIdentifiers) {
            if (StringUtils.hasText(identifier)) {
                CompletableFuture<OrderSyncResult> future = CompletableFuture.supplyAsync(
                        () -> syncOrder(identifier.trim(), operatorName), threadPoolTaskExecutor);
                futures.add(future);
            }
        }

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        try {
            allFutures.get(5, TimeUnit.MINUTES); // 最多等待5分钟
        } catch (Exception e) {
            log.error("批量同步订单超时或异常: operator={}", operatorName, e);
        }

        // 收集结果
        List<OrderSyncResult> results = new ArrayList<>();
        int successCount = 0;

        for (CompletableFuture<OrderSyncResult> future : futures) {
            try {
                OrderSyncResult result = future.get();
                results.add(result);
                if (result.success()) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("获取批量同步结果异常", e);
                results.add(OrderSyncResult.failure("未知", "未知", "获取结果异常", e.getMessage(), 0));
            }
        }

        long totalProcessingTime = System.currentTimeMillis() - startTime;
        int totalCount = results.size();
        int failureCount = totalCount - successCount;

        BatchOrderSyncResult batchResult = new BatchOrderSyncResult(totalCount, successCount, failureCount,
                results.toArray(new OrderSyncResult[0]), totalProcessingTime);

        // 发送批量同步通知
        sendBatchSyncNotification(operatorName, batchResult);

        log.info("批量手动同步订单完成: {}", batchResult.getSummary());
        return batchResult;
    }

    @Override
    public OrderStatusInfo checkOrderStatus(String orderIdentifier) {
        if (!StringUtils.hasText(orderIdentifier)) {
            return OrderStatusInfo.notFound("", "订单标识符不能为空");
        }

        String trimmedIdentifier = orderIdentifier.trim();
        OrderIdentifierType orderType = OrderIdentifierType.detectType(trimmedIdentifier);

        try {
            // 查找1688订单ID
            String alibabaOrderId = findAlibabaOrderId(trimmedIdentifier, orderType);
            if (alibabaOrderId == null) {
                return OrderStatusInfo.notFound(trimmedIdentifier, "未找到匹配的订单记录");
            }

            // 获取1688订单详情
            OrderDetailResponse.OrderDetail orderDetail = getAlibabaOrderDetail(Long.valueOf(alibabaOrderId));
            if (orderDetail == null) {
                return OrderStatusInfo.notFound(trimmedIdentifier, "无法获取1688订单详情");
            }

            // 构建状态信息
            String currentStatus = orderDetail.getBaseInfo().getStatus();
            OrderStatusEnums statusEnum = OrderStatusEnums.fromCode(currentStatus);
            String statusDescription = statusEnum != null ? statusEnum.getDesc() : "未知状态";
            String lastUpdateTime = orderDetail.getBaseInfo().getModifyTime() != null
                    ? orderDetail.getBaseInfo().getModifyTime().format(FORMATTER)
                    : "未知";

            // 检查是否需要同步（简单逻辑：检查本地是否有对应记录）
            boolean needsSync = checkIfNeedsSync(alibabaOrderId);

            return OrderStatusInfo.found(trimmedIdentifier, alibabaOrderId, orderType.getDescription(),
                    currentStatus, statusDescription, lastUpdateTime, "正常", needsSync);

        } catch (Exception e) {
            log.error("检查订单状态异常: identifier={}", trimmedIdentifier, e);
            return OrderStatusInfo.notFound(trimmedIdentifier, "检查状态异常: " + e.getMessage());
        }
    }

    /**
     * 根据订单标识符和类型查找1688订单ID
     */
    private String findAlibabaOrderId(String identifier, OrderIdentifierType type) {
        try {
            switch (type) {
                case ALIBABA_ORDER_ID:
                    return identifier; // 直接是1688订单ID

                case NAYA_PURCHASE_ORDER:
                    return findAlibabaOrderIdByNayaPurchaseOrder(identifier);

                case WMS_PURCHASE_ORDER:
                    return findAlibabaOrderIdByWmsPurchaseOrder(identifier);

                default:
                    // 尝试各种方式查找
                    String orderId = findAlibabaOrderIdByNayaPurchaseOrder(identifier);
                    if (orderId == null) {
                        orderId = findAlibabaOrderIdByWmsPurchaseOrder(identifier);
                    }
                    return orderId;
            }
        } catch (Exception e) {
            log.error("查找1688订单ID异常: identifier={}, type={}", identifier, type, e);
            return null;
        }
    }

    /**
     * 通过Naya采购单号查找1688订单ID
     */
    private String findAlibabaOrderIdByNayaPurchaseOrder(String nayaPurchaseOrder) {
        // 查找采购订单表
        LambdaQueryWrapper<TzOrderPurchase> purchaseWrapper = Wrappers.<TzOrderPurchase>lambdaQuery()
                .eq(TzOrderPurchase::getPurchaseOrderNo, nayaPurchaseOrder)
                .last("LIMIT 1");

        TzOrderPurchase purchase = orderPurchaseMapper.selectOne(purchaseWrapper);
        if (purchase != null) {
            // 通过采购订单ID查找供应商订单
            LambdaQueryWrapper<TzOrderSupplier> supplierWrapper = Wrappers.<TzOrderSupplier>lambdaQuery()
                    .eq(TzOrderSupplier::getPurchaseOrderId, purchase.getId())
                    .isNotNull(TzOrderSupplier::getPlatformOrderId)
                    .last("LIMIT 1");

            TzOrderSupplier supplier = orderSupplierMapper.selectOne(supplierWrapper);
            if (supplier != null && StringUtils.hasText(supplier.getPlatformOrderId())) {
                return supplier.getPlatformOrderId();
            }
        }

        // 也可能直接在供应商订单表中有记录
        LambdaQueryWrapper<TzOrderSupplier> directWrapper = Wrappers.<TzOrderSupplier>lambdaQuery()
                .eq(TzOrderSupplier::getPlatformOrderNo, nayaPurchaseOrder)
                .isNotNull(TzOrderSupplier::getPlatformOrderId)
                .last("LIMIT 1");

        TzOrderSupplier directSupplier = orderSupplierMapper.selectOne(directWrapper);
        if (directSupplier != null && StringUtils.hasText(directSupplier.getPlatformOrderId())) {
            return directSupplier.getPlatformOrderId();
        }

        return null;
    }

    /**
     * 通过WMS采购单号查找1688订单ID
     */
    private String findAlibabaOrderIdByWmsPurchaseOrder(String wmsPurchaseOrder) {
        // 先查找供应商订单表
        LambdaQueryWrapper<TzOrderSupplier> wrapper = Wrappers.<TzOrderSupplier>lambdaQuery()
                .eq(TzOrderSupplier::getWmsPurchaseOrderNo, wmsPurchaseOrder)
                .isNotNull(TzOrderSupplier::getPlatformOrderId)
                .last("LIMIT 1");

        TzOrderSupplier supplier = orderSupplierMapper.selectOne(wrapper);
        if (supplier != null && StringUtils.hasText(supplier.getPlatformOrderId())) {
            return supplier.getPlatformOrderId();
        }

        // 也可以通过WMS API查询获取更多信息
        try {
            PurchaseOrderDetailReq request = PurchaseOrderDetailReq.builder()
                    .orderId(wmsPurchaseOrder)
                    .build();
            List<WmsPurchaseOrderDetailsRes> wmsDetails = wmsManager.queryOrderDetail(request);
            if (wmsDetails != null && !wmsDetails.isEmpty()) {
                WmsPurchaseOrderDetailsRes wmsDetail = wmsDetails.get(0);
                if (wmsDetail.getOrderId() != null) {
                    return wmsDetail.getOrderId().toString();
                }
            }
        } catch (Exception e) {
            log.warn("通过WMS API查找订单ID失败: wmsPurchaseOrder={}", wmsPurchaseOrder, e);
        }

        return null;
    }

    /**
     * 获取1688订单详情
     */
    private OrderDetailResponse.OrderDetail getAlibabaOrderDetail(Long orderId) {
        try {
            OrderDetailRequestRecord request = OrderDetailRequestRecord.builder()
                    .webSite("1688")
                    .orderId(orderId)
                    .build();
            return orderManager.getOrderDetail(request);
        } catch (Exception e) {
            log.error("获取1688订单详情异常: orderId={}", orderId, e);
            return null;
        }
    }

    /**
     * 执行订单同步处理
     */
    @Transactional
    private OrderSyncResult performOrderSync(String orderIdentifier, String alibabaOrderId,
            OrderDetailResponse.OrderDetail orderDetail,
            OrderIdentifierType orderType, String operatorName,
            long startTime) {
        try {
            // 构建消息事件（模拟webhook回调）
            MessageEvent<OrderMessage> messageEvent = createSyncMessageEvent(orderDetail);
            OrderMessage orderMessage = messageEvent.getData();

            // 根据订单状态确定消息类型
            OrderMessageTypeEnums messageTypeEnum = determineMessageType(orderDetail);

            // 记录手动同步日志
            Long logId = callbackLogsRepository.createProcessingLog(
                    "手动同步: " + operatorName,
                    null,
                    messageTypeEnum.getMessageType(),
                    Long.valueOf(alibabaOrderId),
                    LocalDateTime.now());

            // 执行订单同步处理
            orderWebhookService.processOrderWebhook(orderMessage, messageEvent, messageTypeEnum);

            // 标记成功
            callbackLogsRepository.markSuccess(logId);

            long processingTime = System.currentTimeMillis() - startTime;
            String currentStatus = orderDetail.getBaseInfo().getStatus();
            OrderStatusEnums statusEnum = OrderStatusEnums.fromCode(currentStatus);
            String statusDescription = statusEnum != null ? statusEnum.getDesc() : "未知状态";

            // 发送成功通知
            sendSyncNotification(operatorName, orderIdentifier, true,
                    String.format("订单同步成功，当前状态：%s", statusDescription));

            return OrderSyncResult.success(orderIdentifier, alibabaOrderId, orderType.getDescription(),
                    currentStatus, "同步成功",
                    String.format("订单状态：%s，处理耗时：%dms", statusDescription, processingTime),
                    processingTime);

        } catch (Exception e) {
            log.error("执行订单同步处理异常: alibabaOrderId={}", alibabaOrderId, e);
            long processingTime = System.currentTimeMillis() - startTime;

            // 发送失败通知
            sendSyncNotification(operatorName, orderIdentifier, false, "同步处理异常: " + e.getMessage());

            return OrderSyncResult.failure(orderIdentifier, orderType.getDescription(),
                    "同步处理异常: " + e.getMessage(), getExceptionDetails(e), processingTime);
        }
    }

    /**
     * 创建同步消息事件
     */
    private MessageEvent<OrderMessage> createSyncMessageEvent(OrderDetailResponse.OrderDetail orderDetail) {
        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("manual-sync-" + System.currentTimeMillis());
        event.setType(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
        event.setReceivedAt(LocalDateTime.now());

        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderId(orderDetail.getBaseInfo().getId());
        orderMessage.setCurrentStatus(orderDetail.getBaseInfo().getStatus());

        event.setData(orderMessage);
        return event;
    }

    /**
     * 根据订单状态确定消息类型
     */
    private OrderMessageTypeEnums determineMessageType(OrderDetailResponse.OrderDetail orderDetail) {
        String status = orderDetail.getBaseInfo().getStatus();
        OrderStatusEnums statusEnum = OrderStatusEnums.fromCode(status);

        // 根据订单状态映射到对应的消息类型
        return switch (statusEnum) {
            case WAIT_BUYER_PAY -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE;
            case WAIT_SELLER_SEND -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PAY;
            case WAIT_BUYER_RECEIVE -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS;
            case CONFIRM_GOODS -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS;
            case SUCCESS -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_SUCCESS;
            case CANCEL, TERMINATED -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE;
            default -> OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE;
        };
    }

    /**
     * 检查是否需要同步
     */
    private boolean checkIfNeedsSync(String alibabaOrderId) {
        try {
            // 简单逻辑：检查是否存在对应的采购订单记录
            LambdaQueryWrapper<TzOrderSupplier> wrapper = Wrappers.<TzOrderSupplier>lambdaQuery()
                    .eq(TzOrderSupplier::getPlatformOrderId, alibabaOrderId)
                    .last("LIMIT 1");

            TzOrderSupplier supplier = orderSupplierMapper.selectOne(wrapper);
            return supplier == null;
        } catch (Exception e) {
            log.error("检查同步需求异常: alibabaOrderId={}", alibabaOrderId, e);
            return true; // 出现异常时认为需要同步
        }
    }

    /**
     * 发送同步通知
     */
    private void sendSyncNotification(String operatorName, String orderIdentifier,
            boolean success, String message) {
        try {
            weChatNotificationService.sendManualSyncNotification(operatorName, orderIdentifier, success, message);
        } catch (Exception e) {
            log.error("发送同步通知异常: orderIdentifier={}", orderIdentifier, e);
        }
    }

    /**
     * 发送批量同步通知
     */
    private void sendBatchSyncNotification(String operatorName, BatchOrderSyncResult result) {
        try {
            String message = String.format("批量同步完成：%s", result.getSummary());
            weChatNotificationService.sendManualSyncNotification(operatorName, "批量操作",
                    result.isAllSuccess(), message);
        } catch (Exception e) {
            log.error("发送批量同步通知异常", e);
        }
    }

    /**
     * 获取异常详情
     */
    private String getExceptionDetails(Exception e) {
        if (e.getCause() != null) {
            return String.format("异常：%s，原因：%s", e.getMessage(), e.getCause().getMessage());
        }
        return "异常：" + e.getMessage();
    }
}
