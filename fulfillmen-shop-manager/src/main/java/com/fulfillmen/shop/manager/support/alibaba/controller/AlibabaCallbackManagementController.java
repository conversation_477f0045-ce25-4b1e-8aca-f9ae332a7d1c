/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.controller;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.alibaba.service.ManualOrderSyncService;
import com.fulfillmen.shop.manager.support.alibaba.service.ManualOrderSyncService.BatchOrderSyncResult;
import com.fulfillmen.shop.manager.support.alibaba.service.ManualOrderSyncService.OrderStatusInfo;
import com.fulfillmen.shop.manager.support.alibaba.service.ManualOrderSyncService.OrderSyncResult;
import com.fulfillmen.shop.manager.support.notification.service.WeChatNotificationService;

import cn.dev33.satoken.annotation.SaCheckRole;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里巴巴回调管理控制器
 *
 * <pre>
 * 功能说明：
 * 1. 提供手动同步订单功能
 * 2. 提供回调重试管理功能
 * 3. 提供订单状态查询功能
 * 4. 提供企业微信通知测试功能
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/1/3
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin/alibaba/callback")
@RequiredArgsConstructor
@Validated
@Tag(name = "阿里巴巴回调管理", description = "阿里巴巴回调重试和手动同步管理API")
@SaCheckRole("admin")
public class AlibabaCallbackManagementController {

    private final ManualOrderSyncService manualOrderSyncService;
    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final WeChatNotificationService weChatNotificationService;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 手动同步单个订单
     */
    @PostMapping("/sync-order")
    @Operation(summary = "手动同步单个订单", description = "支持通过Naya采购单号、WMS采购单号或1688订单ID进行手动同步")
    public ResponseEntity<OrderSyncResult> syncOrder(@Valid @RequestBody SyncOrderRequest request) {

        log.info("收到手动同步订单请求: identifier={}, operator={}",
                request.orderIdentifier, request.operatorName);

        try {
            OrderSyncResult result = manualOrderSyncService.syncOrder(
                    request.orderIdentifier, request.operatorName);

            if (result.success()) {
                return ResponseEntity.ok(result);
            } else {
                return ResponseEntity.badRequest().body(result);
            }
        } catch (Exception e) {
            log.error("手动同步订单异常: identifier={}", request.orderIdentifier, e);
            return ResponseEntity.internalServerError().body(
                    OrderSyncResult.failure(request.orderIdentifier, "未知",
                            "同步订单异常: " + e.getMessage(), "", 0));
        }
    }

    /**
     * 批量手动同步订单
     */
    @PostMapping("/batch-sync-order")
    @Operation(summary = "批量手动同步订单", description = "批量同步多个订单，支持多种订单号类型混合")
    public ResponseEntity<BatchOrderSyncResult> batchSyncOrder(@Valid @RequestBody BatchSyncOrderRequest request) {

        log.info("收到批量手动同步订单请求: count={}, operator={}",
                request.orderIdentifiers.length, request.operatorName);

        try {
            BatchOrderSyncResult result = manualOrderSyncService.batchSyncOrder(
                    request.orderIdentifiers, request.operatorName);

            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量手动同步订单异常: operator={}", request.operatorName, e);
            return ResponseEntity.internalServerError().body(
                    new BatchOrderSyncResult(0, 0, 1, new OrderSyncResult[0], 0));
        }
    }

    /**
     * 检查订单状态
     */
    @GetMapping("/check-order-status")
    @Operation(summary = "检查订单状态", description = "查询订单当前状态信息，不执行同步操作")
    public ResponseEntity<OrderStatusInfo> checkOrderStatus(
            @Parameter(description = "订单标识符") @RequestParam @NotBlank @Size(min = 5, max = 50) String orderIdentifier) {

        log.info("收到检查订单状态请求: identifier={}", orderIdentifier);

        try {
            OrderStatusInfo statusInfo = manualOrderSyncService.checkOrderStatus(orderIdentifier);
            return ResponseEntity.ok(statusInfo);
        } catch (Exception e) {
            log.error("检查订单状态异常: identifier={}", orderIdentifier, e);
            return ResponseEntity.internalServerError().body(
                    OrderStatusInfo.notFound(orderIdentifier, "查询异常: " + e.getMessage()));
        }
    }

    /**
     * 查询失败的回调记录
     */
    @GetMapping("/failed-logs")
    @Operation(summary = "查询失败的回调记录", description = "查询指定时间范围内失败的回调记录")
    public ResponseEntity<List<SysAlibabaCallbackLogs>> getFailedLogs(
            @Parameter(description = "时间范围（小时）") @RequestParam(defaultValue = "24") int timeHours,
            @Parameter(description = "限制数量") @RequestParam(defaultValue = "50") int limit) {

        try {
            List<SysAlibabaCallbackLogs> failedLogs = callbackLogsRepository.findFailedLogsForRetry(
                    Integer.MAX_VALUE, timeHours, limit);
            return ResponseEntity.ok(failedLogs);
        } catch (Exception e) {
            log.error("查询失败回调记录异常", e);
            return ResponseEntity.internalServerError().body(List.of());
        }
    }

    /**
     * 获取回调统计信息
     */
    @GetMapping("/callback-stats")
    @Operation(summary = "获取回调统计信息", description = "获取指定时间范围内的回调处理统计信息")
    public ResponseEntity<Map<String, Object>> getCallbackStats(
            @Parameter(description = "时间范围（小时）") @RequestParam(defaultValue = "24") int timeHours) {

        try {
            long failedCount = callbackLogsRepository.countFailedLogs(timeHours);

            Map<String, Object> stats = Map.of(
                    "timeRange", timeHours + "小时内",
                    "failedCount", failedCount,
                    "queryTime", LocalDateTime.now().format(FORMATTER),
                    "status", failedCount > 0 ? "有失败记录" : "正常");

            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            log.error("获取回调统计信息异常", e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 测试企业微信通知
     */
    @PostMapping("/test-wechat-notification")
    @Operation(summary = "测试企业微信通知", description = "发送测试消息到企业微信群")
    public ResponseEntity<Map<String, Object>> testWeChatNotification() {
        try {
            boolean success = weChatNotificationService.testConnection();

            return ResponseEntity.ok(Map.of(
                    "success", success,
                    "message", success ? "企业微信通知测试成功" : "企业微信通知测试失败"));
        } catch (Exception e) {
            log.error("测试企业微信通知异常", e);
            return ResponseEntity.internalServerError().body(Map.of(
                    "success", false,
                    "message", "测试异常: " + e.getMessage()));
        }
    }

    /**
     * 手动触发重试任务
     */
    @PostMapping("/trigger-retry")
    @Operation(summary = "手动触发重试任务", description = "立即执行一次失败回调的重试任务")
    public ResponseEntity<Map<String, String>> triggerRetry(
            @Parameter(description = "操作员名称") @RequestParam @NotBlank String operatorName) {

        log.info("收到手动触发重试任务请求: operator={}", operatorName);

        try {
            String message = String.format("重试任务触发请求已接收，操作员：%s，时间：%s。" +
                    "请注意：实际的重试处理由定时任务执行，请查看日志确认处理结果。",
                    operatorName, LocalDateTime.now().format(FORMATTER));

            // 发送通知
            weChatNotificationService.sendSystemErrorNotification(
                    "手动触发重试任务",
                    String.format("操作员 %s 在 %s 手动触发了回调重试任务",
                            operatorName, LocalDateTime.now().format(FORMATTER)));

            return ResponseEntity.ok(Map.of("message", message));
        } catch (Exception e) {
            log.error("手动触发重试任务异常: operator={}", operatorName, e);
            return ResponseEntity.internalServerError().body(Map.of("error", e.getMessage()));
        }
    }

    /**
     * 单个订单同步请求
     */
    public record SyncOrderRequest(
            @NotBlank(message = "订单标识符不能为空") @Size(min = 5, max = 50, message = "订单标识符长度必须在5-50个字符之间") String orderIdentifier,

            @NotBlank(message = "操作员名称不能为空") @Size(max = 20, message = "操作员名称长度不能超过20个字符") String operatorName) {
    }

    /**
     * 批量订单同步请求
     */
    public record BatchSyncOrderRequest(
            @NotEmpty(message = "订单标识符列表不能为空") @Size(min = 1, max = 50, message = "批量同步订单数量必须在1-50个之间") String[] orderIdentifiers,

            @NotBlank(message = "操作员名称不能为空") @Size(max = 20, message = "操作员名称长度不能超过20个字符") String operatorName) {
    }
}
