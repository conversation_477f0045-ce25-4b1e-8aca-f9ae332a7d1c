/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.notification.service;

/**
 * 企业微信消息通知服务
 *
 * <AUTHOR>
 * @date 2025/1/3
 * @description 企业微信群机器人消息推送服务
 * @since 1.0.0
 */
public interface WeChatNotificationService {

    /**
     * 发送阿里巴巴回调重试失败通知
     *
     * @param orderId        订单ID
     * @param eventType      事件类型
     * @param retryCount     重试次数
     * @param failedMessage  失败信息
     * @param lastFailedTime 最后失败时间
     */
    void sendCallbackRetryFailedNotification(Long orderId, String eventType, int retryCount,
            String failedMessage, String lastFailedTime);

    /**
     * 发送手动同步订单结果通知
     *
     * @param operatorName 操作员名称
     * @param orderId      订单ID
     * @param syncResult   同步结果
     * @param message      消息内容
     */
    void sendManualSyncNotification(String operatorName, String orderId, boolean syncResult, String message);

    /**
     * 发送系统异常通知
     *
     * @param title   标题
     * @param content 内容
     */
    void sendSystemErrorNotification(String title, String content);

    /**
     * 测试企业微信连接
     *
     * @return 是否连接成功
     */
    boolean testConnection();
}
