/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import java.util.List;

import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderItem;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.response.model.order.TradeBaseInfo;
import com.fulfillmen.support.alibaba.api.response.order.OrderDetailResponse.OrderDetail;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单改价事件处理器
 * <p>
 * 策略实现类，专门处理 {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY}
 * 事件。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderPriceModificationProcessor implements OrderEventProcessor {

    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final IWmsManager wmsManager;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单改价事件: orderId={}", orderId);

        try {
            updateLocalOrderPriceInformation(context);
            syncPriceChangeToWms(context);

            log.info("订单改价事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单改价事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单改价事件失败: orderId=" + orderId, e);
        }
    }

    /**
     * 更新本地订单价格信息
     */
    private void updateLocalOrderPriceInformation(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.debug("更新本地订单价格信息: orderId={}", orderId);

        TzOrderSupplier supplier = context.getTzOrderSupplierByOrderId(orderId);
        TzOrderPurchase purchase = context.tzOrderPurchase();
        List<TzOrderItem> orderItems = context.getOrderItemsBySupplierOrderId(supplier.getId());

        // 重新同步商品信息
        context.resyncTzOrderItem(orderId);
        // 更新供应商订单信息 修改重新计算最终实付价格
        context.recalculateActualInformationToSupplierOrder(orderId);
        // 重新计算采购单的实付金额
        context.recalculateActualInformationToPurchaseOrder();

        // 更新数据库
        transactionTemplate.executeWithoutResult(transactionStatus -> {
            try {
                tzOrderSupplierMapper.updateById(supplier);
                tzOrderPurchaseMapper.updateById(purchase);
                tzOrderItemMapper.updateBatchById(orderItems);
            } catch (Exception e) {
                log.error("更新价格信息到数据库时失败: purchaseNo={}, error={}", purchase.getPurchaseOrderNo(),
                    e.getMessage(), e);
                transactionStatus.setRollbackOnly();
                throw e;
            }
        });
    }

    /**
     * 同步价格变更到WMS
     */
    private void syncPriceChangeToWms(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.debug("同步价格变更到WMS: orderId={}", orderId);

        OrderDetail alibabaOrderDetail = context.alibabaOrderDetail();
        TradeBaseInfo baseInfo = alibabaOrderDetail.getBaseInfo();
        WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetailRes = context.getWmsPurchaseOrderDetail();

        WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
            .purchaseNo(wmsPurchaseOrderDetailRes.getPurchaseNo())
            .paymentTime(baseInfo.getPayTime())
            .orderId(context.getAlibabaOrderId())
            .alibabaFinalAmount(baseInfo.getTotalAmount())
            .finalShoppingFee(baseInfo.getShippingFee())
            .build();

        // 重新同步商品信息
        List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsReqs = context.resyncWmsPurchaseOrderDetailsReqList();
        wmsPurchaseOrderDetailReq.setOrderDetails(wmsPurchaseOrderDetailsReqs);

        // 更新WMS采购订单
        this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReq);
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY);
    }
}
