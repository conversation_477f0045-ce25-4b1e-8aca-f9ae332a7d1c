/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.order.event;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fulfillmen.shop.common.context.OrderContextDTO;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * 取消采购订单同步 WMS 处理器
 *
 * <AUTHOR>
 * @date 2025/7/22
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CancelPurchaseOrderSyncWmsHandler {

    private final IWmsManager wmsManager;

    public void handle(OrderContextDTO context) {
        log.info("开始取消 wms 订单，采购订单号: {}", context.getPurchaseOrderNo());
        try {
            syncCancelWmsOrders(context);
            log.info("取消 wms 订单完成 : [{}] ", context.getPurchaseOrderNo());
        } catch (Exception e) {
            log.error("取消 wms 订单失败 : [{}] ", context.getPurchaseOrderNo(), e);
        }
    }

    private void syncCancelWmsOrders(OrderContextDTO context) {
        log.info("开始取消 wms 订单，采购订单号: {}", context.getPurchaseOrderNo());
        String purchaseOrderNo = context.getPurchaseOrder().getPurchaseOrderNo();
        List<TzOrderSupplier> wmsOrders = context.getSupplierOrders();
        if (CollectionUtils.isEmpty(wmsOrders)) {
            log.info("没有需要取消的 wms 订单，采购订单号: {}", purchaseOrderNo);
            return;
        }

        // 获取当前用户的 WMS 账户信息
        String cusCode = UserContextHolder.getWmsCusCodeOrTenantCusCode();

        if (!StringUtils.hasText(cusCode)) {
            log.error("无法获取 cusCode , 找不到 WMS 账户信息，无法取消 Wms 采购订单。请检查一下配置信息。");
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.UNABLE_RETRIEVE_WMS_ACCOUNT);
        }

        // 调用 WMS API 取消订单 取消失败列表
        wmsManager.cancelWmsPurchaseOrderByNayaPurchaseNo(purchaseOrderNo, cusCode);
    }
}
