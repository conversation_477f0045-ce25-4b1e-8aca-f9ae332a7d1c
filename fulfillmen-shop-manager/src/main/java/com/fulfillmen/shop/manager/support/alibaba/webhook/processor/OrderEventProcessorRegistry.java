/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;

import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单事件处理器注册表
 * <p>
 * 该类负责在应用启动时自动发现并注册所有的 {@link OrderEventProcessor} 实现。
 * 它使用策略模式，根据订单消息的类型，将请求动态分发给正确的处理器。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderEventProcessorRegistry {

    private final List<OrderEventProcessor> processors;
    private final Map<OrderMessageTypeEnums, OrderEventProcessor> registry = new EnumMap<>(OrderMessageTypeEnums.class);

    /**
     * 在依赖注入完成后，初始化注册表。
     * <p>
     * 遍历所有找到的处理器实例，将它们根据各自支持的消息类型注册到 {@code registry} 中。
     * 如果发现有多种处理器尝试注册同一个消息类型，会记录一个错误日志，这是为了防止意外的逻辑覆盖。
     * </p>
     */
    @PostConstruct
    public void initialize() {
        log.info("开始注册订单事件处理器...");
        for (OrderEventProcessor processor : processors) {
            for (OrderMessageTypeEnums type : processor.supports()) {
                if (registry.containsKey(type)) {
                    log.error("订单事件处理器冲突！消息类型 '{}' 被多个处理器注册: [{}] 和 [{}]",
                        type, registry.get(type).getClass().getSimpleName(), processor.getClass().getSimpleName());
                }
                registry.put(type, processor);
                log.info("已注册处理器: [{}] 支持消息类型 -> {}", processor.getClass().getSimpleName(), type);
            }
        }
        log.info("订单事件处理器注册完成，共注册 {} 个处理器，支持 {} 种消息类型。", processors.size(), registry.size());
    }

    /**
     * 根据消息类型查找对应的处理器。
     *
     * @param messageType 阿里巴巴订单消息的类型。
     * @return 一个包含处理器的 {@link Optional}，如果找不到则为空。
     */
    public Optional<OrderEventProcessor> getProcessor(OrderMessageTypeEnums messageType) {
        return Optional.ofNullable(registry.get(messageType));
    }
}
