/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.core.repository;

import com.baomidou.mybatisplus.extension.repository.IRepository;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/8 09:36
 * @description: todo
 * @since 1.0.0
 */
public interface SysAlibabaCallbackLogsRepository extends IRepository<SysAlibabaCallbackLogs> {

    /**
     * 创建 PROCESSING 日志并返回日志ID
     *
     * @param message   原始消息体
     * @param signature 签名
     * @return 日志ID（失败返回 null）
     */
    Long createProcessingLog(String message, String signature);

    /**
     * 创建 PROCESSING 日志并返回日志ID（包含事件与订单信息）
     *
     * @param message    原始消息体（可为 null）
     * @param signature  签名（可为 null）
     * @param eventType  事件类型（可为 null）
     * @param orderId    订单ID（可为 null）
     * @param receivedAt 接收时间（为空则使用当前时间）
     * @return 日志ID（失败返回 null）
     */
    Long createProcessingLog(String message, String signature, String eventType, Long orderId,
      LocalDateTime receivedAt);

    /**
     * 异步标记成功
     *
     * @param logId 日志ID
     */
    void markSuccess(Long logId);

    /**
     * 异步标记失败
     *
     * @param logId  日志ID
     * @param reason 失败原因
     */
    void markFailed(Long logId, String reason);

    /**
     * 查询需要重试的失败记录
     *
     * @param maxRetryCount 最大重试次数
     * @param timeHours     时间范围（小时）
     * @param limit         限制数量
     * @return 失败记录列表
     */
    List<SysAlibabaCallbackLogs> findFailedLogsForRetry(int maxRetryCount, int timeHours, int limit);

    /**
     * 更新重试次数并标记为处理中
     *
     * @param logId      日志ID
     * @param retryCount 新的重试次数
     */
    void updateRetryCount(Long logId, int retryCount);

    /**
     * 根据订单ID查找最新的回调记录
     *
     * @param orderId 订单ID
     * @return 回调记录
     */
    SysAlibabaCallbackLogs findLatestByOrderId(Long orderId);

    /**
     * 统计失败记录数量
     *
     * @param timeHours 时间范围（小时）
     * @return 失败记录数量
     */
    long countFailedLogs(int timeHours);
}
