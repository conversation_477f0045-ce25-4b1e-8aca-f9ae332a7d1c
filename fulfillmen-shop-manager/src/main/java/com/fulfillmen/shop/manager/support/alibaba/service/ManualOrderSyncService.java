/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.service;

/**
 * 手动订单同步服务
 *
 * <pre>
 * 功能说明：
 * 1. 支持通过多种订单号进行手动同步：
 *    - Naya采购单号（以C开头的订单号）
 *    - WMS采购单号
 *    - 1688订单ID
 * 2. 以1688订单为基准，同步订单状态和数据
 * 3. 提供同步结果和详细信息
 * 4. 记录同步操作日志
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/09 12:10
 * @since 1.0.0
 */
public interface ManualOrderSyncService {

    /**
     * 手动同步订单
     *
     * @param orderIdentifier 订单标识符（可以是Naya采购单号、WMS采购单号或1688订单ID）
     * @param operatorName    操作员名称
     * @return 同步结果
     */
    OrderSyncResult syncOrder(String orderIdentifier, String operatorName);

    /**
     * 批量手动同步订单
     *
     * @param orderIdentifiers 订单标识符列表
     * @param operatorName     操作员名称
     * @return 批量同步结果
     */
    BatchOrderSyncResult batchSyncOrder(String[] orderIdentifiers, String operatorName);

    /**
     * 检查订单当前状态
     *
     * @param orderIdentifier 订单标识符
     * @return 订单状态信息
     */
    OrderStatusInfo checkOrderStatus(String orderIdentifier);

    /**
     * 订单同步结果
     */
    record OrderSyncResult(
            boolean success,
            String orderIdentifier,
            String alibabaOrderId,
            String orderType,
            String currentStatus,
            String syncStatus,
            String message,
            String details,
            long processingTimeMs) {

        /**
         * 创建成功结果
         */
        public static OrderSyncResult success(String orderIdentifier, String alibabaOrderId,
                String orderType, String currentStatus,
                String syncStatus, String details, long processingTimeMs) {
            return new OrderSyncResult(true, orderIdentifier, alibabaOrderId, orderType,
                    currentStatus, syncStatus, "同步成功", details, processingTimeMs);
        }

        /**
         * 创建失败结果
         */
        public static OrderSyncResult failure(String orderIdentifier, String orderType,
                String message, String details, long processingTimeMs) {
            return new OrderSyncResult(false, orderIdentifier, null, orderType,
                    null, "同步失败", message, details, processingTimeMs);
        }

        /**
         * 创建未找到结果
         */
        public static OrderSyncResult notFound(String orderIdentifier, long processingTimeMs) {
            return new OrderSyncResult(false, orderIdentifier, null, "未知",
                    null, "订单不存在", "未找到匹配的订单记录", "请检查订单号是否正确", processingTimeMs);
        }
    }

    /**
     * 批量订单同步结果
     */
    record BatchOrderSyncResult(
            int totalCount,
            int successCount,
            int failureCount,
            OrderSyncResult[] results,
            long totalProcessingTimeMs) {

        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            return totalCount > 0 ? (double) successCount / totalCount : 0.0;
        }

        /**
         * 是否全部成功
         */
        public boolean isAllSuccess() {
            return successCount == totalCount;
        }

        /**
         * 获取汇总信息
         */
        public String getSummary() {
            return String.format("总计: %d, 成功: %d, 失败: %d, 成功率: %.1f%%, 耗时: %dms",
                    totalCount, successCount, failureCount, getSuccessRate() * 100, totalProcessingTimeMs);
        }
    }

    /**
     * 订单状态信息
     */
    record OrderStatusInfo(
            boolean found,
            String orderIdentifier,
            String alibabaOrderId,
            String orderType,
            String currentStatus,
            String statusDescription,
            String lastUpdateTime,
            String syncStatus,
            boolean needsSync,
            String message) {

        /**
         * 创建找到的状态信息
         */
        public static OrderStatusInfo found(String orderIdentifier, String alibabaOrderId,
                String orderType, String currentStatus,
                String statusDescription, String lastUpdateTime,
                String syncStatus, boolean needsSync) {
            return new OrderStatusInfo(true, orderIdentifier, alibabaOrderId, orderType,
                    currentStatus, statusDescription, lastUpdateTime, syncStatus, needsSync, "查询成功");
        }

        /**
         * 创建未找到的状态信息
         */
        public static OrderStatusInfo notFound(String orderIdentifier, String message) {
            return new OrderStatusInfo(false, orderIdentifier, null, "未知",
                    null, null, null, "未找到", false, message);
        }
    }

    /**
     * 订单类型枚举
     */
    enum OrderIdentifierType {
        /**
         * 1688订单ID
         */
        ALIBABA_ORDER_ID("1688订单ID"),

        /**
         * Naya采购单号（以N开头）
         */
        NAYA_PURCHASE_ORDER("Naya采购单号"),

        /**
         * WMS采购单号
         */
        WMS_PURCHASE_ORDER("WMS采购单号"),

        /**
         * 未知类型
         */
        UNKNOWN("未知类型");

        private final String description;

        OrderIdentifierType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }

        /**
         * 根据订单标识符判断类型
         */
        public static OrderIdentifierType detectType(String orderIdentifier) {
            if (orderIdentifier == null || orderIdentifier.trim().isEmpty()) {
                return UNKNOWN;
            }

            String trimmed = orderIdentifier.trim();

            // Naya采购单号以C开头且长度大于10
            if (trimmed.startsWith("N") && trimmed.length() > 10) {
                return NAYA_PURCHASE_ORDER;
            }

            // 纯数字且长度较长的可能是1688订单ID
            if (trimmed.matches("\\d+") && trimmed.length() > 10) {
                return ALIBABA_ORDER_ID;
            }

            // 其他情况认为是WMS采购单号
            if (trimmed.length() > 5) {
                return WMS_PURCHASE_ORDER;
            }

            return UNKNOWN;
        }
    }
}
