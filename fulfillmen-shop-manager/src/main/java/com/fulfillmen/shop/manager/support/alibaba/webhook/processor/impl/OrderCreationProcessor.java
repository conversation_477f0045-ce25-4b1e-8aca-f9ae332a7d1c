/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import com.fulfillmen.shop.dao.mapper.TzOrderItemMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderPurchaseMapper;
import com.fulfillmen.shop.dao.mapper.TzOrderSupplierMapper;
import com.fulfillmen.shop.domain.entity.TzOrderPurchase;
import com.fulfillmen.shop.domain.entity.TzOrderSupplier;
import com.fulfillmen.shop.manager.support.alibaba.IPayManager;
import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.support.alibaba.api.request.pay.AlipayUrlRequestRecord;
import com.fulfillmen.support.alibaba.api.request.pay.PayWayQueryRequestRecord;
import com.fulfillmen.support.alibaba.api.response.model.PayTypeInfo;
import com.fulfillmen.support.alibaba.api.response.pay.AlipayUrlResponse;
import com.fulfillmen.support.alibaba.api.response.pay.PayWayQueryResponse;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailReq;
import com.fulfillmen.support.wms.dto.request.WmsPurchaseOrderDetailsReq;
import com.fulfillmen.support.wms.dto.response.WmsPurchaseOrderDetailsRes;
import com.fulfillmen.support.wms.exception.WmsApiException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * 订单创建事件处理器
 * <p>
 * 策略实现类，专门处理 {@link OrderMessageTypeEnums#ORDER_BUYER_VIEW_BUYER_MAKE} 事件。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderCreationProcessor implements OrderEventProcessor {

    private final IPayManager payManager;
    private final IWmsManager wmsManager;
    private final TzOrderSupplierMapper tzOrderSupplierMapper;
    private final TzOrderPurchaseMapper tzOrderPurchaseMapper;
    private final TzOrderItemMapper tzOrderItemMapper;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final TransactionTemplate transactionTemplate;

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单创建事件: orderId={}", orderId);

        try {
            WmsPurchaseOrderDetailsRes wmsPurchaseOrderDetail = context.getWmsPurchaseOrderDetail();
            WmsPurchaseOrderDetailReq wmsPurchaseOrderDetailReq = WmsPurchaseOrderDetailReq.builder()
                .purchaseNo(wmsPurchaseOrderDetail.getPurchaseNo())
                .orderId(Long.valueOf(orderId))
                .build();

            TzOrderSupplier orderSupplier = context.getTzOrderSupplierByOrderId(orderId);
            TzOrderPurchase purchase = context.tzOrderPurchase();

            CompletableFuture<Void> alipayUrlFuture = CompletableFuture.runAsync(() -> {
                AlipayUrlResponse alipayUrl = this.payManager.getAlipayUrl(
                    AlipayUrlRequestRecord.builder()
                        .orderIdList(Collections.singletonList(context.getAlibabaOrderId())).build());
                wmsPurchaseOrderDetailReq.setPayUrl(alipayUrl.getPayUrl());
                orderSupplier.setPlatformPayUrl(wmsPurchaseOrderDetailReq.getPayUrl());
            }, threadPoolTaskExecutor);

            CompletableFuture<Void> payWayQueryFuture = CompletableFuture.runAsync(() -> {
                PayWayQueryResponse response = this.payManager
                    .getPayWayQuery(PayWayQueryRequestRecord.of(context.getAlibabaOrderId()));
                String payTypeDesc = response.getResultList().getChannels().stream().map(PayTypeInfo::getName)
                    .collect(Collectors.joining(","));
                String payType = response.getResultList().getChannels().stream().map(PayTypeInfo::getCode)
                    .map(String::valueOf).collect(Collectors.joining(","));
                wmsPurchaseOrderDetailReq.setPlatformPayType(payTypeDesc);
                orderSupplier.setPlatformTradeType(payType);
                orderSupplier.setPlatformTradeTypeDesc(payTypeDesc);
            }, threadPoolTaskExecutor);

            CompletableFuture.allOf(alipayUrlFuture, payWayQueryFuture).join();

            // 计算供应商实付信息
            context.recalculateActualInformationToSupplierOrder(orderId);
            // 重新计算供应商订单的应付信息
            context.recalculatePayableInformationToSupplierOrder(orderId);
            // 重新计算采购单的应付信息
            context.recalculatePayableInformationToPurchaseOrder();

            transactionTemplate.executeWithoutResult(transactionStatus -> {
                try {
                    context.resyncTzOrderItem(orderId);
                    this.tzOrderSupplierMapper.updateById(orderSupplier);
                    this.tzOrderPurchaseMapper.updateById(purchase);
                    this.tzOrderItemMapper.updateBatchById(context.tzOrderItems());
                } catch (Exception ex) {
                    log.error("更新供应商和采购单数据库失败: orderId={}, error={}", orderId, ex.getMessage(), ex);
                    transactionStatus.setRollbackOnly();
                    throw ex; // re-throw to be caught by outer catch block
                }
            });

            List<WmsPurchaseOrderDetailsReq> wmsPurchaseOrderDetailsRequestList = context
                .resyncWmsPurchaseOrderDetailsReqList();
            wmsPurchaseOrderDetailReq.setOrderDetails(wmsPurchaseOrderDetailsRequestList);
            wmsPurchaseOrderDetailReq.setProductFinalTotalAmount(orderSupplier.getPayableGoodsAmount());
            wmsPurchaseOrderDetailReq.setAlibabaTotalAmount(orderSupplier.getPayableAmountTotal());
            wmsPurchaseOrderDetailReq.setOriginalShippingFee(orderSupplier.getPayableFreightAmount());
            wmsPurchaseOrderDetailReq.setCouponDiscount(orderSupplier.getPayableCouponAmount());
            wmsPurchaseOrderDetailReq.setDiscount(orderSupplier.getPayableDiscountAmount());
            wmsPurchaseOrderDetailReq.setPlusDiscount(orderSupplier.getPayablePlusDiscountAmount());
            wmsPurchaseOrderDetailReq.setFinalShoppingFee(orderSupplier.getActualPaymentFreightAmount());
            // 更新 wms 采购单
            this.wmsManager.updateWmsPurchaseOrder(wmsPurchaseOrderDetailReq);

            log.info("订单创建事件处理完成: orderId={}", orderId);
        } catch (WmsApiException e) {
            log.error("WMS API在处理订单创建时失败: orderId={}, error={}", orderId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("处理订单创建事件时发生未知异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单创建事件失败: orderId=" + orderId, e);
        }
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(OrderMessageTypeEnums.ORDER_BUYER_VIEW_BUYER_MAKE);
    }
}
