/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor;

import java.util.List;

import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;

/**
 * 订单事件处理器策略接口
 * <p>
 * 定义了处理特定类型阿里巴巴订单消息的统一契约。
 * 每个实现类负责一种或多种相关的订单事件。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
public interface OrderEventProcessor {

    /**
     * 处理具体的订单事件。
     * <p>
     * 这是策略的核心方法，包含了特定事件的所有业务逻辑。
     * </p>
     *
     * @param context 包含所有相关数据的订单处理上下文，避免了方法参数的冗长。
     */
    void process(OrderContextRecord context);

    /**
     * 声明此处理器支持的消息类型。
     * <p>
     * 用于在启动时注册处理器，并由 {@link OrderEventProcessorRegistry} 用来分发消息。
     * </p>
     *
     * @return 该处理器能够处理的消息类型列表。
     */
    List<OrderMessageTypeEnums> supports();
}
