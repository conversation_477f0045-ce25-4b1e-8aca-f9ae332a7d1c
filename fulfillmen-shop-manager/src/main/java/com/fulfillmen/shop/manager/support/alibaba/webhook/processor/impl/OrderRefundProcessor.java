/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.manager.support.alibaba.webhook.processor.impl;

import java.util.List;

import org.springframework.stereotype.Component;

import com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderContextRecord;
import com.fulfillmen.shop.manager.support.alibaba.webhook.processor.OrderEventProcessor;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.google.common.collect.ImmutableList;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单退款事件处理器
 * <p>
 * 策略实现类，专门处理售中和售后退款事件。
 * </p>
 *
 * <AUTHOR>
 * @date 2025/08/08
 * @version 1.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderRefundProcessor implements OrderEventProcessor {

    // 定义支持的退款操作类型，使用静态不可变列表以提高性能和安全性
    private static final List<String> SUPPORTED_REFUND_ACTIONS = ImmutableList.of(
        "SELLER_AGREE_REFUND",
        "SYSTEM_AGREE_REFUND_PROTOCOL",
        "SYSTEM_AGREE_REFUND",
        "SELLER_AGREE_REFUND_PROCOTOL");

    @Override
    public void process(OrderContextRecord context) {
        String orderId = context.getAlibabaOrderIdStr();
        log.info("开始处理订单退款事件: orderId={}", orderId);

        try {
            // FIXME: 实现完整的退款业务逻辑
            // 1. 检查WMS订单状态，如果已发货，可能需要创建退货入库单
            // 2. 更新本地订单项状态为"退款中"或"已退款"
            // 3. 更新采购单和供应商订单的已退款金额
            // 4. 通知财务系统进行账务处理
            // 注意：refundAction 需要在调用层面进行检查

            log.warn("订单退款逻辑尚未完全实现: orderId={}", orderId);

            log.info("订单退款事件处理完成: orderId={}", orderId);
        } catch (Exception e) {
            log.error("处理订单退款事件时发生异常: orderId={}, error={}", orderId, e.getMessage(), e);
            throw new RuntimeException("处理订单退款事件失败: orderId=" + orderId, e);
        }
    }

    /**
     * 前置条件检查：确认退款操作类型是否是我们需要处理的。
     * 注意：此方法预留给未来的退款逻辑完善时使用。
     */
    @SuppressWarnings("unused")
    private boolean isRefundProcessable(String refundAction) {
        if (refundAction == null || refundAction.trim().isEmpty()) {
            return false;
        }
        return SUPPORTED_REFUND_ACTIONS.contains(refundAction);
    }

    @Override
    public List<OrderMessageTypeEnums> supports() {
        return List.of(
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES,
            OrderMessageTypeEnums.ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES);
    }
}
