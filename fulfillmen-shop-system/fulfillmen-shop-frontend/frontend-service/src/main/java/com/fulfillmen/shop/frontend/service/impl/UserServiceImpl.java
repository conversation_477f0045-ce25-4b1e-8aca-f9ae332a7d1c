/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.frontend.service.impl;

import static com.fulfillmen.shop.common.properties.CommonConstants.ACTIVATION_KEY_PREFIX;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.extra.servlet.JakartaServletUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fulfillmen.shop.common.context.UserContext;
import com.fulfillmen.shop.common.context.UserContextHolder;
import com.fulfillmen.shop.common.enums.FulfillmenErrorCodeEnum;
import com.fulfillmen.shop.common.enums.FulfillmenValidationCodeEnum;
import com.fulfillmen.shop.common.exception.BusinessExceptionI18n;
import com.fulfillmen.shop.dao.mapper.TzUserAddressMapper;
import com.fulfillmen.shop.dao.mapper.TzUserMapper;
import com.fulfillmen.shop.domain.convert.TzUserAddressMapping;
import com.fulfillmen.shop.domain.convert.TzUserMapping;
import com.fulfillmen.shop.domain.entity.TzUser;
import com.fulfillmen.shop.domain.entity.TzUserAddress;
import com.fulfillmen.shop.domain.entity.enums.UserStatusEnum;
import com.fulfillmen.shop.domain.entity.enums.UserTypeEnum;
import com.fulfillmen.shop.domain.req.UserAddressReq;
import com.fulfillmen.shop.domain.req.UserAuthReq;
import com.fulfillmen.shop.domain.req.UserAuthReq.UserLoginDTO;
import com.fulfillmen.shop.domain.req.UserUpdateRep;
import com.fulfillmen.shop.domain.res.UserAddressRes;
import com.fulfillmen.shop.domain.res.UserInfoRes;
import com.fulfillmen.shop.domain.res.UserLoginRes;
import com.fulfillmen.shop.frontend.convert.FrontendUserConvert;
import com.fulfillmen.shop.frontend.service.IUserService;
import com.fulfillmen.shop.manager.core.common.ICaptchaManager;
import com.fulfillmen.shop.manager.event.UserEvent;
import com.fulfillmen.shop.manager.event.UserEventEnum;
import com.fulfillmen.shop.manager.support.wms.IWmsManager;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import com.fulfillmen.starter.core.exception.BusinessException;
import com.fulfillmen.starter.core.validation.ValidationUtils;
import com.fulfillmen.starter.web.util.SpringWebUtils;
import com.fulfillmen.support.wms.dto.response.WmsAccountInfoRes;
import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.ahoo.cosid.IdGenerator;
import me.ahoo.cosid.provider.DefaultIdGeneratorProvider;
import org.apache.commons.lang3.RandomStringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

/**
 * 用户注册
 *
 * <AUTHOR>
 * @date 2025/4/22 17:47
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl implements IUserService {

    private final TzUserMapper tzUserMapper;
    private final ICaptchaManager captchaManager;
    private final IWmsManager wmsManager;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final MessageSource messageSource;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;
    private final TzUserAddressMapper tzUserAddressMapper;

    @Override
    public void registry(UserAuthReq.UserRegistryDTO userRegistryDTO, HttpServletRequest request) {
        log.info("开始处理用户注册请求: {}", userRegistryDTO.getUsername());
        // 校验验证码
        ValidationUtils.throwIf(!captchaManager.validateCaptcha(userRegistryDTO.getCaptchaCode(), userRegistryDTO
          .getCaptchaId()), "captcha is incorrect");
        // 1. 校验邮箱和用户名是否已存在
        LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUser::getUsername, userRegistryDTO.getUsername())
          .or()
          .eq(TzUser::getEmail, userRegistryDTO.getEmail());
        var user = tzUserMapper.selectOne(queryWrapper);
        if (user != null) {
            var usernameIsExist = user.getUsername().equals(userRegistryDTO.getUsername());
            var emailIsExist = user.getEmail().equals(userRegistryDTO.getEmail());
            ValidationUtils.throwIf(usernameIsExist, "[{}] username is exist", userRegistryDTO.getUsername());
            ValidationUtils.throwIf(emailIsExist, "[{}] email is exist", userRegistryDTO.getEmail());
        }
        // 2. 生成密码盐值
        String passwordSalt = RandomStringUtils.randomAlphanumeric(16);
        // 3. 密码加密 (使用盐值加密更安全)
        String password = DigestUtils.md5DigestAsHex((userRegistryDTO.getPassword() + passwordSalt).getBytes());
        // 转换为DO
        IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
        TzUser tzUser = TzUserMapping.INSTANCE.toDO(userRegistryDTO);
        tzUser.setId(idGenerator.generate());
        tzUser.setPassword(password);
        tzUser.setPasswordSalt(passwordSalt);
        tzUser.setRegisterTime(LocalDateTime.now());
        tzUser.setRegisterIp(JakartaServletUtil.getClientIP(request));
        // 插入用户
        boolean success = this.tzUserMapper.insert(tzUser) > 0;
        if (success) {
            // 发送账户激活邮件
            applicationEventPublisher.publishEvent(new UserEvent(tzUser, UserEventEnum.ON_REGISTER));
        }
    }

    @Override
    public UserLoginRes login(UserLoginDTO userLoginDTO, HttpServletRequest request) {
        // 校验验证码
        ValidationUtils.throwIf(!captchaManager.validateCaptcha(userLoginDTO.getCaptchaCode(), userLoginDTO
          .getCaptchaId()), "captcha is incorrect");
        // 1. 校验用户名或邮箱是否存在
        LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUser::getEmail, userLoginDTO.getAccount())
          .or()
          .eq(TzUser::getUsername, userLoginDTO.getAccount());
        var user = tzUserMapper.selectOne(queryWrapper);
        ValidationUtils.throwIf(user == null, "[{}] account is not exist", userLoginDTO.getAccount());
        // 2. 校验密码
        String password = DigestUtils.md5DigestAsHex((userLoginDTO.getPassword() + user.getPasswordSalt()).getBytes());
        ValidationUtils.throwIf(!user.getPassword()
          .equals(password), "[{}] account or password is incorrect", userLoginDTO.getAccount());
        // 3. 检查用户状态
        ValidationUtils.throwIf(user
          .getStatus() != UserStatusEnum.NORMAL, "username or email [{}] is not active", userLoginDTO.getAccount());
        return getUserLoginRes(userLoginDTO.getRememberMe(), request, user);
    }

    @Override
    public void activation(String uid, String code) {
        // 1. 从 redis 中获取验证码
        String captchaKey = ACTIVATION_KEY_PREFIX.formatted(uid);
        String captchaCode = RedisUtils.get(captchaKey)
          .orElseThrow(() -> BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.VERIFICATION_CODE_HAS_EXPIRED))
          .toString();
        // 2. 校验验证码
        ValidationUtils.throwIfNotEqual(code, captchaCode, "Verification code is incorrect");
        // 2. 激活用户
        TzUser tzUser = tzUserMapper.selectById(uid);
        ValidationUtils.throwIfNull(tzUser, "user not found");
        tzUser.setStatus(UserStatusEnum.NORMAL);
        tzUserMapper.updateById(tzUser);
        RedisUtils.delete(captchaKey);
    }

    @Override
    public UserLoginRes loginByWms(String authCode, String cusCode) {
        HttpServletRequest request = SpringWebUtils.getRequest();
        // 1. 通过 authCode cusCode 获取 wms 账户信息
        WmsAccountInfoRes accountInfo = this.wmsManager.getWmsAccountInfo(authCode, cusCode)
          .orElseThrow(() -> new BusinessException("Unable to retrieve WMS account information"));
        // 2. 使用解密后的数据进行用户注册/登录
        String wmsCusCode = accountInfo.getCustomerCode();

        if (!wmsCusCode.equals(cusCode)) {
            log.warn("WMS安全登录校验失败：URL cuscode [{}] 与token cuscode [{}] 不匹配", cusCode, wmsCusCode);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.USER_NOT_FOUND);
        }

        LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<TzUser>()
          .eq(TzUser::getWmsCusCode, wmsCusCode);
        TzUser user = tzUserMapper.selectOne(queryWrapper);

        if (user != null) {
            // 如果用户存在，就判断未登录时间是否超过三天
            LocalDateTime threeDay = LocalDateTime.now().minusDays(3);

            if (user.getLastLoginTime() == null || user.getLastLoginTime().isAfter(threeDay)) {
                user.setUsername(accountInfo.getCustomerName());
                user.setEmail(accountInfo.getEmail());
                user.setMobile(accountInfo.getMobile());
                user.setWmsApiKey(accountInfo.getApiKey());
                user.setCountry(accountInfo.getCountry());
                user.setProvince(accountInfo.getProvince());
                user.setCity(accountInfo.getCity());
                user.setAddress(accountInfo.getAddress());
                // wms 设定的服务费
                user.setWmsServiceFee(accountInfo.getServiceFeeRate());
                tzUserMapper.updateById(user);
            } else {
                log.info("WMS user [{}] is active, skipping information update.", cusCode);
            }
        } else {
            // 2.1 注册用户
            IdGenerator idGenerator = DefaultIdGeneratorProvider.INSTANCE.getRequired("safe-js");
            // 2.2 生成随机密码
            String randomPassword = RandomStringUtils.randomAlphanumeric(8);
            // 2.3 生成密码盐值
            String passwordSalt = RandomStringUtils.randomAlphanumeric(16);
            // 2.4 密码加密 (使用盐值加密更安全)
            String password = DigestUtils.md5DigestAsHex((randomPassword + passwordSalt).getBytes());
            user = TzUser.builder()
              .id(idGenerator.generate())
              .email(accountInfo.getEmail())
              .username(accountInfo.getCustomerName())
              .mobile(accountInfo.getMobile())
              .password(password)
              .passwordSalt(passwordSalt)
              .status(UserStatusEnum.NORMAL)
              .registerTime(LocalDateTime.now())
              .registerIp(JakartaServletUtil.getClientIP(request))
              .type(UserTypeEnum.WMS)
              .wmsCusId(accountInfo.getCustomerId())
              .wmsCusCode(accountInfo.getCustomerCode())
              .wmsApiKey(accountInfo.getApiKey())
              // wms 设定的服务费
              .wmsServiceFee(accountInfo.getServiceFeeRate())
              .country(accountInfo.getCountry())
              .province(accountInfo.getProvince())
              .city(accountInfo.getCity())
              .address(accountInfo.getAddress())
              .build();
            tzUserMapper.insert(user);
        }
        // 4. 返回登录用户信息
        return getUserLoginRes(false, request, user);
    }

    @Override
    public void resendEmailCaptcha(String email) {
        // 校验邮箱是否存在
        LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUser::getEmail, email);
        TzUser user = tzUserMapper.selectOne(queryWrapper);
        ValidationUtils.throwIf(user == null, "email is not exist");
        // 校验用户是否已核查
        ValidationUtils.throwIf(user.getStatus() == UserStatusEnum.NORMAL, "user is already active");
        // 重新发送邮件
        applicationEventPublisher.publishEvent(new UserEvent(user, UserEventEnum.ON_REGISTER));
    }

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     */

    @Override
    public UserInfoRes getUserInfo(Long userId) {

        // 1. 查询用户信息
        TzUser user = tzUserMapper.selectById(userId);
        if (user == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.USER_NOT_FOUND);
        }

        // 2. 检查用户状态
        switch (user.getStatus()) {
            case DISABLED:
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.ACCOUNT_DISABLED);
            case LOCKED:
                throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.ACCOUNT_LOCKED);
            case NORMAL:
                break;
        }

        // 3. 返回用户信息
        return FrontendUserConvert.INSTANCE.toDo(user);
    }

    /**
     * 修改密码
     *
     * @param userId  用户id
     * @param request 请求
     */
    @Override
    public void updatePassword(Long userId, UserUpdateRep.UserUpdatePasswordReq request) {

        // 1. 查询用户信息
        TzUser user = tzUserMapper.selectById(userId);

        // 2. 验证旧密码
        String oldPasswordEncrypted = DigestUtils.md5DigestAsHex((request.getOldPassword() + user.getPasswordSalt())
          .getBytes());
        if (!oldPasswordEncrypted.equals(user.getPassword())) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.PASSWORD_ERROR);
        }

        // 3. 验证新密码不能与旧密码相同
        if (user.getPassword().equals(request.getNewPassword())) {
            throw BusinessExceptionI18n.of(FulfillmenValidationCodeEnum.NEW_PASSWORD_SAME_AS_OLD);
        }

        // 4. 更新密码
        String newPasswordEncrypted = DigestUtils.md5DigestAsHex((request.getNewPassword() + user.getPasswordSalt()).getBytes());
        user.setPassword(newPasswordEncrypted);
        // 更新密码后，强制让用户重新登录
        user.setPasswordVersion(user.getPasswordVersion() != null ? user.getPasswordVersion() + 1 : 1);

        int rows = tzUserMapper.updateById(user);
        if (rows != 1) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED);
        }
    }

    /**
     * 重置密码发送邮箱验证码
     */
    @Override
    public void sendResetPasswordEmailCaptcha() {
        // 1.从当前登录会话中获取用户ID和邮箱
        Long userId = StpUtil.getLoginIdAsLong();
        TzUser user = this.tzUserMapper.selectById(userId);

        // 2. 增加健壮性校验
        ValidationUtils.throwIfNull(user, "Logged-in user not found in database.");
        ValidationUtils.throwIf(user.getEmail() == null || user.getEmail().isBlank(),
          "User does not have a valid email address.");

        log.info("发送重置密码邮件验证码: {}", user.getEmail());

        // 3.触发邮件发送逻辑
        applicationEventPublisher.publishEvent(new UserEvent(user, UserEventEnum.ON_RESET_PASSWORD));
    }

    @Override
    public void resetPasswordWithEmail(UserUpdateRep.ResetPasswordReq request) {
        log.info("重置密码请求: {}", request);

        LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUser::getEmail, request.getEmail());
        TzUser user = tzUserMapper.selectOne(queryWrapper);
        if (user == null) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.USER_NOT_FOUND, request.getEmail());
        }

        resendEmailCaptcha(user.getEmail());

        String newPasswordEncrypted = DigestUtils.md5DigestAsHex((request.getNewPassword() + user.getPasswordSalt()).getBytes());

        user.setPassword(newPasswordEncrypted);
        // 更新密码后，强制让用户重新登录
        user.setPasswordVersion(user.getPasswordVersion() != null ? user.getPasswordVersion() + 1 : 1);

        int rows = tzUserMapper.updateById(user);
        if (rows != 1) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED);
        }
    }

    @Override
    public String sendUpdateEmailCaptcha(UserUpdateRep.SendUpdateEmailCaptchaReq request) {
        // 1. 安全获取当前登录用户
        Long userId = StpUtil.getLoginIdAsLong();
        TzUser user = this.tzUserMapper.selectById(userId);
        ValidationUtils.throwIfNull(user, "Logged-in user not found in database.");

        // 2. 调用重构后的私有方法进行校验
        validateNewEmailIsAvailable(user.getEmail(), request.getNewEmail());

        // 3. 发送验证码并返回 captchaId
        log.info("Sending 'update email' verification code to new email [{}] for user ID [{}]", request.getNewEmail(), userId);
        try {
            return captchaManager.sendEmailCaptcha(request.getNewEmail());
        } catch (Exception e) {
            log.error("Failed to send 'update email' captcha to [{}]. Error: {}", request.getNewEmail(), e.getMessage(), e);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED, "Failed to send verification email.");
        }
    }

    @Override
    public void updateEmail(UserUpdateRep.UserUpdateEmailReq request) {
        // 1. 验证邮箱验证码
        // todo 后续要换成i18nMessageUtils.getMessage
        ValidationUtils.throwIf(!captchaManager.validateCaptcha(request.getCaptchaCode(), request.getCaptchaId()),
          messageSource.getMessage("validation.captcha.code.incorrect", null, LocaleContextHolder.getLocale()));

        // 2. 安全地获取当前登录用户 (修复安全漏洞)
        Long userId = StpUtil.getLoginIdAsLong();
        TzUser user = tzUserMapper.selectById(userId);
        ValidationUtils.throwIfNull(user, "Logged-in user not found in database.");

        // 3. 再次校验新邮箱，防止在验证和更新之间被占用 (安全实践)
        validateNewEmailIsAvailable(user.getEmail(), request.getNewEmail());

        // 4. 更新邮箱
        log.info("User ID [{}] is updating email to [{}]", userId, request.getNewEmail());
        user.setEmail(request.getNewEmail());

        int rows = tzUserMapper.updateById(user);
        if (rows != 1) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED);
        }
    }

    /**
     * 校验新邮箱是否可用的私有辅助方法
     *
     * @param oldEmail 当前邮箱
     * @param newEmail 新邮箱
     */
    private void validateNewEmailIsAvailable(String oldEmail, String newEmail) {
        // 校验新邮箱不能与旧邮箱相同
        if (oldEmail.equalsIgnoreCase(newEmail)) {
            // 建议：未来可以为此场景创建一个更具体的错误码，例如 NEW_EMAIL_SAME_AS_OLD
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED, "New email cannot be the same as the old one.");
        }

        // 校验新邮箱是否已被其他用户使用
        LambdaQueryWrapper<TzUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUser::getEmail, newEmail);
        if (this.tzUserMapper.selectCount(queryWrapper) > 0) {
            // 建议：未来可以为此场景创建一个更具体的错误码，例如 EMAIL_ALREADY_IN_USE
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED, "This email address is already in use.");
        }
    }

    /**
     * 修改用户信息
     *
     * @param userId  用户id
     * @param request 请求
     */
    @Override
    public void updateInfo(Long userId, UserUpdateRep.UserUpdateInfoReq request) {

        // 1. 查询用户信息
        TzUser user = tzUserMapper.selectById(userId);

        // 2. 更新用户信息
        updateUserInfo(user, request);

        // 3. 保存更新
        int rows = tzUserMapper.updateById(user);
        if (rows != 1) {
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED);
        }
    }

    /**
     * 用户创建新的收货地址
     *
     * @param request 地址创建请求
     * @return 创建后的地址信息
     */
    @Override
    public UserAddressRes createAddress(UserAddressReq.CreateReq request) {
        // 1.获取当前用户id
        Long userId = UserContextHolder.getUserId();

        // 2.如果设置为默认地址，则将原来的默认地址取消默认
        if (Boolean.TRUE.equals(request.getIsDefault())) {
            unsetOldDefaultAddress(userId);
        }

        // 3.转换并保存新地址
        TzUserAddress address = TzUserAddressMapping.INSTANCE.toEntity(request);
        address.setUserId(userId);
        address.setGmtCreated(LocalDateTime.now());
        address.setGmtModified(LocalDateTime.now());
        tzUserAddressMapper.insert(address);

        log.info("用户[{}]成功创建新地址，地址ID为[{}]", userId, address.getId());
        return TzUserAddressMapping.INSTANCE.toRes(address);
    }

    /**
     * 用户修改收货地址
     *
     * @param id      地址ID
     * @param request 地址修改请求
     */
    @Override
    public void reviseAddress(Long id, UserAddressReq.ReviseReq request) {
        Long userId = UserContextHolder.getUserId();
        // 查找并验证用户地址，确保地址属于当前用户
        TzUserAddress address = findAndVerifyUserAddress(id, userId);
        boolean wasDefault = Boolean.TRUE.equals(address.getIsDefault());

        // 仅当一个非默认地址要被设为默认时，才去重置旧的默认地址
        if (Boolean.TRUE.equals(request.getIsDefault()) && !wasDefault) {
            unsetOldDefaultAddress(userId);
        }

        // 使用MapStruct更新实体
        TzUserAddressMapping.INSTANCE.updateEntityFromReq(request, address);
        address.setGmtModified(LocalDateTime.now());
        tzUserAddressMapper.updateById(address);
        log.info("用户[{}]成功更新地址，地址ID为[{}]", userId, id);
    }

    /**
     * 用户删除地址
     *
     * @param id 要删除的地址ID
     */
    @Override
    public void deleteAddress(Long id) {
        Long userId = UserContextHolder.getUserId();

        TzUserAddress address = findAndVerifyUserAddress(id, userId);

        int deleteRows = tzUserAddressMapper.deleteById(address);
        if (deleteRows == 0) {
            log.warn("用户[{}]删除地址[{}]失败，可能已被其他事务删除", userId, id);
            throw BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.OPERATION_FAILED, "删除地址失败");
        }
        log.info("用户[{}]成功删除，地址ID[{}]", userId, id);
    }

    /**
     * 用户设置默认地址
     *
     * @param id 要设置为默认的地址ID
     */
    @Override
    public void setDefaultAddress(Long id) {
        Long userId = UserContextHolder.getUserId();
        TzUserAddress addressToSetDefault = findAndVerifyUserAddress(id, userId);

        if (Boolean.TRUE.equals(addressToSetDefault.getIsDefault())) {
            log.info("地址[{}]已是用户[{}]的默认地址，无需重复设置", id, userId);
            return;
        }

        // 事务性操作：先取消旧默认，再设置新默认
        unsetOldDefaultAddress(userId);
        addressToSetDefault.setIsDefault(true);
        tzUserAddressMapper.updateById(addressToSetDefault);

        log.info("成功将地址[{}]设置为用户[{}]的默认地址", id, userId);
    }

    /**
     * 用户获取地址
     *
     * @param id 地址ID
     * @return 地址信息
     */
    @Override
    public UserAddressRes getAddress(Long id) {
        Long userId = UserContextHolder.getUserId();

        TzUserAddress address = findAndVerifyUserAddress(id, userId);
        return TzUserAddressMapping.INSTANCE.toRes(address);
    }

    private TzUserAddress findAndVerifyUserAddress(Long id, Long userId) {
        LambdaQueryWrapper<TzUserAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TzUserAddress::getId, id)
          .eq(TzUserAddress::getUserId, userId);
        return Optional.ofNullable(tzUserAddressMapper.selectOne(queryWrapper))
          .orElseThrow(() -> BusinessExceptionI18n.of(FulfillmenErrorCodeEnum.RELEVANT_INFORMATION_NOT_FOUND));
    }

    /**
     * 用户获取地址列表
     *
     * @return 地址列表
     */
    @Override
    public List<UserAddressRes> listAddresses() {
        Long userId = UserContextHolder.getUserId();
        LambdaQueryWrapper<TzUserAddress> queryWrapper = new LambdaQueryWrapper<TzUserAddress>()
          .eq(TzUserAddress::getUserId, userId)
          .orderByDesc(TzUserAddress::getIsDefault);

        List<TzUserAddress> addresses = tzUserAddressMapper.selectList(queryWrapper);
        return TzUserAddressMapping.INSTANCE.toResList(addresses);
    }

    /**
     * 将指定用户的所有地址设置为非默认，这是一个原子操作。
     */
    private void unsetOldDefaultAddress(Long userId) {
        LambdaUpdateWrapper<TzUserAddress> updateWrapper = new LambdaUpdateWrapper<TzUserAddress>()
          .eq(TzUserAddress::getUserId, userId)
          .eq(TzUserAddress::getIsDefault, true)
          .set(TzUserAddress::getIsDefault, false);
        tzUserAddressMapper.update(null, updateWrapper);
    }

    // 私有方法，用于更新用户信息
    private void updateUserInfo(TzUser tzUser, UserUpdateRep.UserUpdateInfoReq request) {
        tzUser.setUsername(request.getUsername());
        tzUser.setGender(request.getGender());
        tzUser.setBirth(request.getBirth());
        tzUser.setAvatar(request.getAvatar());
        tzUser.setMobile(request.getMobile());
    }

    /**
     * 获取登录用户信息
     *
     * @param rememberMe 是否记住我 true yes，false no
     * @param request    请求对象
     * @param user       用户信息
     * @return 登录用户信息
     */
    @NotNull
    private UserLoginRes getUserLoginRes(Boolean rememberMe, HttpServletRequest request, TzUser user) {
        // 创建用户上下文信息
        UserContext userContext = UserContext.builder()
          .id(user.getId())
          .username(user.getUsername())
          .wmsCusCode(user.getWmsCusCode() == null ? null : user.getWmsCusCode())
          .wmsApiKey(user.getWmsApiKey() == null ? null : user.getWmsApiKey())
          .wmsServiceFee(user.getWmsServiceFee())
          .tenantId(user.getTenantId())
          .build();

        // 记住我，本地保存token 时长为7天
        SaLoginParameter loginParameter = SaLoginParameter.create();
        if (rememberMe) {
            loginParameter.setTimeout(60 * 60 * 24 * 7);
        }

        // 3. 登录成功
        StpUtil.login(user.getId(), loginParameter);
        UserContextHolder.setContext(userContext);
        // 3.1 异步更新用户最后登录时间
        CompletableFuture.runAsync(() -> {
            // 异步更新用户最后登录时间
            user.setLastLoginTime(LocalDateTime.now());
            user.setLastLoginIp(JakartaServletUtil.getClientIP(request));
            tzUserMapper.updateById(user);
        }, threadPoolTaskExecutor);
        // 4. 返回登录用户信息
        return new UserLoginRes(user.getId(), user.getUsername(), user.getEmail(), user.getAvatar(), StpUtil
          .getTokenName(), StpUtil.getTokenValue());
    }
}
