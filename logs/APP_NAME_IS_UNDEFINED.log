2025-08-12 14:40:02 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107316398272585728]:[0] [POST] /alibaba/callback
2025-08-12 14:40:02 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107316398272585728]:[0] 收到阿里巴巴webhook回调，消息长度: 337
2025-08-12 14:40:02 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107316398272585728]:[0] 开始处理webhook消息, 消息长度: 337
2025-08-12 14:40:02 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107316398272585728]:[0] 解析到 1 个消息事件
2025-08-12 14:40:03 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107316398272585728]:[0] 接收到订单webhook消息: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873749476412540788, status=waitsellersend
2025-08-12 14:40:04 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.i.SysAlibabaCallbackLogsRepositoryImpl - [1107316398272585728]:[0] 创建Webhook处理日志异常(含事件与订单)
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
### The error may exist in com/fulfillmen/shop/dao/mapper/SysAlibabaCallbackLogsMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_alibaba_callback_logs (id, metadata, order_id, event_type, received_timestamp, process_status, gmt_created, gmt_modified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
; Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy150.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl.createProcessingLog(SysAlibabaCallbackLogsRepositoryImpl.java:87)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl$$SpringCGLIB$$0.createProcessingLog(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:126)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy231.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy230.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 130 common frames omitted
2025-08-12 14:40:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107316398272585728]:[0] 开始处理订单webhook消息: orderId=2873749476412540788, msgId=144548371008, messageType=ORDER_BUYER_VIEW_ORDER_PAY, currentStatus=waitsellersend
2025-08-12 14:40:04 INFO  [naya-task-pool6] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1107316398272585728]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2873749476412540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-08-12 14:40:04 INFO  [naya-task-pool7] [tid::uId::ip::os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107316398272585728]:[0] 开始WMS订单详情查询，查询条件: {"orderId":"2873749476412540788"}
2025-08-12 14:40:04 INFO  [naya-task-pool6] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107316398272585728]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1754980804064access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2873749476412540788webSite1688 签名: 67F61D8362F58AFAF4D2828B5048B1D150A175EA
2025-08-12 14:40:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107316398272585728]:[0] 订单数据获取完成: orderId=2873749476412540788, alibabaOrderDetail=true, wmsOrderDetails=1
2025-08-12 14:40:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107316398272585728]:[0] 未找到供应商订单: orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2873749476412540788, idOfStr=2873749476412540788, businessType=cb, buyerId=b2b-2207416548807a4d12, completeTime=null, createTime=2025-08-11T18:03:15, modifyTime=2025-08-12T11:05:55, refund=0, sellerId=b2b-26784394432e7a2, shippingFee=0, status=waitsellersend, totalAmount=1775, discount=0, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-0579-12345678, fax=null, email=null, name=钟少燕, imInPlatform=宏培电子商务, companyName=义乌市宏培化妆品有限公司, mobile=***********, address=null), shopName=义乌市宏培化妆品有限公司, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=null, payTime=2025-08-12T11:05:55, receivingTime=null, alipayTradeId=2025081222001846301445063864, sumProductPayment=1776, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=宏培电子商务, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=中田 12092, toDivisionCode=441302, toPost=516000, toArea=广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=18124000751, toPhone=null, toEmail=null, toTownCode=null), tradeTypeDesc=担保交易, payChannelList=[跨境宝2.0], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[kjpayV2], outOrderId=12092, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-08-11T18:03:16, gmtPay=2025-08-12T11:05:55, gmtEnd=null, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=2, logisticsStatus=1, payFee=1775, paidFee=1775, goodsFee=null, adjustFee=0, discountFee=1, postFee=0, paidPostFee=0)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=2, payTime=2025-08-12T11:05:55, payWay=13, phasAmount=1775, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=null, description=null, itemAmount=719.6, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=10, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476413540788, quantity=72, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476413540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=1000ml  hair mask（鱼子酱）0152), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=3c075ebfdd021eb6da2f7451e9a278c6, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476413540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=null, description=null, itemAmount=527.7, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=11, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476414540788, quantity=48, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476414540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=750ml  conditioner（鱼子酱）0145), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=92d9e9428136d07a8728ccde5ff4e290, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476414540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=null, description=null, itemAmount=527.7, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=11, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476415540788, quantity=48, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476415540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=750ml  shampoo（鱼子酱）0138), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=f506ba9c3c6a37fbd1129f6aa880fa4c, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476415540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)])], nativeLogistics=TradeNativeLogisticsInfo(address=江北 金泽物流园二期一号楼四楼-12092, area=惠城区, areaCode=441302, city=惠州市, contactPerson=中田 12092, fax=null, mobile=18124000751, province=广东省, telephone=null, zip=516000, logisticsItems=null, townCode=null, town=null), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null)
2025-08-12 14:40:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107316398272585728]:[0] 订单数据完整性检查结果: orderId=2873749476412540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-08-12 14:40:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107316398272585728]:[0] 开始同步和补齐订单数据: orderId=2873749476412540788, needsSync=true
2025-08-12 14:40:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107316398272585728]:[0] 开始补齐缺失的订单数据: orderId=2873749476412540788, missing=采购订单, 供应商订单, 订单项
2025-08-12 14:40:06 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107316398272585728]:[0] 创建采购订单成功: orderId=2873749476412540788, purchaseOrderId=728177073901724
2025-08-12 14:40:06 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107316398272585728]:[0] 创建供应商订单成功: orderId=2873749476412540788, supplierOrderCount=1
2025-08-12 14:40:59 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107316398272585728]:[0] 事务执行过程中发生异常: orderId=2873749476412540788
java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-12 14:40:59 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107316398272585728]:[0] 订单webhook消息处理失败: orderId=2873749476412540788, msgId=144548371008, messageType=ORDER_BUYER_VIEW_ORDER_PAY, error=订单数据补齐事务执行失败
com.fulfillmen.starter.core.exception.BusinessException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:242)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	... 115 common frames omitted
2025-08-12 14:40:59 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107316398272585728]:[0] 发布订单处理失败事件: orderId=2873749476412540788, error=订单数据补齐事务执行失败
2025-08-12 14:40:59 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107316398272585728]:[0] 订单webhook消息处理失败: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873749476412540788, error=订单数据补齐事务执行失败
com.fulfillmen.starter.core.exception.BusinessException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:242)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	... 115 common frames omitted
2025-08-12 14:40:59 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.alibaba.webhook.AbstractTypedMessageHandler - [1107316398272585728]:[0] 消息处理异常: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY
com.fulfillmen.starter.core.exception.BusinessException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:242)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	... 115 common frames omitted
2025-08-12 14:40:59 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107316398272585728]:[0] Webhook消息处理完成，处理时间: 56737ms，处理结果数量: 1
2025-08-12 14:40:59 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107316398272585728]:[0] [POST] /alibaba/callback 200 56739ms
2025-08-12 14:41:00 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m12s46ms).
2025-08-12 14:45:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-08-12 14:31:03,422 to 2025-08-12 14:45:00,002
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.01|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.01| 10.00%|            10|             1|             0|             0|        0.0|          0
currency.rate._remote      |      0.01|100.00%|             9|             9|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.01| 83.33%|             6|             5|             0|             0|        0.0|          0
pdc:product:_local         |      0.01| 83.33%|             6|             5|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             1|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-08-12 14:45:00 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.s.SysAlibabaCallbackRetryScheduledTask - 开始执行阿里巴巴回调重试任务，配置：maxRetryCount=3, batchSize=20, timeWindowHours=24
2025-08-12 14:45:00 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.s.SysAlibabaCallbackRetryScheduledTask - 没有需要重试的失败记录
2025-08-12 14:46:41 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1754981201206}] instanceId:[InstanceId{instanceId=**************:2620, stable=false}] @ namespace:[fulfillmen-shop].
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-12 14:46:41 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-12 14:46:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:46:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-08-12 14:46:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-12 14:46:43 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
