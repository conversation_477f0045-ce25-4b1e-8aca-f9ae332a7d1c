2025-08-12 14:26:57 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-12 14:26:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 2247 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-08-12 14:26:57 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-08-12 14:26:57 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "sealosDev"
2025-08-12 14:26:59 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-12 14:26:59 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-12 14:26:59 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 50 ms. Found 0 Redis repository interfaces.
2025-08-12 14:26:59 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-08-12 14:26:59 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:26:59 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:26:59 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-08-12 14:26:59 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, sys_alibaba_callback_logs, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-08-12 14:26:59 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-08-12 14:27:00 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2918 ms
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:27:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:27:00 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-08-12 14:27:00 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-08-12 14:27:00 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-08-12 14:27:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-08-12 14:27:01 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-08-12 14:27:01 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-08-12 14:27:01 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-08-12 14:27:01 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-08-12 14:27:02 INFO  [redisson-netty-1-7] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:27:14 INFO  [redisson-netty-1-19] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:27:32 INFO  [redisson-netty-1-1] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-08-12 14:27:32 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[http://wms.fulfillmen.com], skipSslVerification=[false]
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 开始注册订单事件处理器...
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCompletionProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SUCCESS
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderConfirmationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCreationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_BUYER_MAKE
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BATCH_PAY
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPriceModificationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_PART_PART_SENDGOODS
2025-08-12 14:27:32 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 订单事件处理器注册完成，共注册 8 个处理器，支持 13 种消息类型。
2025-08-12 14:27:32 WARN  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sysAlibabaCallbackRetryScheduledTask' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes/com/fulfillmen/shop/secheduler/SysAlibabaCallbackRetryScheduledTask.class]: Unsatisfied dependency expressed through constructor parameter 2: Error creating bean with name 'weChatNotificationServiceImpl' defined in file [/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-manager/target/classes/com/fulfillmen/shop/manager/support/notification/service/impl/WeChatNotificationServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0: No qualifying bean of type 'org.springframework.web.client.RestTemplate' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {}
2025-08-12 14:27:33 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-08-12 14:27:33 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.logging.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-08-12 14:27:33 ERROR [main] [tid::uId::ip::os::browser:] o.s.b.diagnostics.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Parameter 0 of constructor in com.fulfillmen.shop.manager.support.notification.service.impl.WeChatNotificationServiceImpl required a bean of type 'org.springframework.web.client.RestTemplate' that could not be found.


Action:

Consider defining a bean of type 'org.springframework.web.client.RestTemplate' in your configuration.

2025-08-12 14:30:38 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-12 14:30:38 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 2620 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-08-12 14:30:38 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-08-12 14:30:38 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "sealosDev"
2025-08-12 14:30:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-12 14:30:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-12 14:30:39 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 29 ms. Found 0 Redis repository interfaces.
2025-08-12 14:30:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-08-12 14:30:39 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:30:39 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:30:39 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-08-12 14:30:39 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, sys_alibaba_callback_logs, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-08-12 14:30:40 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2402 ms
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:30:40 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:30:40 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-08-12 14:30:40 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-08-12 14:30:40 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-08-12 14:30:40 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-08-12 14:30:41 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-08-12 14:30:41 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-08-12 14:30:41 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-08-12 14:30:41 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-08-12 14:30:43 INFO  [redisson-netty-1-7] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:31:03 INFO  [redisson-netty-1-20] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:31:03 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-08-12 14:31:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-08-12 14:31:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-08-12 14:31:03 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[http://wms.fulfillmen.com], skipSslVerification=[false]
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 开始注册订单事件处理器...
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCompletionProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SUCCESS
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderConfirmationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCreationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_BUYER_MAKE
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BATCH_PAY
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPriceModificationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_PART_PART_SENDGOODS
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 订单事件处理器注册完成，共注册 8 个处理器，支持 13 种消息类型。
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [DISABLED, AUTO, MANUAL]
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - 支持的消息类型: [ORDER_BUYER_VIEW_BUYER_MAKE, ORDER_BUYER_VIEW_ORDER_PAY, ORDER_BATCH_PAY, ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS, ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, ORDER_BUYER_VIEW_ORDER_SUCCESS, ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY]
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.autoconfigure.ValidatorAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Validator' completed initialization.
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-12 14:31:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-08-12 14:31:04 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3bec2275
2025-08-12 14:31:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-08-12 14:31:05 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:2620, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-12 14:31:05 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1754980265057}] - instanceId:[InstanceId{instanceId=**************:2620, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-12 14:31:05 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-08-12 14:31:05 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-12 14:31:05 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-12 14:31:05 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - TaskScheduler extension configuration applied: poolSize=12, threadNamePrefix=scheduling-, rejectedPolicy=CALLER_RUNS
2025-08-12 14:31:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 95 ms
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-08-12 14:31:06 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-12 14:31:06 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 29.271 seconds (process running for 29.939)
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-12 14:31:07 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-08-12 14:31:07 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-08-12 14:31:07 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-12 14:31:07 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-12 14:31:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-08-12 14:31:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-12 14:31:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-12 14:31:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-08-12 14:31:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-12 14:31:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-12 14:31:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-12 14:31:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-12 14:31:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-08-12 14:31:11 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 14:31:11 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 14:31:11 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-12 14:31:11 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-08-12 14:31:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-08-12 14:31:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-12 14:31:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-12 14:31:12 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-08-12 14:31:12 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-12 14:31:12 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-12 14:31:12 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-12 14:31:12 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-12 14:31:12 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-08-12 14:31:17 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@289835b8
2025-08-12 14:31:17 INFO  [RMI TCP Connection(3)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 14:34:54 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107315101058244608]:[0] 从自定义域名(localhost)解析到租户ID: 10000
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107315101058244608]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107315101058244608]:[0] 租户缓存命中: 10000
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107315101058244608]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107315101058244608]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107315101058244608]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 14:34:55 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107315101058244608]:[0] [POST] /alibaba/callback
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107315101058244608]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107315101058244608]:[0] 收到阿里巴巴webhook回调，消息内容: "bizKey":"2873749476412540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":2873749476412540788,"sellerMemberId":"b2b-**********2e7a2","msgSendTime":"2025-08-12 11:05:55"},"gmtBorn":1754967955151,"msgId":144548371008,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: E2E11A0298D66A0E97E8ED75DAA8D40AED28F9B6
2025-08-12 14:34:55 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107315101058244608]:[0] 收到阿里巴巴webhook回调，消息长度: 336
2025-08-12 14:34:55 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107315101058244608]:[0] 开始处理webhook消息, 消息长度: 336
2025-08-12 14:34:55 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.SignatureValidator - [1107315101058244608]:[0] 签名验证失败，期望: B73314BD8E21B647E1E5134DACF135743FEB8EB8, 实际: E2E11A0298D66A0E97E8ED75DAA8D40AED28F9B6
2025-08-12 14:34:55 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107315101058244608]:[0] 消息签名验证失败
2025-08-12 14:34:55 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107315101058244608]:[0] Webhook消息处理失败，处理时间: 20 ms
com.fulfillmen.support.alibaba.webhook.exception.WebhookProcessingException: 消息签名验证失败
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:50)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-12 14:34:55 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107315101058244608]:[0] [POST] /alibaba/callback 200 43ms
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107315101058244608]:[0] 清理增强租户上下文
2025-08-12 14:34:55 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107315101058244608]:[0] 过滤器清理租户上下文完成
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107315292062654464]:[0] 从缓存中获取租户ID: 10000
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107315292062654464]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107315292062654464]:[0] 租户缓存命中: 10000
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107315292062654464]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107315292062654464]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107315292062654464]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 14:35:38 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107315292062654464]:[0] [POST] /alibaba/callback
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107315292062654464]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 14:35:38 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107315292062654464]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"2873749476412540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":2873749476412540788,"sellerMemberId":"b2b-**********2e7a2","msgSendTime":"2025-08-12 11:05:55"},"gmtBorn":1754967955151,"msgId":144548371008,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: E2E11A0298D66A0E97E8ED75DAA8D40AED28F9B6
2025-08-12 14:35:38 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107315292062654464]:[0] 收到阿里巴巴webhook回调，消息长度: 337
2025-08-12 14:35:38 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107315292062654464]:[0] 开始处理webhook消息, 消息长度: 337
2025-08-12 14:35:38 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107315292062654464]:[0] 解析到 1 个消息事件
2025-08-12 14:35:39 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107315292062654464]:[0] 接收到订单webhook消息: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873749476412540788, status=waitsellersend
2025-08-12 14:35:39 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.i.SysAlibabaCallbackLogsRepositoryImpl - [1107315292062654464]:[0] 创建Webhook处理日志异常(含事件与订单)
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
### The error may exist in com/fulfillmen/shop/dao/mapper/SysAlibabaCallbackLogsMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_alibaba_callback_logs (id, metadata, order_id, event_type, received_timestamp, process_status, gmt_created, gmt_modified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
; Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy150.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl.createProcessingLog(SysAlibabaCallbackLogsRepositoryImpl.java:87)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl$$SpringCGLIB$$0.createProcessingLog(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:126)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy231.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy230.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 130 common frames omitted
2025-08-12 14:35:39 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107315292062654464]:[0] 开始处理订单webhook消息: orderId=2873749476412540788, msgId=144548371008, messageType=ORDER_BUYER_VIEW_ORDER_PAY, currentStatus=waitsellersend
2025-08-12 14:35:39 INFO  [naya-task-pool2] [tid::uId::ip::os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107315292062654464]:[0] 开始WMS订单详情查询，查询条件: {"orderId":"2873749476412540788"}
2025-08-12 14:35:39 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1107315292062654464]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2873749476412540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-08-12 14:35:39 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107315292062654464]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1754980539616access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2873749476412540788webSite1688 签名: 40C86E8AF8E80C156519F04DF11E695B475F3FB5
2025-08-12 14:35:41 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107315292062654464]:[0] 订单数据获取完成: orderId=2873749476412540788, alibabaOrderDetail=true, wmsOrderDetails=1
2025-08-12 14:35:41 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 开始检查订单数据完整性: orderDetail={"baseInfo":{"id":2873749476412540788,"businessType":"cb","refund":0,"shippingFee":0,"status":"waitsellersend","totalAmount":1775,"discount":0,"buyerContact":{"phone":"86-752-2313067","name":"汤维政","imInPlatform":"惠州中田贸易","companyName":"惠州市中田贸易有限公司","mobile":"***********"},"sellerContact":{"phone":"86-0579-12345678","name":"钟少燕","imInPlatform":"宏培电子商务","companyName":"义乌市宏培化妆品有限公司","mobile":"***********","shopName":"义乌市宏培化妆品有限公司"},"tradeType":"50060","refundPayment":0,"idOfStr":"2873749476412540788","buyerID":"b2b-2207416548807a4d12","createTime":"2025-08-11 18:03:15","modifyTime":"2025-08-12 11:05:55","sellerID":"b2b-**********2e7a2","payTime":"2025-08-12 11:05:55","alipayTradeId":"2025081222001846301445063864","sumProductPayment":1776,"flowTemplateCode":"assureTrade","sellerOrder":false,"buyerLoginId":"惠州中田贸易","sellerLoginId":"宏培电子商务","closeOperateType":"","couponFee":0,"receiverInfo":{"toFullName":"中田 12092","toDivisionCode":"441302","toPost":"516000","toArea":"广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092","toMobile":"***********"},"tradeTypeDesc":"担保交易","payChannelList":["跨境宝2.0"],"tradeTypeCode":"assureTrade","payTimeout":432000,"payTimeoutType":0,"payChannelCodeList":["kjpayV2"],"outOrderId":"12092","stepPayAll":false,"newStepOrderList":[{"gmtStart":"2025-08-11 18:03:16","gmtPay":"2025-08-12 11:05:55","stepNo":1,"lastStep":true,"stepName":"全款交易","activeStatus":1,"payStatus":2,"logisticsStatus":1,"payFee":1775,"paidFee":1775,"adjustFee":0,"discountFee":1,"postFee":0,"paidPostFee":0}],"overSeaOrder":false},"orderBizInfo":{"odsCyd":false,"creditOrder":false,"dropshipping":false,"shippingInsurance":"givenByAnXinGou"},"tradeTerms":[{"payStatus":"2","payTime":"2025-08-12 11:05:55","payWay":"13","phasAmount":1775,"phase":*****************,"cardPay":false,"expressPay":false,"payWayDesc":"支付平台"}],"productItems":[{"itemAmount":719.6,"name":"跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask","price":10,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476413540788","quantity":72,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":2873749476413540788,"type":"common","unit":"瓶","productCargoNumber":"0121/0107/0114","skuInfos":[{"name":"规格类型","value":"1000ml  hair mask（鱼子酱）0152"},{"name":"净含量","value":"产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明"}],"entryDiscount":0,"specId":"3c075ebfdd021eb6da2f7451e9a278c6","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"2873749476413540788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:03:16","gmtModified":"2025-08-12 11:05:55","gmtPayExpireTime":"2025-08-12 11:20:45","guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]},{"itemAmount":527.7,"name":"跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask","price":11,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476414540788","quantity":48,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":2873749476414540788,"type":"common","unit":"瓶","productCargoNumber":"0121/0107/0114","skuInfos":[{"name":"规格类型","value":"750ml  conditioner（鱼子酱）0145"},{"name":"净含量","value":"产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明"}],"entryDiscount":0,"specId":"92d9e9428136d07a8728ccde5ff4e290","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"2873749476414540788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:03:16","gmtModified":"2025-08-12 11:05:55","gmtPayExpireTime":"2025-08-12 11:20:45","guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]},{"itemAmount":527.7,"name":"跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask","price":11,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476415540788","quantity":48,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":2873749476415540788,"type":"common","unit":"瓶","productCargoNumber":"0121/0107/0114","skuInfos":[{"name":"规格类型","value":"750ml  shampoo（鱼子酱）0138"},{"name":"净含量","value":"产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明"}],"entryDiscount":0,"specId":"f506ba9c3c6a37fbd1129f6aa880fa4c","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"2873749476415540788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:03:16","gmtModified":"2025-08-12 11:05:55","gmtPayExpireTime":"2025-08-12 11:20:45","guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]}],"nativeLogistics":{"address":"江北 金泽物流园二期一号楼四楼-12092","area":"惠城区","areaCode":"441302","city":"惠州市","contactPerson":"中田 12092","mobile":"***********","province":"广东省","zip":"516000"},"orderRateInfo":{"buyerRateStatus":5,"sellerRateStatus":5},"extAttributes":[],"fromEncryptOrder":false}
2025-08-12 14:35:41 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 未找到供应商订单: orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2873749476412540788, idOfStr=2873749476412540788, businessType=cb, buyerId=b2b-2207416548807a4d12, completeTime=null, createTime=2025-08-11T18:03:15, modifyTime=2025-08-12T11:05:55, refund=0, sellerId=b2b-**********2e7a2, shippingFee=0, status=waitsellersend, totalAmount=1775, discount=0, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-0579-12345678, fax=null, email=null, name=钟少燕, imInPlatform=宏培电子商务, companyName=义乌市宏培化妆品有限公司, mobile=***********, address=null), shopName=义乌市宏培化妆品有限公司, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=null, payTime=2025-08-12T11:05:55, receivingTime=null, alipayTradeId=2025081222001846301445063864, sumProductPayment=1776, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=宏培电子商务, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=中田 12092, toDivisionCode=441302, toPost=516000, toArea=广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=***********, toPhone=null, toEmail=null, toTownCode=null), tradeTypeDesc=担保交易, payChannelList=[跨境宝2.0], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[kjpayV2], outOrderId=12092, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-08-11T18:03:16, gmtPay=2025-08-12T11:05:55, gmtEnd=null, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=2, logisticsStatus=1, payFee=1775, paidFee=1775, goodsFee=null, adjustFee=0, discountFee=1, postFee=0, paidPostFee=0)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=2, payTime=2025-08-12T11:05:55, payWay=13, phasAmount=1775, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=null, description=null, itemAmount=719.6, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=10, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476413540788, quantity=72, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476413540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=1000ml  hair mask（鱼子酱）0152), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=3c075ebfdd021eb6da2f7451e9a278c6, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476413540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=null, description=null, itemAmount=527.7, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=11, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476414540788, quantity=48, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476414540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=750ml  conditioner（鱼子酱）0145), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=92d9e9428136d07a8728ccde5ff4e290, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476414540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=null, description=null, itemAmount=527.7, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=11, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476415540788, quantity=48, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476415540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=750ml  shampoo（鱼子酱）0138), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=f506ba9c3c6a37fbd1129f6aa880fa4c, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476415540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)])], nativeLogistics=TradeNativeLogisticsInfo(address=江北 金泽物流园二期一号楼四楼-12092, area=惠城区, areaCode=441302, city=惠州市, contactPerson=中田 12092, fax=null, mobile=***********, province=广东省, telephone=null, zip=516000, logisticsItems=null, townCode=null, town=null), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null)
2025-08-12 14:35:41 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107315292062654464]:[0] 订单数据完整性检查结果: orderId=2873749476412540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-08-12 14:35:41 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 开始同步和补齐订单数据: orderId=2873749476412540788, needsSync=true
2025-08-12 14:35:41 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 开始补齐缺失的订单数据: orderId=2873749476412540788, missing=采购订单, 供应商订单, 订单项
2025-08-12 14:35:42 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 创建采购订单成功: orderId=2873749476412540788, purchaseOrderId=728175992787074
2025-08-12 14:35:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 创建供应商订单成功: orderId=2873749476412540788, supplierOrderCount=1
2025-08-12 14:35:44 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107315292062654464]:[0] 数据库不存在，从API获取商品详情, id: ************
2025-08-12 14:35:44 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107315292062654464]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1754980544206access_token5fd99355-518b-47a6-b83e-0503223e0665offerDetailParam{"offerId":************,"country":"en"} 签名: 546E185961BF3DA47A0E3E74845AB8B9D4709C2E
2025-08-12 14:35:44 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] io.micrometer.core.instrument.MeterRegistry - [1107315292062654464]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-08-12 14:35:44 DEBUG [reactor-http-nio-3] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - [1107315292062654464]:[0] result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=1047065, categoryName=null, subject=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, subjectTrans=Cross-Border Shampoo Conditioner Hair Mask Full English E-Commerce Anti-Dandruff Moisturizing Export Shampoo Hair Mask, description=<div id="offer-template-0"></div><div style="width: 790.0px;"><img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01dBIWss2Jcx6kSVlk3_!!**********-0-cib.jpg" usemap="#_sdmap_0"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN016i0WKb2Jcx6nG74QY_!!**********-0-cib.jpg" usemap="#_sdmap_1"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN013NYHeu2Jcx6mCuDFB_!!**********-0-cib.jpg" usemap="#_sdmap_2"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN012KCWjC2Jcx6o0jcDh_!!**********-0-cib.jpg" usemap="#_sdmap_3"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01bybKqK2Jcx6mCuDFU_!!**********-0-cib.jpg" usemap="#_sdmap_4"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01UK0GnW2Jcx6ni7yYT_!!**********-0-cib.jpg" usemap="#_sdmap_5"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01MpdWBL2Jcx6my2zOs_!!**********-0-cib.jpg" usemap="#_sdmap_6"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01UQNSK62Jcx6mngDaa_!!**********-0-cib.jpg" usemap="#_sdmap_7"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Owx7nH2Jcx6my2BVD_!!**********-0-cib.jpg" usemap="#_sdmap_8"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01HvtLou2Jcx6my23B5_!!**********-0-cib.jpg" usemap="#_sdmap_9"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN012oLZZ02Jcx6kSAQFu_!!**********-0-cib.jpg" usemap="#_sdmap_10"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01xrbVDr2Jcx6nE0YYd_!!**********-0-cib.jpg" usemap="#_sdmap_11"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01VMVfIA2Jcx6l2864R_!!**********-0-cib.jpg" usemap="#_sdmap_12"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01c1gf1D2Jcx6kxVlOg_!!**********-0-cib.jpg" usemap="#_sdmap_13"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01oHV5UY2Jcx6nDyLLY_!!**********-0-cib.jpg" usemap="#_sdmap_14"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01DaPkpR2Jcx6kS9orN_!!**********-0-cib.jpg" usemap="#_sdmap_15"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01ff0WKB2Jcx6mnePNR_!!**********-0-cib.jpg" usemap="#_sdmap_16"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01upGyFP2Jcx6g5TQgH_!!**********-0-cib.jpg" usemap="#_sdmap_17"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01lLR6zp2Jcx6g5TIMy_!!**********-0-cib.jpg" usemap="#_sdmap_18"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN018PuscT2Jcx6mndbSj_!!**********-0-cib.jpg" usemap="#_sdmap_19"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01wnGinW2Jcx6mfEjct_!!**********-0-cib.jpg" usemap="#_sdmap_20"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01olW3we2Jcx6kSDNGz_!!**********-0-cib.jpg" usemap="#_sdmap_21"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01CRd5KP2Jcx6kSBpcr_!!**********-0-cib.jpg" usemap="#_sdmap_22"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01YKQyzU2Jcx6my5TQi_!!**********-0-cib.jpg" usemap="#_sdmap_23"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01fz4sYq2Jcx6my60gm_!!**********-0-cib.jpg" usemap="#_sdmap_24"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01fV4WK92Jcx6kSBA52_!!**********-0-cib.jpg" usemap="#_sdmap_25"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01TP0fKF2Jcx6lzn5WQ_!!**********-0-cib.jpg" usemap="#_sdmap_26"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN017OccXV2Jcx6mfG4pe_!!**********-0-cib.jpg" usemap="#_sdmap_27"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01xN6mEH2Jcx6mA4BAN_!!**********-0-cib.jpg" usemap="#_sdmap_28"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01hKxFWb2Jcx6o0lILn_!!**********-0-cib.jpg" usemap="#_sdmap_29"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN017LsOrU2Jcx6mA0xXa_!!**********-0-cib.jpg" usemap="#_sdmap_30"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01c2pSiM2Jcx7nR8tQF_!!**********-0-cib.jpg" usemap="#_sdmap_31"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01kUiBK62Jcx7hqkFOr_!!**********-0-cib.jpg" usemap="#_sdmap_32"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01b7LZ6a2Jcx7nPX8hJ_!!**********-0-cib.jpg" usemap="#_sdmap_33"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN014A61bq2Jcx7mcqx76_!!**********-0-cib.jpg" usemap="#_sdmap_34"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01opFF6n2Jcx7oK4Mjf_!!**********-0-cib.jpg" usemap="#_sdmap_35"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN011z3hCK2Jcx7mfwI8E_!!**********-0-cib.jpg" usemap="#_sdmap_36"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01F3z7kN2Jcx7orHzq0_!!**********-0-cib.jpg" usemap="#_sdmap_37"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Eg6Q8g2Jcx7nO846l_!!**********-0-cib.jpg" usemap="#_sdmap_38"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01TbpmNY2Jcx7mC0UBg_!!**********-0-cib.jpg" usemap="#_sdmap_39"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01LwAglZ2Jcx7maw7tu_!!**********-0-cib.jpg" usemap="#_sdmap_40"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01mhjxh32Jcx7neOPZs_!!**********-0-cib.jpg" usemap="#_sdmap_41"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01VwHbIY2Jcx7nN9pxQ_!!**********-0-cib.jpg" usemap="#_sdmap_42"/></div>, mainVideo=null, detailVideo=null, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN014A61bq2Jcx7mcqx76_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01vqWBww2Jcx6mXdUSA_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01rN2HEc2Jcx6fpLfMB_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01pWDimB2Jcx6nS9L8D_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01sN0Iwp2Jcx6ljd6dH_!!**********-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=8244, attributeName=品牌类型, value=国货品牌, attributeNameTrans=Brand type, valueTrans=Domestic brand), GoodsDetailResponse.ProductAttribute(attributeId=7869, attributeName=是否进口, value=否, attributeNameTrans=Whether to import, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=346, attributeName=产地, value=广州, attributeNameTrans=Origin, valueTrans=Guangzhou), GoodsDetailResponse.ProductAttribute(attributeId=9722, attributeName=商品条形码, value=*************, attributeNameTrans=Product barcode, valueTrans=*************), GoodsDetailResponse.ProductAttribute(attributeId=8701, attributeName=产品系列, value=迷迭香出口洗发水, attributeNameTrans=Product series, valueTrans=Rosemary export shampoo), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=是否促销装, value=否, attributeNameTrans=Whether it is promotional wear, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=8803, attributeName=适用发质, value=通用, attributeNameTrans=Suitable for hair type, valueTrans=General), GoodsDetailResponse.ProductAttribute(attributeId=7101, attributeName=适用人群, value=通用, attributeNameTrans=Applicable people, valueTrans=General), GoodsDetailResponse.ProductAttribute(attributeId=3114, attributeName=香型, value=草本香, attributeNameTrans=Fragrance, valueTrans=Herbal fragrance), GoodsDetailResponse.ProductAttribute(attributeId=1398, attributeName=货号, value=0121/0107/0114, attributeNameTrans=Item number, valueTrans=0121/0107/0114), GoodsDetailResponse.ProductAttribute(attributeId=7507, attributeName=箱装数量, value=380pcs, attributeNameTrans=Box quantity, valueTrans=380pcs), GoodsDetailResponse.ProductAttribute(attributeId=2175, attributeName=品名, value=跨境出口, attributeNameTrans=Product name, valueTrans=Cross-border export), GoodsDetailResponse.ProductAttribute(attributeId=119140111, attributeName=特殊用途化妆品, value=否, attributeNameTrans=Special purpose cosmetics, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=8795, attributeName=功效, value=去头屑, attributeNameTrans=Effect, valueTrans=Anti dandruff), GoodsDetailResponse.ProductAttribute(attributeId=8795, attributeName=功效, value=滋润营养, attributeNameTrans=Effect, valueTrans=Moisturizing and nutritious), GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=LMDKCOCO, attributeNameTrans=Brand, valueTrans=LMDKCOCO), GoodsDetailResponse.ProductAttribute(attributeId=1141, attributeName=功能, value=去屑滋润, attributeNameTrans=Function, valueTrans=Anti-dandruff and moisturizing), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000m hair mask（迷迭香薄荷）0121, attributeNameTrans=Specification type, valueTrans=1000m hair mask (rosemary mint) 0121), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml shampoo（迷迭香薄荷）0107, attributeNameTrans=Specification type, valueTrans=750ml shampoo (rosemary mint) 0107), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  conditioner（迷迭香薄荷）0114, attributeNameTrans=Specification type, valueTrans=750ml conditioner (rosemary mint) 0114), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  shampoo（蛋白质）0952, attributeNameTrans=Specification type, valueTrans=750ml shampoo (protein) 0952), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  conditioner（蛋白质）0969, attributeNameTrans=Specification type, valueTrans=750ml conditioner (protein) 0969), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000ml hair mask（蛋白质）0998, attributeNameTrans=Specification type, valueTrans=1000ml hair mask (protein) 0998), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  shampoo（角蛋白）0921, attributeNameTrans=Specification type, valueTrans=750ml shampoo (glass) 0921), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  conditioner（角蛋白）0938, attributeNameTrans=Specification type, valueTrans=750ml conditioner (glass) 0938), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000ml  hair mask（角蛋白）0981, attributeNameTrans=Specification type, valueTrans=1000ml hair mask (glass) 0981), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml shampoo（迷迭香）0891, attributeNameTrans=Specification type, valueTrans=750ml shampoo (rosemary) 0891), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml conditioner（迷迭香）0907, attributeNameTrans=Specification type, valueTrans=750ml conditioner (rosemary) 0907), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000ml hair mask（迷迭香）0974, attributeNameTrans=Specification type, valueTrans=1000ml hair mask (rosemary) 0974), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  shampoo（鱼子酱）0138, attributeNameTrans=Specification type, valueTrans=750ml shampoo (caviar) 0138), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=750ml  conditioner（鱼子酱）0145, attributeNameTrans=Specification type, valueTrans=750ml conditioner (caviar) 0145), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000ml  hair mask（鱼子酱）0152, attributeNameTrans=Specification type, valueTrans=1000ml hair mask (caviar) 0152), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=800ml shampoo（角蛋白）0149, attributeNameTrans=Specification type, valueTrans=800ml shampoo (glass) 0149), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=800ml conditioner（角蛋白）0156, attributeNameTrans=Specification type, valueTrans=800ml conditioner (glass) 0156), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000ml Keratin hair mask（角蛋白）0163, attributeNameTrans=Specification type, valueTrans=1000ml keratin hair mask 0163), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=800ml Nano protein keratin shampoo(纳米蛋白角蛋白)0118, attributeNameTrans=Specification type, valueTrans=800ml nano protein keratin shampoo (nano protein keratin) 0118), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=800ml Nano protein keratin conditioner(纳米蛋白角蛋白)012, attributeNameTrans=Specification type, valueTrans=800ml nano protein keratin conditioner (nano protein keratin) 012), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=规格类型, value=1000ml Nano protein keratin hair mask(纳米蛋白角蛋白)0132, attributeNameTrans=Specification type, valueTrans=1000ml nano protein keratin hair mask (nano protein keratin) 0132), GoodsDetailResponse.ProductAttribute(attributeId=1627207, attributeName=颜色分类, value=绿色, attributeNameTrans=Color classification, valueTrans=Green), GoodsDetailResponse.ProductAttribute(attributeId=7119, attributeName=上市时间, value=2024, attributeNameTrans=Time on the market, valueTrans=2024), GoodsDetailResponse.ProductAttribute(attributeId=1654, attributeName=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, attributeNameTrans=Net content, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=ebay, attributeNameTrans=Main downstream platforms, valueTrans=ebay), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=亚马逊, attributeNameTrans=Main downstream platforms, valueTrans=Amazon), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=wish, attributeNameTrans=Main downstream platforms, valueTrans=wish), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=速卖通, attributeNameTrans=Main downstream platforms, valueTrans=Aliexpress), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=独立站, attributeNameTrans=Main downstream platforms, valueTrans=Independent station), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=LAZADA, attributeNameTrans=Main downstream platforms, valueTrans=LAZADA), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=非洲, attributeNameTrans=Main sales area, valueTrans=Africa), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=欧洲, attributeNameTrans=Main sales area, valueTrans=Europe), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=南美, attributeNameTrans=Main sales area, valueTrans=South america), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=东南亚, attributeNameTrans=Main sales area, valueTrans=Southeast asia), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=北美, attributeNameTrans=Main sales area, valueTrans=North america), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=东北亚, attributeNameTrans=Main sales area, valueTrans=Northeast asia), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=中东, attributeNameTrans=Main sales area, valueTrans=Middle east), GoodsDetailResponse.ProductAttribute(attributeId=193290003, attributeName=有可授权的自有品牌, value=是, attributeNameTrans=There are licensable private brands, valueTrans=Is), GoodsDetailResponse.ProductAttribute(attributeId=182282223, attributeName=是否跨境出口专供货源, value=是, attributeNameTrans=Whether to exclusively supply goods for cross-border export, valueTrans=Is), GoodsDetailResponse.ProductAttribute(attributeId=364, attributeName=产品类别, value=洗发水, attributeNameTrans=Product category, valueTrans=Shampoo), GoodsDetailResponse.ProductAttribute(attributeId=366, attributeName=产品名称, value=跨境外贸洗发, attributeNameTrans=Product name, valueTrans=Cross-border foreign trade shampoo)], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=10.0, jxhyPrice=null, skuId=*************, specId=7ed4eb606473cbf5c8c0eb97f5a57be5, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000m hair mask（迷迭香薄荷）0121, valueTrans=1000m hair mask (rosemary mint) 0121, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01VypbkH2Jcx6m4rnea_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=10.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=10)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=629b47c81dbd4203dcbd16d63c751faa, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml shampoo（迷迭香薄荷）0107, valueTrans=750ml shampoo (rosemary mint) 0107, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN013Yhfk92Jcx6m4vh28_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=787de974e53981be6a96233a5db7a4f3, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  conditioner（迷迭香薄荷）0114, valueTrans=750ml conditioner (rosemary mint) 0114, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01TVzgBA2Jcx6lpRoIv_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=192, price=11.0, jxhyPrice=null, skuId=*************, specId=5a180a428f2154fa83db4d96b62891b5, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  shampoo（蛋白质）0952, valueTrans=750ml shampoo (protein) 0952, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01t6SFZP2Jcx7p528hC_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=c016d394f9f3061d4af07507e4e07e93, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  conditioner（蛋白质）0969, valueTrans=750ml conditioner (protein) 0969, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01mhjxh32Jcx7neOPZs_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=192, price=10.0, jxhyPrice=null, skuId=*************, specId=77e9b78081de7c55438625900b2e1666, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000ml hair mask（蛋白质）0998, valueTrans=1000ml hair mask (protein) 0998, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01LwAglZ2Jcx7maw7tu_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=10.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=10)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=a737f9615647465ee235b04dcad8f083, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  shampoo（角蛋白）0921, valueTrans=750ml shampoo (glass) 0921, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01NVDXH02Jcx7opvCvJ_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=3b264558904936ecc3bfc7712db4b49f, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  conditioner（角蛋白）0938, valueTrans=750ml conditioner (glass) 0938, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01YmxCD02Jcx7oIRnUF_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=10.0, jxhyPrice=null, skuId=*************, specId=3b16940ad58b8c9a904c3f4b20117311, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000ml  hair mask（角蛋白）0981, valueTrans=1000ml hair mask (glass) 0981, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01TbpmNY2Jcx7mC0UBg_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=10.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=10)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=d88a0c3062c48301aa14d8ae4d95845e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml shampoo（迷迭香）0891, valueTrans=750ml shampoo (rosemary) 0891, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01h8aScw2Jcx7oSkrfW_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=11.0, jxhyPrice=null, skuId=*************, specId=9b9254076e6ecd3227f450b14b9dd720, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml conditioner（迷迭香）0907, valueTrans=750ml conditioner (rosemary) 0907, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01hJ13Fd2Jcx7otujXW_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=10.0, jxhyPrice=null, skuId=*************, specId=800f863d6ec7ef9aa8a050d180424673, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000ml hair mask（迷迭香）0974, valueTrans=1000ml hair mask (rosemary) 0974, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01MncMvr2Jcx7oTxNc3_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=10.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=10)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=192, price=11.0, jxhyPrice=null, skuId=*************, specId=f506ba9c3c6a37fbd1129f6aa880fa4c, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  shampoo（鱼子酱）0138, valueTrans=750ml shampoo (caviar) 0138, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN011z3hCK2Jcx7mfwI8E_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=192, price=11.0, jxhyPrice=null, skuId=*************, specId=92d9e9428136d07a8728ccde5ff4e290, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=750ml  conditioner（鱼子酱）0145, valueTrans=750ml conditioner (caviar) 0145, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN014A61bq2Jcx7mcqx76_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=11.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=11)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=168, price=10.0, jxhyPrice=null, skuId=*************, specId=3c075ebfdd021eb6da2f7451e9a278c6, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000ml  hair mask（鱼子酱）0152, valueTrans=1000ml hair mask (caviar) 0152, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01opFF6n2Jcx7oK4Mjf_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=10.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=10)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=16.5, jxhyPrice=null, skuId=*************, specId=2659312e38efa1718634a6f6f8b4bbb8, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=800ml shampoo（角蛋白）0149, valueTrans=800ml shampoo (glass) 0149, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01QHXZNQ2Jcx7oSokv4_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=16.5, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=16.5)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=16.5, jxhyPrice=null, skuId=*************, specId=a60b449e70bd40e98911908268cd56bd, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=800ml conditioner（角蛋白）0156, valueTrans=800ml conditioner (glass) 0156, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01qN4wsA2Jcx7mhw9T3_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=16.5, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=16.5)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=15.3, jxhyPrice=null, skuId=*************, specId=b163c2f06107a6745cb0cbe3a4d81b36, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000ml Keratin hair mask（角蛋白）0163, valueTrans=1000ml keratin hair mask 0163, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01ScAant2Jcx7hrYSjQ_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=15.3, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=15.3)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=16.5, jxhyPrice=null, skuId=*************, specId=b8fe487745e61fb16e847df3971a88ae, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=800ml Nano protein keratin shampoo(纳米蛋白角蛋白)0118, valueTrans=800ml nano protein keratin shampoo (nano protein keratin) 0118, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01c2pSiM2Jcx7nR8tQF_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=16.5, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=16.5)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=16.5, jxhyPrice=null, skuId=*************, specId=19a2bcbafba373de700efde7a5f7d3cd, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=800ml Nano protein keratin conditioner(纳米蛋白角蛋白)012, valueTrans=800ml nano protein keratin conditioner (nano protein keratin) 012, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01kUiBK62Jcx7hqkFOr_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=16.5, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=16.5)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=240, price=15.3, jxhyPrice=null, skuId=*************, specId=07769a3ded1ef0e2acfa86ab54b00c68, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1654, attributeName=净含量, attributeNameTrans=Net content, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明, valueTrans=Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=*********, attributeName=规格类型, attributeNameTrans=Specification type, value=1000ml Nano protein keratin hair mask(纳米蛋白角蛋白)0132, valueTrans=1000ml nano protein keratin hair mask (nano protein keratin) 0132, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01b7LZ6a2Jcx7nPX8hJ_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=15.3, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=15.3))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=4776, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=24, price=10.0, promotionPrice=null)], quoteType=1, unitInfo=GoodsDetailResponse.UnitInfo(unit=瓶, transUnit=Bottle), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=false, startQuantity=24, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=浙江省金华市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=null, skuShippingInfoList=[GoodsDetailResponse.SkuShippingInfo(specId=7ed4eb606473cbf5c8c0eb97f5a57be5, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100), GoodsDetailResponse.SkuShippingInfo(specId=629b47c81dbd4203dcbd16d63c751faa, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=787de974e53981be6a96233a5db7a4f3, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=5a180a428f2154fa83db4d96b62891b5, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=c016d394f9f3061d4af07507e4e07e93, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=77e9b78081de7c55438625900b2e1666, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100), GoodsDetailResponse.SkuShippingInfo(specId=a737f9615647465ee235b04dcad8f083, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=3b264558904936ecc3bfc7712db4b49f, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=3b16940ad58b8c9a904c3f4b20117311, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100), GoodsDetailResponse.SkuShippingInfo(specId=d88a0c3062c48301aa14d8ae4d95845e, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=9b9254076e6ecd3227f450b14b9dd720, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=800f863d6ec7ef9aa8a050d180424673, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100), GoodsDetailResponse.SkuShippingInfo(specId=f506ba9c3c6a37fbd1129f6aa880fa4c, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=92d9e9428136d07a8728ccde5ff4e290, skuId=*************, width=0.0, length=0.0, height=0.0, weight=850), GoodsDetailResponse.SkuShippingInfo(specId=3c075ebfdd021eb6da2f7451e9a278c6, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100), GoodsDetailResponse.SkuShippingInfo(specId=2659312e38efa1718634a6f6f8b4bbb8, skuId=*************, width=0.0, length=0.0, height=0.0, weight=900), GoodsDetailResponse.SkuShippingInfo(specId=a60b449e70bd40e98911908268cd56bd, skuId=*************, width=0.0, length=0.0, height=0.0, weight=900), GoodsDetailResponse.SkuShippingInfo(specId=b163c2f06107a6745cb0cbe3a4d81b36, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100), GoodsDetailResponse.SkuShippingInfo(specId=b8fe487745e61fb16e847df3971a88ae, skuId=*************, width=0.0, length=0.0, height=0.0, weight=900), GoodsDetailResponse.SkuShippingInfo(specId=19a2bcbafba373de700efde7a5f7d3cd, skuId=*************, width=0.0, length=0.0, height=0.0, weight=900), GoodsDetailResponse.SkuShippingInfo(specId=07769a3ded1ef0e2acfa86ab54b00c68, skuId=*************, width=0.0, length=0.0, height=0.0, weight=1100)], skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.9, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.9, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.9, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.9, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=0.85, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.1, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=false, sellerOpenId=BBBJufCmGTclHQmI_NXdlqAeA, minOrderQuantity=24, batchNumber=24, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=false), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=false), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=false), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=null, productCargoNumber=0121/0107/0114, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=4, compositeServiceScore=5.0, logisticsExperienceScore=4.5, disputeComplaintScore=4.0, offerExperienceScore=4.0, consultingExperienceScore=3.5, repeatPurchasePercent=0.6888060018187283, afterSalesExperienceScore=4.0, collect30DayWithin48HPercent=1.0, qualityRefundWithin30Day=0.0006615006615006615), soldOut=202, channelPrice=null, promotionModel=null, tradeScore=0.0, topCategoryId=130822220, secondCategoryId=10313, thirdCategoryId=1047065, sellingPoint=null, offerIdentities=[powerful_merchants, tp_member], createDate=2025-05-27 14:32:42, isSelect=false, certificateList=[], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-08-12 14:35:44 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.d.c.product.PdcProductDetailConvertMapping - [1107315292062654464]:[0] 生成新ID: *************** 对应平台商品ID: ************
2025-08-12 14:35:44 INFO  [naya-task-pool3] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - [1107315292062654464]:[0] 开始解密旺旺昵称, openUid: BBBJufCmGTclHQmI_NXdlqAeA
2025-08-12 14:35:44 INFO  [naya-task-pool3] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107315292062654464]:[0] 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1754980544759access_token5fd99355-518b-47a6-b83e-0503223e0665openUidBBBJufCmGTclHQmI_NXdlqAeA 签名: E979F88A380BDCB840269486958B484F1CC9C802
2025-08-12 14:35:44 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] com.fulfillmen.shop.common.util.MetaInfoHashUtils - [1107315292062654464]:[0] 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=20097f5457e6374c7906d361c0c04054, metaInfo长度=36538
2025-08-12 14:35:45 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107315292062654464]:[0] 商品详情同步到数据库完成, id: ************
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107315292062654464]:[0] 商品详情更新到缓存完成, id: ************
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107315292062654464]:[0] 自动同步功能已关闭，跳过事件发布
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107315292062654464]:[0] 获取或同步产品数据: platformProductId=************
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107315292062654464]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107315292062654464]:[0] 从PdcProductMapping同步产品数据: platformProductId=************
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107315292062654464]:[0] 开始同步产品数据，platformProductId: ************
2025-08-12 14:35:46 DEBUG [naya-task-pool4] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107315292062654464]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107315292062654464]:[0] SPU创建成功，spuId: 728176011526277
2025-08-12 14:35:46 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107315292062654464]:[0] 多规格SKU创建成功，数量: 21
2025-08-12 14:35:46 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107315292062654464]:[0] 事务执行过程中发生异常: orderId=2873749476412540788
java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-08-12 14:35:47 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107315292062654464]:[0] 订单webhook消息处理失败: orderId=2873749476412540788, msgId=144548371008, messageType=ORDER_BUYER_VIEW_ORDER_PAY, error=订单数据补齐事务执行失败
com.fulfillmen.starter.core.exception.BusinessException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:242)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	... 115 common frames omitted
2025-08-12 14:35:47 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107315292062654464]:[0] 发布订单处理失败事件: orderId=2873749476412540788, error=订单数据补齐事务执行失败
2025-08-12 14:35:47 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107315292062654464]:[0] 订单webhook消息处理失败: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873749476412540788, error=订单数据补齐事务执行失败
com.fulfillmen.starter.core.exception.BusinessException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:242)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	... 115 common frames omitted
2025-08-12 14:35:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.a.threadpool.AsyncAutoConfiguration - [1107315292062654464]:[0] [Fulfillmen Starter] - Auto Configuration 'AsyncConfigurer' completed initialization.
2025-08-12 14:35:47 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.alibaba.webhook.AbstractTypedMessageHandler - [1107315292062654464]:[0] 消息处理异常: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY
com.fulfillmen.starter.core.exception.BusinessException: 订单数据补齐事务执行失败
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:242)
	at org.springframework.transaction.support.TransactionTemplate.execute(TransactionTemplate.java:140)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncMissingOrderData(OrderDataSyncServiceImpl.java:199)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.syncAndCompleteOrderData(OrderDataSyncServiceImpl.java:174)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderWebhookServiceImpl.processOrderWebhook(OrderWebhookServiceImpl.java:122)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:131)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "com.fulfillmen.shop.domain.dto.TzProductSkuDTO.getImage()" because "sku" is null
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createOrderItemData(OrderDataSyncServiceImpl.java:478)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.createAndSaveOrderItems(OrderDataSyncServiceImpl.java:284)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.service.impl.OrderDataSyncServiceImpl.lambda$syncMissingOrderData$2(OrderDataSyncServiceImpl.java:225)
	... 115 common frames omitted
2025-08-12 14:35:47 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107315292062654464]:[0] Webhook消息处理完成，处理时间: 8328ms，处理结果数量: 1
2025-08-12 14:35:47 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107315292062654464]:[0] [POST] /alibaba/callback 200 8328ms
2025-08-12 14:35:47 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107315292062654464]:[0] 清理增强租户上下文
2025-08-12 14:35:47 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107315292062654464]:[0] 过滤器清理租户上下文完成
2025-08-12 14:46:46 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-12 14:46:46 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 6225 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-08-12 14:46:46 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-08-12 14:46:46 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "sealosDev"
2025-08-12 14:46:47 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-12 14:46:47 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-12 14:46:47 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
2025-08-12 14:46:47 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-08-12 14:46:47 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:46:47 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:46:47 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-08-12 14:46:47 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, sys_alibaba_callback_logs, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-08-12 14:46:47 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-08-12 14:46:48 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2432 ms
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:46:48 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:46:48 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-08-12 14:46:48 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-08-12 14:46:48 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-08-12 14:46:48 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-08-12 14:46:49 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-08-12 14:46:49 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-08-12 14:46:50 INFO  [redisson-netty-1-7] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:46:59 INFO  [redisson-netty-1-20] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:46:59 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-08-12 14:46:59 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-08-12 14:46:59 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-08-12 14:46:59 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-08-12 14:46:59 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[http://wms.fulfillmen.com], skipSslVerification=[false]
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 开始注册订单事件处理器...
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCompletionProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SUCCESS
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderConfirmationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCreationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_BUYER_MAKE
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BATCH_PAY
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPriceModificationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_PART_PART_SENDGOODS
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 订单事件处理器注册完成，共注册 8 个处理器，支持 13 种消息类型。
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [MANUAL, AUTO, DISABLED]
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - 支持的消息类型: [ORDER_BUYER_VIEW_BUYER_MAKE, ORDER_BUYER_VIEW_ORDER_PAY, ORDER_BATCH_PAY, ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS, ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, ORDER_BUYER_VIEW_ORDER_SUCCESS, ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY]
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.autoconfigure.ValidatorAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Validator' completed initialization.
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1023150a
2025-08-12 14:47:00 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-08-12 14:47:00 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:6225, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=0, lastTimeStamp=1754981220985}] - instanceId:[InstanceId{instanceId=**************:6225, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - TaskScheduler extension configuration applied: poolSize=12, threadNamePrefix=scheduling-, rejectedPolicy=CALLER_RUNS
2025-08-12 14:47:01 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-12 14:47:01 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 57 ms
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 16.73 seconds (process running for 17.885)
2025-08-12 14:47:02 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-08-12 14:47:02 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-08-12 14:47:02 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-08-12 14:47:02 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-12 14:47:02 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-12 14:47:02 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-12 14:47:02 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-08-12 14:47:02 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-12 14:47:02 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-12 14:47:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-08-12 14:47:03 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-08-12 14:47:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-12 14:47:03 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-12 14:47:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-12 14:47:03 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-12 14:47:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-08-12 14:47:04 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-08-12 14:47:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-12 14:47:04 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-12 14:47:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-12 14:47:04 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-12 14:47:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-08-12 14:47:04 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-08-12 14:47:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-12 14:47:04 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-12 14:47:04 DEBUG [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-12 14:47:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-08-12 14:47:04 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-12 14:47:04 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-12 14:47:04 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-08-12 14:47:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-12 14:47:04 INFO  [RMI TCP Connection(6)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 14:47:04 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 14:47:04 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-12 14:47:04 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-12 14:47:06 INFO  [RMI TCP Connection(6)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@1bae0b24
2025-08-12 14:47:06 INFO  [RMI TCP Connection(6)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107318290608332800]:[0] 从缓存中获取租户ID: 10000
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107318290608332800]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107318290608332800]:[0] 租户缓存命中: 10000
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107318290608332800]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107318290608332800]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107318290608332800]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 14:47:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107318290608332800]:[0] [POST] /alibaba/callback
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107318290608332800]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 14:47:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107318290608332800]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"2873749476412540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":2873749476412540788,"sellerMemberId":"b2b-**********2e7a2","msgSendTime":"2025-08-12 11:05:55"},"gmtBorn":1754967955151,"msgId":144548371008,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: E2E11A0298D66A0E97E8ED75DAA8D40AED28F9B6
2025-08-12 14:47:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107318290608332800]:[0] 收到阿里巴巴webhook回调，消息长度: 337
2025-08-12 14:47:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107318290608332800]:[0] 开始处理webhook消息, 消息长度: 337
2025-08-12 14:47:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107318290608332800]:[0] 解析到 1 个消息事件
2025-08-12 14:47:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107318290608332800]:[0] 接收到订单webhook消息: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873749476412540788, status=waitsellersend
2025-08-12 14:47:34 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.i.SysAlibabaCallbackLogsRepositoryImpl - [1107318290608332800]:[0] 创建Webhook处理日志异常(含事件与订单)
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
### The error may exist in com/fulfillmen/shop/dao/mapper/SysAlibabaCallbackLogsMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_alibaba_callback_logs (id, metadata, order_id, event_type, received_timestamp, process_status, gmt_created, gmt_modified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
; Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy150.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl.createProcessingLog(SysAlibabaCallbackLogsRepositoryImpl.java:87)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl$$SpringCGLIB$$0.createProcessingLog(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:126)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy231.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy230.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 130 common frames omitted
2025-08-12 14:47:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107318290608332800]:[0] 开始处理订单webhook消息: orderId=2873749476412540788, msgId=144548371008, messageType=ORDER_BUYER_VIEW_ORDER_PAY, currentStatus=waitsellersend
2025-08-12 14:47:34 INFO  [naya-task-pool2] [tid::uId::ip::os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107318290608332800]:[0] 开始WMS订单详情查询，查询条件: {"orderId":"2873749476412540788"}
2025-08-12 14:47:34 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1107318290608332800]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2873749476412540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-08-12 14:47:34 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107318290608332800]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1754981254318access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2873749476412540788webSite1688 签名: E1419736AC02327EA6C80387BFFA74488B52226C
2025-08-12 14:47:35 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107318290608332800]:[0] 订单数据获取完成: orderId=2873749476412540788, alibabaOrderDetail=true, wmsOrderDetails=1
2025-08-12 14:47:35 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 开始检查订单数据完整性: orderDetail={"baseInfo":{"id":2873749476412540788,"businessType":"cb","refund":0,"shippingFee":0,"status":"waitsellersend","totalAmount":1775,"discount":0,"buyerContact":{"phone":"86-752-2313067","name":"汤维政","imInPlatform":"惠州中田贸易","companyName":"惠州市中田贸易有限公司","mobile":"***********"},"sellerContact":{"phone":"86-0579-12345678","name":"钟少燕","imInPlatform":"宏培电子商务","companyName":"义乌市宏培化妆品有限公司","mobile":"***********","shopName":"义乌市宏培化妆品有限公司"},"tradeType":"50060","refundPayment":0,"idOfStr":"2873749476412540788","buyerID":"b2b-2207416548807a4d12","createTime":"2025-08-11 18:03:15","modifyTime":"2025-08-12 11:05:55","sellerID":"b2b-**********2e7a2","payTime":"2025-08-12 11:05:55","alipayTradeId":"2025081222001846301445063864","sumProductPayment":1776,"flowTemplateCode":"assureTrade","sellerOrder":false,"buyerLoginId":"惠州中田贸易","sellerLoginId":"宏培电子商务","closeOperateType":"","couponFee":0,"receiverInfo":{"toFullName":"中田 12092","toDivisionCode":"441302","toPost":"516000","toArea":"广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092","toMobile":"***********"},"tradeTypeDesc":"担保交易","payChannelList":["跨境宝2.0"],"tradeTypeCode":"assureTrade","payTimeout":432000,"payTimeoutType":0,"payChannelCodeList":["kjpayV2"],"outOrderId":"12092","stepPayAll":false,"newStepOrderList":[{"gmtStart":"2025-08-11 18:03:16","gmtPay":"2025-08-12 11:05:55","stepNo":1,"lastStep":true,"stepName":"全款交易","activeStatus":1,"payStatus":2,"logisticsStatus":1,"payFee":1775,"paidFee":1775,"adjustFee":0,"discountFee":1,"postFee":0,"paidPostFee":0}],"overSeaOrder":false},"orderBizInfo":{"odsCyd":false,"creditOrder":false,"dropshipping":false,"shippingInsurance":"givenByAnXinGou"},"tradeTerms":[{"payStatus":"2","payTime":"2025-08-12 11:05:55","payWay":"13","phasAmount":1775,"phase":*****************,"cardPay":false,"expressPay":false,"payWayDesc":"支付平台"}],"productItems":[{"itemAmount":719.6,"name":"跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask","price":10,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476413540788","quantity":72,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":2873749476413540788,"type":"common","unit":"瓶","productCargoNumber":"0121/0107/0114","skuInfos":[{"name":"规格类型","value":"1000ml  hair mask（鱼子酱）0152"},{"name":"净含量","value":"产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明"}],"entryDiscount":0,"specId":"3c075ebfdd021eb6da2f7451e9a278c6","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"2873749476413540788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:03:16","gmtModified":"2025-08-12 11:05:55","gmtPayExpireTime":"2025-08-12 11:20:45","guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]},{"itemAmount":527.7,"name":"跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask","price":11,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476414540788","quantity":48,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":2873749476414540788,"type":"common","unit":"瓶","productCargoNumber":"0121/0107/0114","skuInfos":[{"name":"规格类型","value":"750ml  conditioner（鱼子酱）0145"},{"name":"净含量","value":"产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明"}],"entryDiscount":0,"specId":"92d9e9428136d07a8728ccde5ff4e290","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"2873749476414540788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:03:16","gmtModified":"2025-08-12 11:05:55","gmtPayExpireTime":"2025-08-12 11:20:45","guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]},{"itemAmount":527.7,"name":"跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask","price":11,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476415540788","quantity":48,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":2873749476415540788,"type":"common","unit":"瓶","productCargoNumber":"0121/0107/0114","skuInfos":[{"name":"规格类型","value":"750ml  shampoo（鱼子酱）0138"},{"name":"净含量","value":"产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明"}],"entryDiscount":0,"specId":"f506ba9c3c6a37fbd1129f6aa880fa4c","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"2873749476415540788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:03:16","gmtModified":"2025-08-12 11:05:55","gmtPayExpireTime":"2025-08-12 11:20:45","guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]}],"nativeLogistics":{"address":"江北 金泽物流园二期一号楼四楼-12092","area":"惠城区","areaCode":"441302","city":"惠州市","contactPerson":"中田 12092","mobile":"***********","province":"广东省","zip":"516000"},"orderRateInfo":{"buyerRateStatus":5,"sellerRateStatus":5},"extAttributes":[],"fromEncryptOrder":false}
2025-08-12 14:47:35 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 未找到供应商订单: orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2873749476412540788, idOfStr=2873749476412540788, businessType=cb, buyerId=b2b-2207416548807a4d12, completeTime=null, createTime=2025-08-11T18:03:15, modifyTime=2025-08-12T11:05:55, refund=0, sellerId=b2b-**********2e7a2, shippingFee=0, status=waitsellersend, totalAmount=1775, discount=0, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-0579-12345678, fax=null, email=null, name=钟少燕, imInPlatform=宏培电子商务, companyName=义乌市宏培化妆品有限公司, mobile=***********, address=null), shopName=义乌市宏培化妆品有限公司, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=null, payTime=2025-08-12T11:05:55, receivingTime=null, alipayTradeId=2025081222001846301445063864, sumProductPayment=1776, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=宏培电子商务, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=中田 12092, toDivisionCode=441302, toPost=516000, toArea=广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=***********, toPhone=null, toEmail=null, toTownCode=null), tradeTypeDesc=担保交易, payChannelList=[跨境宝2.0], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[kjpayV2], outOrderId=12092, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-08-11T18:03:16, gmtPay=2025-08-12T11:05:55, gmtEnd=null, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=2, logisticsStatus=1, payFee=1775, paidFee=1775, goodsFee=null, adjustFee=0, discountFee=1, postFee=0, paidPostFee=0)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=2, payTime=2025-08-12T11:05:55, payWay=13, phasAmount=1775, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=null, description=null, itemAmount=719.6, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=10, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01Jq3zgX1YuYPaf2x3s_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476413540788, quantity=72, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476413540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=1000ml  hair mask（鱼子酱）0152), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=3c075ebfdd021eb6da2f7451e9a278c6, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476413540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=null, description=null, itemAmount=527.7, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=11, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01rJdqQA1cqD7OlGB1o_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476414540788, quantity=48, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476414540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=750ml  conditioner（鱼子酱）0145), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=92d9e9428136d07a8728ccde5ff4e290, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476414540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)]), TradeProductItem(cargoNumber=null, description=null, itemAmount=527.7, name=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, price=11, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01hTEQWJ22sfFZbg1HU_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873749476415540788, quantity=48, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=2873749476415540788, type=common, unit=瓶, weight=null, weightUnit=null, productCargoNumber=0121/0107/0114, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格类型, value=750ml  shampoo（鱼子酱）0138), TradeProductItem.TradeSkuInfo(name=净含量, value=产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明)], entryDiscount=0, specId=f506ba9c3c6a37fbd1129f6aa880fa4c, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873749476415540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:03:16, gmtModified=2025-08-12T11:05:55, gmtCompleted=null, gmtPayExpireTime=2025-08-12T11:20:45, sharePostage=null, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)])], nativeLogistics=TradeNativeLogisticsInfo(address=江北 金泽物流园二期一号楼四楼-12092, area=惠城区, areaCode=441302, city=惠州市, contactPerson=中田 12092, fax=null, mobile=***********, province=广东省, telephone=null, zip=516000, logisticsItems=null, townCode=null, town=null), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null)
2025-08-12 14:47:35 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107318290608332800]:[0] 订单数据完整性检查结果: orderId=2873749476412540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-08-12 14:47:35 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 开始同步和补齐订单数据: orderId=2873749476412540788, needsSync=true
2025-08-12 14:47:35 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 开始补齐缺失的订单数据: orderId=2873749476412540788, missing=采购订单, 供应商订单, 订单项
2025-08-12 14:47:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 创建采购订单成功: orderId=2873749476412540788, purchaseOrderId=728178916765698
2025-08-12 14:47:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 创建供应商订单成功: orderId=2873749476412540788, supplierOrderCount=1
2025-08-12 14:47:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:47:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 获取或同步产品数据: platformProductId=************
2025-08-12 14:47:37 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:47:37 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 从PdcProductMapping同步产品数据: platformProductId=************
2025-08-12 14:47:37 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 开始同步产品数据，platformProductId: ************
2025-08-12 14:47:37 DEBUG [naya-task-pool3] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:47:37 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] SPU创建成功，spuId: 728178922184708
2025-08-12 14:47:37 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 多规格SKU创建成功，数量: 21
2025-08-12 14:48:31 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=55s84ms).
2025-08-12 14:53:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:53:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 获取或同步产品数据: platformProductId=************
2025-08-12 14:53:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 从现有SPU获取产品数据: spuId=728178922184708
2025-08-12 14:53:05 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=4m33s284ms).
2025-08-12 14:53:05 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:56:35 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-08-12 14:56:35 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-08-12 14:56:35 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 14:56:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-12 14:56:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-12 14:56:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-12 14:56:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=0, lastTimeStamp=1754981795939}] instanceId:[InstanceId{instanceId=**************:6225, stable=false}] @ namespace:[fulfillmen-shop].
2025-08-12 14:56:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:56:35 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-12 14:56:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 获取或同步产品数据: platformProductId=************
2025-08-12 14:56:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107318290608332800]:[0] 从现有SPU获取产品数据: spuId=728178922184708
2025-08-12 14:56:36 WARN  [HikariPool-1 housekeeper] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=3m31s3ms).
2025-08-12 14:56:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107318290608332800]:[0] 从缓存获取商品详情, id: ************
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 创建订单项成功: orderId=2873749476412540788, orderItemCount=3
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107318290608332800]:[0] 订单数据补齐完成: orderId=2873749476412540788, purchaseOrderId=728178916765698, supplierOrderCount=1, orderItemCount=3
2025-08-12 14:56:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.a.threadpool.AsyncAutoConfiguration - [1107318290608332800]:[0] [Fulfillmen Starter] - Auto Configuration 'AsyncConfigurer' completed initialization.
2025-08-12 14:56:36 INFO  [naya-task-pool5] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107318290608332800]:[0] 处理数据同步完成事件: orderId=2873749476412540788, messageType=ORDER_BUYER_VIEW_ORDER_PAY, eventTime=2025-08-12T14:56:36.563585
2025-08-12 14:56:36 INFO  [naya-task-pool5] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107318290608332800]:[0] 数据同步成功: orderId=2873749476412540788, purchaseOrderNo=N2508121447358531871, isDataSync=true, isCompatibility=false
2025-08-12 14:56:36 DEBUG [naya-task-pool5] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107318290608332800]:[0] 更新同步指标: orderId=2873749476412540788
2025-08-12 14:56:36 DEBUG [naya-task-pool5] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107318290608332800]:[0] 清理临时数据: orderId=2873749476412540788
2025-08-12 14:56:36 DEBUG [naya-task-pool5] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107318290608332800]:[0] 数据同步完成事件处理完成: orderId=2873749476412540788
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107318290608332800]:[0] 路由到具体的业务处理逻辑: orderId=2873749476412540788, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107318290608332800]:[0] 开始处理订单支付事件: orderId=2873749476412540788
2025-08-12 14:56:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107318290608332800]:[0] 更新本地订单状态为已支付: orderId=2873749476412540788
2025-08-12 14:56:36 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107318290608332800]:[0] 通知WMS订单已支付: orderId=2873749476412540788
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.webhook.handler.OrderContextRecord - [1107318290608332800]:[0] 重新 wms 同步商品信息: WmsPurchaseOrderDetailsItemRes(orderId=2873749476412540788, skuId=*************, enName=Cross-Border Shampoo Conditioner Hair Mask Full English E-Commerce Anti-Dandruff Moisturizing Export Shampoo Hair Mask, cnName=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, weight=0.000, quantity=48, variantId=f506ba9c3c6a37fbd1129f6aa880fa4c, productId=************, unitPrice=11.000, originUnitPrice=11.000, finalUnitPrice=11.000, subTotal=528.000, originSubTotalAmount=528.000, finalSubTotalAmount=1775.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN011z3hCK2Jcx7mfwI8E_!!**********-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=净含量:产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明;规格类型:750ml  shampoo（鱼子酱）0138;, skuAttribEn=Net content:Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer;Specification type:750ml shampoo (caviar) 0138;)
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.webhook.handler.OrderContextRecord - [1107318290608332800]:[0] 重新 wms 同步商品信息: WmsPurchaseOrderDetailsItemRes(orderId=2873749476412540788, skuId=*************, enName=Cross-Border Shampoo Conditioner Hair Mask Full English E-Commerce Anti-Dandruff Moisturizing Export Shampoo Hair Mask, cnName=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, weight=0.000, quantity=48, variantId=92d9e9428136d07a8728ccde5ff4e290, productId=************, unitPrice=11.000, originUnitPrice=11.000, finalUnitPrice=11.000, subTotal=528.000, originSubTotalAmount=528.000, finalSubTotalAmount=1775.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN014A61bq2Jcx7mcqx76_!!**********-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=净含量:产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明;规格类型:750ml  conditioner（鱼子酱）0145;, skuAttribEn=Net content:Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer;Specification type:750ml conditioner (caviar) 0145;)
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.webhook.handler.OrderContextRecord - [1107318290608332800]:[0] 重新 wms 同步商品信息: WmsPurchaseOrderDetailsItemRes(orderId=2873749476412540788, skuId=*************, enName=Cross-Border Shampoo Conditioner Hair Mask Full English E-Commerce Anti-Dandruff Moisturizing Export Shampoo Hair Mask, cnName=跨境洗发水护发素发膜全英文电商去屑滋润出口shampoo hair mask, weight=0.000, quantity=72, variantId=3c075ebfdd021eb6da2f7451e9a278c6, productId=************, unitPrice=10.000, originUnitPrice=10.000, finalUnitPrice=10.000, subTotal=720.000, originSubTotalAmount=720.000, finalSubTotalAmount=1775.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01opFF6n2Jcx7oK4Mjf_!!**********-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=净含量:产品仅供出口销售，禁止国内销售，购买等同默认公司免责申明;规格类型:1000ml  hair mask（鱼子酱）0152;, skuAttribEn=Net content:Products are only for export sales. domestic sales are prohibited. purchases are equivalent to defaulting to the company's disclaimer;Specification type:1000ml hair mask (caviar) 0152;)
2025-08-12 14:56:36 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107318290608332800]:[0] 开始更新 WMS 采购订单请求，采购单号: [{"purchaseNo":"C120922025081118031400005","orderId":2873749476412540788,"alibabaFinalAmount":1775,"alibabaTotalAmount":1775,"plusDiscount":1.000,"couponDiscount":0,"status":2,"paymentTime":"2025-08-12 11:05:55","orderDetails":[{"quantity":48,"variantId":"f506ba9c3c6a37fbd1129f6aa880fa4c","productId":"************","finalUnitPrice":10.99,"finalSubTotalAmount":527.7,"sku":"*************"},{"quantity":48,"variantId":"92d9e9428136d07a8728ccde5ff4e290","productId":"************","finalUnitPrice":10.99,"finalSubTotalAmount":527.7,"sku":"*************"},{"quantity":72,"variantId":"3c075ebfdd021eb6da2f7451e9a278c6","productId":"************","finalUnitPrice":9.99,"finalSubTotalAmount":719.6,"sku":"*************"}]}]
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107318290608332800]:[0] 订单支付事件处理完成: orderId=2873749476412540788
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107318290608332800]:[0] 订单消息处理完成: orderId=2873749476412540788, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107318290608332800]:[0] 订单webhook消息处理完成: orderId=2873749476412540788, msgId=144548371008, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107318290608332800]:[0] 订单webhook消息处理完成: msgId=144548371008, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873749476412540788
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107318290608332800]:[0] Webhook消息处理完成，处理时间: 543406ms，处理结果数量: 1
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107318290608332800]:[0] [POST] /alibaba/callback 200 543429ms
2025-08-12 14:56:37 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - [1107318290608332800]:[0] Graceful shutdown complete
2025-08-12 14:56:37 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-12 14:56:37 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107318290608332800]:[0] 清理增强租户上下文
2025-08-12 14:56:37 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107318290608332800]:[0] 过滤器清理租户上下文完成
2025-08-12 14:56:37 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-12 14:56:39 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 14:56:39 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-08-12 14:56:39 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-12 14:56:39 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-12 14:58:02 INFO  [background-preinit] [tid::uId::ip::os::browser:] org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 8.0.2.Final
2025-08-12 14:58:02 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Starting BootstrapApplication using Java 21.0.5 with PID 6915 (/Users/<USER>/work/fulfillmen/fulfillmen-shop/fulfillmen-shop-bootstrap/target/classes started by yzsama in /Users/<USER>/work/fulfillmen/fulfillmen-workspace)
2025-08-12 14:58:02 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Running with Spring Boot v3.3.11, Spring v6.1.19
2025-08-12 14:58:02 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - The following 1 profile is active: "sealosDev"
2025-08-12 14:58:03 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-12 14:58:03 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-12 14:58:03 INFO  [main] [tid::uId::ip::os::browser:] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 30 ms. Found 0 Redis repository interfaces.
2025-08-12 14:58:03 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.a.dao.SaTokenDaoRedissionConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken-Dao-Redis' completed initialization.
2025-08-12 14:58:04 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.JetCacheProxyConfiguration' of type [com.alicp.jetcache.anno.config.JetCacheProxyConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:58:04 WARN  [main] [tid::uId::ip::os::browser:] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.alicp.jetcache.anno.config.CommonConfiguration' of type [com.alicp.jetcache.anno.config.CommonConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected/applied to a currently created BeanPostProcessor [healthEndpointGroupsBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies/advisors. If this bean does not have to be post-processed, declare it with ROLE_INFRASTRUCTURE.
2025-08-12 14:58:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.MybatisPlusAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus' completed initialization.
2025-08-12 14:58:04 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.TenantConfig - 多租户拦截器已配置，忽略表: [tenant_commission_config, tenants, sys_alibaba_callback_logs, regions, tenant_plan_relation, sys_alibaba_category, subregions, sys_users, tenant_domains, sys_option, openapi_account, pdc_product_mapping, tenant_plans, tenants_info, tenant_files, sys_config, openapi_account_permission, openapi_interface, tenant_locales]
2025-08-12 14:58:04 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.d.m.a.i.MyBatisPlusIdGeneratorConfiguration - [Fulfillmen Starter] - Auto Configuration 'MyBatis Plus-IdGenerator-CosId' completed initialization.
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - Default locale initialized to: en_US
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.FulfillmenWebMvcConfig - MessageSource configured with basenames: [i18n/messages, i18n/openapi-message]
2025-08-12 14:58:05 WARN  [main] [tid::uId::ip::os::browser:] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3223 ms
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - === 过滤器配置信息 ===
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器: enabled=true, order=-2147483638
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器: enabled=true, order=-2147483628
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 过滤器优先级说明:
过滤器执行顺序（数值越小优先级越高）：

1. TRACE_FILTER     (-2147483648) - 链路跟踪过滤器，生成 TraceId
2. TENANT_FILTER    (-2147483638) - 租户过滤器，设置租户上下文
3. MDC_FILTER       (-2147483628) - MDC 过滤器，设置日志上下文
4. XSS_FILTER       (-2147483548) - XSS 过滤器，安全防护
5. CORS_FILTER      (-2147483538) - CORS 过滤器，跨域处理
6. LOG_FILTER       (2147483637) - 日志过滤器，记录请求响应

注意：
- 链路跟踪过滤器优先级最高，确保 TraceId 在整个请求生命周期中可用
- 租户过滤器在链路跟踪之后，为后续过滤器提供租户上下文
- MDC 过滤器在租户过滤器之后，可以获取到租户信息并设置到日志上下文
- 安全相关过滤器（XSS、CORS）在业务过滤器之前执行
- 日志过滤器优先级最低，记录完整的请求响应信息

2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - ===================
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - 租户过滤器已注册 [enabled=true, order=-2147483638, urlPatterns=[/*], excludePatterns=20]
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.config.filter.UnifiedFilterConfiguration - MDC 过滤器已注册 [enabled=true, order=-2147483628, urlPatterns=[/*], features=IP标准化,浏览器信息,操作系统信息]
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.trace.TraceAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Trace' completed initialization.
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.l.i.autoconfigure.LogAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Log-interceptor' completed initialization.
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?nayasource\.com
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http://localhost:[0-9]+
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http://localhost:[0-9]+
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?aliyuncs\.com
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.cors.RegexCorsConfiguration - [RegexCors] 添加正则匹配规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:58:05 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 已配置正则表达式跨域规则: http(s)?://(.+\.)?sealoshzh\.site
2025-08-12 14:58:05 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 检测到 allowCredentials=true 且使用通配符配置，这可能导致CORS错误
2025-08-12 14:58:05 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 建议：1) 设置 allowCredentials=false，或 2) 使用具体的域名列表替换通配符
2025-08-12 14:58:05 WARN  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 当前策略：保持 allowCredentials=true，但建议检查配置
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.web.autoconfigure.cors.CorsAutoConfiguration - [Fulfillmen Starter] - 跨域配置初始化完成 [常规域名: 0, 正则域名: 4, 允许凭证: true, 缓存时间: 3600s]
2025-08-12 14:58:05 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.w.a.cors.CorsConfigurationValidator - 
🔍 CORS配置分析报告:
==================================================
 1. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的HTTP方法
 2. ⚠️  安全建议: 生产环境中allowCredentials=true时建议明确指定允许的请求头
 3. 🔒 安全建议: 生产环境建议明确指定允许的HTTP方法，避免使用 '*'
 4. ✅ 最佳实践: 正在使用正则表达式匹配，这是推荐的域名配置方式
==================================================
📖 更多信息请参考: fulfillmen-starter-web/CORS-CONFIG-EXAMPLE.md

2025-08-12 14:58:06 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= caffeine
2025-08-12 14:58:06 INFO  [main] [tid::uId::ip::os::browser:] c.a.jetcache.autoconfigure.AbstractCacheAutoInit - init cache area default , type= redisson
2025-08-12 14:58:06 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.r.autoconfigure.RedissonAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Redisson' completed initialization.
2025-08-12 14:58:06 INFO  [main] [tid::uId::ip::os::browser:] org.redisson.Version - Redisson 3.45.1
2025-08-12 14:58:06 INFO  [redisson-netty-1-7] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 1 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:58:08 INFO  [redisson-netty-1-20] [tid::uId::ip::os::browser:] org.redisson.connection.ConnectionsHolder - 24 connections initialized for dbconn.sealoshzh.site/************:47683
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat period at 15 MINUTES
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 货币汇率缓存初始化完成
2025-08-12 14:58:08 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ThreadPool' completed initialization.
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - ThreadPool extension configuration applied: coreSize=12, maxSize=24, queueCapacity=2147483647, threadNamePrefix=naya-task-pool, rejectedPolicy=CALLER_RUNS
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - 🎯 自动同步功能初始化: 禁用
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS WebClient: baseUrl=[http://wms.fulfillmen.com], skipSslVerification=[false]
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.support.wms.autoconfigure.WmsAutoConfiguration - 初始化WMS声明式HTTP接口
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 开始注册订单事件处理器...
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BOPS_CLOSE
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_CLOSE
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCloseProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SELLER_CLOSE
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCompletionProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_SUCCESS
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderConfirmationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderCreationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_BUYER_MAKE
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPaymentProcessor] 支持消息类型 -> ORDER_BATCH_PAY
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderPriceModificationProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_BUYER_REFUND_IN_SALES
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderRefundProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ORDER_REFUND_AFTER_SALES
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 已注册处理器: [OrderShipmentProcessor] 支持消息类型 -> ORDER_BUYER_VIEW_PART_PART_SENDGOODS
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.p.OrderEventProcessorRegistry - 订单事件处理器注册完成，共注册 8 个处理器，支持 13 种消息类型。
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: fixed_window
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: sliding_window
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: token_bucket
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.util.RateLimitUtil - Loaded rate limit script for algorithm: leaky_bucket
2025-08-12 14:58:08 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已启用
2025-08-12 14:58:08 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.g.a.GraphicCaptchaAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Captcha-Graphic' completed initialization.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.manager.strategy.SyncStrategyFactory - 同步策略工厂初始化完成，可用策略: [DISABLED, AUTO, MANUAL]
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - 支持的消息类型: [ORDER_BUYER_VIEW_BUYER_MAKE, ORDER_BUYER_VIEW_ORDER_PAY, ORDER_BATCH_PAY, ORDER_BUYER_VIEW_ANNOUNCE_SENDGOODS, ORDER_BUYER_VIEW_PART_PART_SENDGOODS, ORDER_BUYER_VIEW_ORDER_COMFIRM_RECEIVEGOODS, ORDER_BUYER_VIEW_ORDER_SUCCESS, ORDER_BUYER_VIEW_ORDER_PRICE_MODIFY]
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 缓存配置详情: ProductDetailVO[本地:8min,远程:25min,容量:300]
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl - 🎯 ProductService优化缓存配置完成:
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── ProductDetailVO缓存: 本地8min/远程25min, 容量300, 键前缀: frontend:product:vo:
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 双层缓存策略: 本地内存 + Redis远程
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   ├── 空值缓存: 已启用，防止缓存穿透
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.frontend.service.impl.ProductServiceImpl -   └── 同步机制: 已启用，保证多实例一致性
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.autoconfigure.ValidatorAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Validator' completed initialization.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized CompositeLocaleResolver with default locale: en_US and supported locales: [en_US, zh_CN, zh_TW]
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Initialized LocaleChangeInterceptor with param name: lang
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.autoconfigure.SpringDocAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'ApiDoc' completed initialization.
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.a.s.autoconfigure.SaTokenAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'SaToken' completed initialization.
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.autoconfigure.mvc.WebMvcAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web MVC' completed initialization.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.a.web.servlet.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.w.a.response.GlobalResponseAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'Web-Global Response' completed initialization.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@5f18f9d2
2025-08-12 14:58:09 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.c.j.autoconfigure.JetCacheAutoConfiguration - [Fulfillmen Starter] - Auto Configuration 'JetCache' completed initialization.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote instanceId:[InstanceId{instanceId=**************:6915, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Distribute Remote machineState:[MachineState{machineId=2, lastTimeStamp=1754981889783}] - instanceId:[InstanceId{instanceId=**************:6915, stable=false}] - machineBit:[20] @ namespace:[fulfillmen-shop].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.order_no] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.order_no] jobSize:[0].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2].
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Submit jobId:[fulfillmen-shop.user_no] is bound to thread:[DefaultPrefetchWorker-2] start.
2025-08-12 14:58:09 INFO  [main] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Submit [fulfillmen-shop.user_no] jobSize:[0].
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.s.c.a.threadpool.ThreadPoolAutoConfiguration - [Fulfillmen Starter] - TaskScheduler extension configuration applied: poolSize=12, threadNamePrefix=scheduling-, rejectedPolicy=CALLER_RUNS
2025-08-12 14:58:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.starter.log.interceptor.handler.LogFilter - Filter 'logFilter' configured for use
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] org.xnio - XNIO version 3.8.16.Final
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] org.jboss.threads - JBoss Threads version 3.5.0.Final
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8080 (http) with context path '/'
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 62 ms
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 13 endpoints beneath base path '/actuator'
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.common.config.GlobalRateLimitWebConfig - 全局限流拦截器已注册: order=100, patterns=/**, rate=100/60s
2025-08-12 14:58:10 DEBUG [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.common.config.I18nConfig - Added LocaleChangeInterceptor to registry
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] io.undertow - starting server: Undertow - 2.3.18.Final
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port 8099 (http) with context path '/'
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Started BootstrapApplication in 8.896 seconds (process running for 9.579)
2025-08-12 14:58:10 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始初始化汇率缓存...
2025-08-12 14:58:10 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 开始定时刷新汇率缓存...
2025-08-12 14:58:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-08-12 14:58:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-12 14:58:10 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-12 14:58:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> USD
2025-08-12 14:58:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> USD = 0.13
2025-08-12 14:58:10 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-USD = 0.13 (原始: 0.13)
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> EUR
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> EUR = 0.11
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-EUR = 0.11 (原始: 0.11)
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> JPY
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> JPY = 20.54
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-JPY = 20.54 (原始: 20.54)
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> KRW
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> KRW = 192.88
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-KRW = 192.88 (原始: 192.88)
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-12 14:58:11 DEBUG [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - 从缓存获取汇率数据: CNY -> INR
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.s.m.s.j.s.impl.CurrencyExchangeServiceImpl - CNY基础汇率截断处理: CNY -> INR = 12.22
2025-08-12 14:58:11 DEBUG [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 获取CNY基础汇率成功(截断处理): CNY-INR = 12.22 (原始: 12.22)
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 汇率缓存初始化完成，缓存条目数: 5
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - Fulfillmen Shop service started successfully.
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API地址：http://127.0.0.1:8080
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - API文档：http://127.0.0.1:8080/doc.html
2025-08-12 14:58:12 INFO  [main] [tid::uId::ip::os::browser:] com.fulfillmen.shop.BootstrapApplication - ----------------------------------------------
2025-08-12 14:58:12 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.domain.util.CurrencyConversionUtils - 批量更新汇率缓存，共 5 条记录
2025-08-12 14:58:12 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - CNY基础汇率缓存更新完成: 成功 5/5 条记录
2025-08-12 14:58:12 INFO  [scheduling-1] [tid::uId::ip::os::browser:] c.f.shop.secheduler.CurrencyRateCacheScheduledTask - 定时刷新汇率缓存完成，缓存条目数: 5
2025-08-12 14:58:12 INFO  [RMI TCP Connection(7)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-12 14:58:12 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-12 14:58:12 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-12 14:58:12 INFO  [RMI TCP Connection(5)-127.0.0.1] [tid::uId::ip::os::browser:] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-12 14:58:13 INFO  [RMI TCP Connection(7)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.p6spy.engine.wrapper.ConnectionWrapper@460447c5
2025-08-12 14:58:13 INFO  [RMI TCP Connection(7)-127.0.0.1] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107321056860872704]:[0] 从缓存中获取租户ID: 10000
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107321056860872704]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107321056860872704]:[0] 租户缓存命中: 10000
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107321056860872704]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107321056860872704]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107321056860872704]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107321056860872704]:[0] [POST] /alibaba/callback
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107321056860872704]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107321056860872704]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"2873749476412540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":2873749476412540788,"sellerMemberId":"b2b-**********2e7a2","msgSendTime":"2025-08-12 11:05:55"},"gmtBorn":1754967955151,"msgId":144548371008,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: E2E11A0298D66A0E97E8ED75DAA8D40AED28F9B6
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107321056860872704]:[0] 收到阿里巴巴webhook回调，消息长度: 337
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107321056860872704]:[0] 开始处理webhook消息, 消息长度: 337
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107321056860872704]:[0] 解析到 1 个消息事件
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107321056860872704]:[0] 消息已处理过，跳过: msgId=144548371008
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107321056860872704]:[0] Webhook消息处理完成，处理时间: 55ms，处理结果数量: 1
2025-08-12 14:58:33 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107321056860872704]:[0] [POST] /alibaba/callback 200 77ms
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107321056860872704]:[0] 清理增强租户上下文
2025-08-12 14:58:33 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107321056860872704]:[0] 过滤器清理租户上下文完成
2025-08-12 15:00:00 INFO  [JetCacheDefaultExecutor] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.StatInfoLogger - jetcache stat from 2025-08-12 14:58:08,482 to 2025-08-12 15:00:00,006
cache                      |       qps|   rate|           get|           hit|          fail|        expire|avgLoadTime|maxLoadTime
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------
categories.                |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._local          |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
categories._remote         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
currency.rate.             |      0.09|100.00%|            10|            10|             0|             0|        0.0|          0
currency.rate._local       |      0.09|  0.00%|            10|             0|             0|             0|        0.0|          0
currency.rate._remote      |      0.09|100.00%|            10|            10|             0|             0|        0.0|          0
frontend:product:vo:       |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_local |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
frontend:product:vo:_remote|      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:               |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_local         |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:_remote        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:        |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_local  |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
pdc:product:search:_remote |      0.00|  0.00%|             0|             0|             0|             0|        0.0|          0
---------------------------+----------+-------+--------------+--------------+--------------+--------------+-----------+-----------

2025-08-12 15:00:00 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.s.SysAlibabaCallbackRetryScheduledTask - 开始执行阿里巴巴回调重试任务，配置：maxRetryCount=3, batchSize=20, timeWindowHours=24
2025-08-12 15:00:00 INFO  [scheduling-2] [tid::uId::ip::os::browser:] c.f.s.s.SysAlibabaCallbackRetryScheduledTask - 没有需要重试的失败记录
2025-08-12 15:00:03 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107321438160855040]:[0] 从缓存中获取租户ID: 10000
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107321438160855040]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107321438160855040]:[0] 租户缓存命中: 10000
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107321438160855040]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107321438160855040]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107321438160855040]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107321438160855040]:[0] [POST] /alibaba/callback
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107321438160855040]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107321438160855040]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"****************788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":****************788,"sellerMemberId":"b2b-*************e0d9c","msgSendTime":"2025-08-12 11:05:54"},"gmtBorn":1754967954306,"msgId":144548357600,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: 99333BC02E593D65A6DBB92325FF94EF5218EBD3
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107321438160855040]:[0] 收到阿里巴巴webhook回调，消息长度: 340
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107321438160855040]:[0] 开始处理webhook消息, 消息长度: 340
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107321438160855040]:[0] 解析到 1 个消息事件
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107321438160855040]:[0] 接收到订单webhook消息: msgId=144548357600, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=****************788, status=waitsellersend
2025-08-12 15:00:04 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.i.SysAlibabaCallbackLogsRepositoryImpl - [1107321438160855040]:[0] 创建Webhook处理日志异常(含事件与订单)
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
### The error may exist in com/fulfillmen/shop/dao/mapper/SysAlibabaCallbackLogsMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_alibaba_callback_logs (id, metadata, order_id, event_type, received_timestamp, process_status, gmt_created, gmt_modified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
; Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy150.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl.createProcessingLog(SysAlibabaCallbackLogsRepositoryImpl.java:87)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl$$SpringCGLIB$$0.createProcessingLog(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:126)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy232.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy231.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 130 common frames omitted
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107321438160855040]:[0] 开始处理订单webhook消息: orderId=****************788, msgId=144548357600, messageType=ORDER_BUYER_VIEW_ORDER_PAY, currentStatus=waitsellersend
2025-08-12 15:00:04 INFO  [naya-task-pool2] [tid::uId::ip::os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107321438160855040]:[0] 开始WMS订单详情查询，查询条件: {"orderId":"****************788"}
2025-08-12 15:00:04 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1107321438160855040]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=****************788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-08-12 15:00:04 INFO  [naya-task-pool1] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107321438160855040]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1754982004333access_token5fd99355-518b-47a6-b83e-0503223e0665orderId****************788webSite1688 签名: 8FBD91082FE89B32978BC46B154B01747C339DBF
2025-08-12 15:00:04 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107321438160855040]:[0] 订单数据获取完成: orderId=****************788, alibabaOrderDetail=true, wmsOrderDetails=1
2025-08-12 15:00:04 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 开始检查订单数据完整性: orderDetail={"baseInfo":{"id":****************788,"businessType":"cn","refund":0,"shippingFee":7,"status":"waitsellersend","totalAmount":30.52,"discount":0,"buyerContact":{"phone":"86-752-2313067","name":"汤维政","imInPlatform":"惠州中田贸易","companyName":"惠州市中田贸易有限公司","mobile":"***********"},"sellerContact":{"phone":"86-","name":"刘焕敏","imInPlatform":"郸城焕敏贸易商行","companyName":"河南郸城县全荣贸易商行（个体工商户）","shopName":"河南郸城县全荣贸易商行"},"tradeType":"50060","refundPayment":0,"idOfStr":"****************788","buyerID":"b2b-2207416548807a4d12","createTime":"2025-08-11 18:01:13","modifyTime":"2025-08-12 11:05:54","sellerID":"b2b-*************e0d9c","payTime":"2025-08-12 11:05:54","alipayTradeId":"2025081222001846301446688289","sumProductPayment":23.52,"flowTemplateCode":"assureTrade","sellerOrder":false,"buyerLoginId":"惠州中田贸易","sellerLoginId":"郸城焕敏贸易商行","closeOperateType":"","couponFee":0,"receiverInfo":{"toFullName":"中田 12092","toDivisionCode":"441302","toPost":"516000","toArea":"广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092","toMobile":"***********"},"tradeTypeDesc":"担保交易","payChannelList":["跨境宝2.0"],"tradeTypeCode":"assureTrade","payTimeout":432000,"payTimeoutType":0,"payChannelCodeList":["kjpayV2"],"outOrderId":"12092","stepPayAll":false,"newStepOrderList":[{"gmtStart":"2025-08-11 18:01:13","gmtPay":"2025-08-12 11:05:54","stepNo":1,"lastStep":true,"stepName":"全款交易","activeStatus":1,"payStatus":2,"logisticsStatus":1,"payFee":30.52,"paidFee":30.52,"adjustFee":0,"discountFee":0,"postFee":7,"paidPostFee":7}],"overSeaOrder":false},"orderBizInfo":{"odsCyd":false,"creditOrder":false,"dropshipping":false,"shippingInsurance":"givenByAnXinGou"},"tradeTerms":[{"payStatus":"2","payTime":"2025-08-12 11:05:54","payWay":"13","phasAmount":30.52,"phase":*****************,"cardPay":false,"expressPay":false,"payWayDesc":"支付平台"}],"productItems":[{"itemAmount":23.52,"name":"✅高纯度无铅焊锡丝SN993松香芯维修焊接锡线ROH","price":23.52,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01cRLv7M1hZKbIAYtWI_!!*************-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01cRLv7M1hZKbIAYtWI_!!*************-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=****************788","quantity":1,"refund":0,"skuID":*************,"status":"waitsellersend","subItemID":****************788,"type":"common","unit":"件","productCargoNumber":"Fh(x}&aq^lb*b*$%p@vh(&aq@vh($p","skuInfos":[{"name":"规格型号","value":"☼☍☍☼无铅 0.6mm【50克】☼☍☍☼"}],"entryDiscount":0,"specId":"ce7e67541a8592102c6dbe5adc376b1e","quantityFactor":1,"statusStr":"等待卖家发货","subItemIDString":"****************788","logisticsStatus":1,"gmtCreate":"2025-08-11 18:01:13","gmtModified":"2025-08-12 11:05:54","sharePostage":7,"guaranteesTerms":[{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]}],"nativeLogistics":{"address":"江北 金泽物流园二期一号楼四楼-12092","area":"惠城区","areaCode":"441302","city":"惠州市","contactPerson":"中田 12092","mobile":"***********","province":"广东省","zip":"516000"},"orderRateInfo":{"buyerRateStatus":5,"sellerRateStatus":5},"extAttributes":[],"fromEncryptOrder":false}
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 未找到供应商订单: orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=****************788, idOfStr=****************788, businessType=cn, buyerId=b2b-2207416548807a4d12, completeTime=null, createTime=2025-08-11T18:01:13, modifyTime=2025-08-12T11:05:54, refund=0, sellerId=b2b-*************e0d9c, shippingFee=7, status=waitsellersend, totalAmount=30.52, discount=0, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-, fax=null, email=null, name=刘焕敏, imInPlatform=郸城焕敏贸易商行, companyName=河南郸城县全荣贸易商行（个体工商户）, mobile=null, address=null), shopName=河南郸城县全荣贸易商行, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=null, payTime=2025-08-12T11:05:54, receivingTime=null, alipayTradeId=2025081222001846301446688289, sumProductPayment=23.52, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=郸城焕敏贸易商行, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=中田 12092, toDivisionCode=441302, toPost=516000, toArea=广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=***********, toPhone=null, toEmail=null, toTownCode=null), tradeTypeDesc=担保交易, payChannelList=[跨境宝2.0], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[kjpayV2], outOrderId=12092, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-08-11T18:01:13, gmtPay=2025-08-12T11:05:54, gmtEnd=null, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=2, logisticsStatus=1, payFee=30.52, paidFee=30.52, goodsFee=null, adjustFee=0, discountFee=0, postFee=7, paidPostFee=7)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=2, payTime=2025-08-12T11:05:54, payWay=13, phasAmount=30.52, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=null, description=null, itemAmount=23.52, name=✅高纯度无铅焊锡丝SN993松香芯维修焊接锡线ROH, price=23.52, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01cRLv7M1hZKbIAYtWI_!!*************-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01cRLv7M1hZKbIAYtWI_!!*************-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=****************788, quantity=1, refund=0, skuId=*************, sort=null, status=waitsellersend, subItemId=****************788, type=common, unit=件, weight=null, weightUnit=null, productCargoNumber=Fh(x}&aq^lb*b*$%p@vh(&aq@vh($p, skuInfos=[TradeProductItem.TradeSkuInfo(name=规格型号, value=☼☍☍☼无铅 0.6mm【50克】☼☍☍☼)], entryDiscount=0, specId=ce7e67541a8592102c6dbe5adc376b1e, quantityFactor=1, statusStr=等待卖家发货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=****************788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=1, gmtCreate=2025-08-11T18:01:13, gmtModified=2025-08-12T11:05:54, gmtCompleted=null, gmtPayExpireTime=null, sharePostage=7, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)])], nativeLogistics=TradeNativeLogisticsInfo(address=江北 金泽物流园二期一号楼四楼-12092, area=惠城区, areaCode=441302, city=惠州市, contactPerson=中田 12092, fax=null, mobile=***********, province=广东省, telephone=null, zip=516000, logisticsItems=null, townCode=null, town=null), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null)
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107321438160855040]:[0] 订单数据完整性检查结果: orderId=****************788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 开始同步和补齐订单数据: orderId=****************788, needsSync=true
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 开始补齐缺失的订单数据: orderId=****************788, missing=采购订单, 供应商订单, 订单项
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 创建采购订单成功: orderId=****************788, purchaseOrderId=728181985726594
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 创建供应商订单成功: orderId=****************788, supplierOrderCount=1
2025-08-12 15:00:05 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107321438160855040]:[0] 数据库不存在，从API获取商品详情, id: ************
2025-08-12 15:00:05 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107321438160855040]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1754982005474access_token5fd99355-518b-47a6-b83e-0503223e0665offerDetailParam{"offerId":************,"country":"en"} 签名: CF7671280CF12771EBAE6FDA913C3B9148B70083
2025-08-12 15:00:05 WARN  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] io.micrometer.core.instrument.MeterRegistry - [1107321438160855040]:[0] This Gauge has been already registered (MeterId{name='alibaba.api.concurrent.requests', tags=[]}), the registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-08-12 15:00:05 DEBUG [reactor-http-nio-3] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - [1107321438160855040]:[0] result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=*********, categoryName=null, subject=◇高纯度无铅焊锡丝SN993松香芯维修焊接锡线ROH, subjectTrans=◇高纯度无铅焊锡丝SN993松香芯维修焊接锡线ROH, description=<div id="offer-template-0"></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01CkWiJq20Dmd6hy9Ao_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN019JIQOu20Dmd7RFg65_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01cMBP8g20Dmd6ER0Bl_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01K2rMPO20Dmd7Ojr8d_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01OXdodf20Dmd6bfunS_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01APDIEA20Dmd7OkONJ_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN012MkBwe20Dmd4ucW2D_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01zVj6oL20Dmd6hx8pv_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01JZjrUn20Dmd5oAWO3_!!**********.jpg"/><p><img src="https://cbu01.alicdn.com/img/ibank/O1CN01wqle1I1QQJ770Iw6z_!!*************-0-cib.jpg"/></p></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01zWVfRs20Dmd6hykc5_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01S935cm20Dmd5oCOpB_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01BKtVIJ20Dmd715pls_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01QwwYyT20Dmd5o8yjD_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01ea8n8H20Dmd8JigGQ_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN018m3AOU20Dmd6hvvy4_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Shi8Mz20Dmd5oBrYO_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN01DdCpTY20Dmd7vy9U3_!!**********.jpg"/></div><div style="text-align: center;"><img style="display: block;width: 100.0%;" src="https://cbu01.alicdn.com/img/ibank/O1CN019Dhaqq20Dmd6hy0sT_!!**********.jpg"/></div><p><img src="https://cbu01.alicdn.com/img/ibank/O1CN01jsOxOl1QQJ7B1fbjb_!!*************-0-cib.jpg"/></p>, mainVideo=https://cloud.video.taobao.com/play/u/*************/p/1/e/6/t/1/************.mp4, detailVideo=https://cloud.video.taobao.com/play/u/*************/p/1/e/6/t/1/************.mp4, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN01JKU8WN1L7vLW1cW2v_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01UBn7ET1L7vLWkSSVP_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN018WlJ3F1L7vLVb4PKZ_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01o5qy341L7vLW1bRU1_!!*************-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01HBiVq81L7vLVIHLNt_!!*************-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.6mm【50克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.6mm【50克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.8mm【50克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.8mm【50克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.6mm【100克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.6mm【100克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.8mm【100克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.8mm【100克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.0mm【100克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.0mm【100克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.2mm【100克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.2mm【100克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.8mm【250克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.8mm【250克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.5mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.5mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.6mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.6mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.8mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.8mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.0mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.0mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.2mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.2mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.5mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.5mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 2.0mm【500克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 2.0mm【500克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.5mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.5mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.6mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.6mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.7mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.7mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 0.8mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 0.8mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.0mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.0mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.2mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.2mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 1.5mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 1.5mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼无铅 2.0mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼无铅 2.0mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼高标准 0.6mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼高标准 0.6mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼高标准 0.8mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼高标准 0.8mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼高标准 1.0mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼高标准 1.0mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1236, attributeName=规格型号, value=☼☍☍☼高标准 1.2mm【1000克】☼☍☍☼, attributeNameTrans=规格型号, valueTrans=☼☍☍☼高标准 1.2mm【1000克】☼☍☍☼), GoodsDetailResponse.ProductAttribute(attributeId=1298, attributeName=焊接电流, value=其他, attributeNameTrans=焊接电流, valueTrans=其他), GoodsDetailResponse.ProductAttribute(attributeId=1134, attributeName=功率, value=其他, attributeNameTrans=功率, valueTrans=其他), GoodsDetailResponse.ProductAttribute(attributeId=2563, attributeName=适用范围, value=其他, attributeNameTrans=适用范围, valueTrans=其他), GoodsDetailResponse.ProductAttribute(attributeId=1836, attributeName=类型, value=其他, attributeNameTrans=类型, valueTrans=其他), GoodsDetailResponse.ProductAttribute(attributeId=100000729, attributeName=加工定制, value=其他, attributeNameTrans=加工定制, valueTrans=其他), GoodsDetailResponse.ProductAttribute(attributeId=1398, attributeName=货号, value=Fh(x}&aq^lb*b*$%p@vh(&aq@vh($p, attributeNameTrans=货号, valueTrans=Fh(x}&aq^lb*b*$%p@vh(&aq@vh($p), GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=其他, attributeNameTrans=品牌, valueTrans=其他)], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=200, price=23.52, jxhyPrice=null, skuId=*************, specId=ce7e67541a8592102c6dbe5adc376b1e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.6mm【50克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.6mm【50克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=23.52, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=23.52)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=17.76, jxhyPrice=null, skuId=6068743807947, specId=90f4ddf7c5c7852879cce4f0094e54a3, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.8mm【50克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.8mm【50克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=17.76, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=17.76)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=38.92, jxhyPrice=null, skuId=6068743807948, specId=8e49c9e620450ad48bf19c659f392a25, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.6mm【100克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.6mm【100克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=38.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=38.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=36.12, jxhyPrice=null, skuId=6068743807949, specId=a607067aad478f79c7e53f6d12818d32, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.8mm【100克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.8mm【100克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=36.12, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=36.12)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=36.12, jxhyPrice=null, skuId=6068743807950, specId=7b2a0da64e82a35268a5b1a68100fcc5, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.0mm【100克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.0mm【100克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=36.12, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=36.12)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=36.12, jxhyPrice=null, skuId=6068743807951, specId=0ee5a7e242616d8c1c2d4b4b28ce63e2, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.2mm【100克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.2mm【100克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=36.12, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=36.12)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=92.12, jxhyPrice=null, skuId=6068743807952, specId=a21975a44631467e019c935ef9e11001, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.8mm【250克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.8mm【250克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=92.12, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=92.12)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=180.32, jxhyPrice=null, skuId=6068743807953, specId=788d0fe159f3f671f606de6a7a068224, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.5mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.5mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=180.32, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=180.32)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=167.72, jxhyPrice=null, skuId=6068743807954, specId=ad0158836ab2baded3a61ed680d59335, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.6mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.6mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=167.72, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=167.72)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=164.92, jxhyPrice=null, skuId=6068743807955, specId=f89ed0805f5926d53cc7e1cec8ed3c3d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.8mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.8mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=164.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=164.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=164.92, jxhyPrice=null, skuId=6068743807956, specId=d56fa66190059e23e226a4bc1277cc6e, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.0mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.0mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=164.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=164.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=164.92, jxhyPrice=null, skuId=6068743807957, specId=2ae97d315a0204ec89b0142d77af0928, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.2mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.2mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=164.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=164.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=164.92, jxhyPrice=null, skuId=6068743807958, specId=fb3a8afcfd41ff72a41af3a0143b5bc5, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.5mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.5mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=164.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=164.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=164.92, jxhyPrice=null, skuId=6068743807959, specId=ef88309df2355bd07cb406b2f68a0409, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 2.0mm【500克】☼☍☍☼, valueTrans=☼☍☍☼无铅 2.0mm【500克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=164.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=164.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=341.32, jxhyPrice=null, skuId=6068743807960, specId=95807c310bf9fd0f249041dea02c9a5b, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.5mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.5mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=341.32, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=341.32)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=335.72, jxhyPrice=null, skuId=6068743807961, specId=360f5df72673767192c05469726055fd, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.6mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.6mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=335.72, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=335.72)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=332.92, jxhyPrice=null, skuId=6068743807962, specId=38edd5f751e4f17b5c5f3f4c392398fa, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.7mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.7mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=332.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=332.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=332.92, jxhyPrice=null, skuId=6068743807963, specId=51ca8c87143e532880d409d159b92e23, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 0.8mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 0.8mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=332.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=332.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=332.92, jxhyPrice=null, skuId=6068743807964, specId=2f0b9a8f7e8911b36f12805462c060fa, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.0mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.0mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=332.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=332.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=332.92, jxhyPrice=null, skuId=6068743807965, specId=064e7c469eb6d35c4656847e011c8a4d, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.2mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.2mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=332.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=332.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=332.92, jxhyPrice=null, skuId=6068743807966, specId=21415d97cca29318902d26b4602843a7, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 1.5mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 1.5mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=332.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=332.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=332.92, jxhyPrice=null, skuId=6068743807967, specId=bb6ec7d43b54389db7112923ce549262, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼无铅 2.0mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼无铅 2.0mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=332.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=332.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=342.72, jxhyPrice=null, skuId=6068743807968, specId=5742127dd9459424bb29decf1db32322, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼高标准 0.6mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼高标准 0.6mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=342.72, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=342.72)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=339.92, jxhyPrice=null, skuId=6068743807969, specId=023a05e2251d44ce6c029a0f189c000f, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼高标准 0.8mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼高标准 0.8mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=339.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=339.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=339.92, jxhyPrice=null, skuId=6068743807970, specId=1899025bba94153a1f2c9401f06b19a7, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼高标准 1.0mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼高标准 1.0mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=339.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=339.92)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=201, price=339.92, jxhyPrice=null, skuId=6068743807971, specId=881228d8687b5f2aa0de9ded1b5fa665, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=1236, attributeName=规格型号, attributeNameTrans=规格型号, value=☼☍☍☼高标准 1.2mm【1000克】☼☍☍☼, valueTrans=☼☍☍☼高标准 1.2mm【1000克】☼☍☍☼, skuImageUrl=null)], cargoNumber=, promotionPrice=null, consignPrice=339.92, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=339.92))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=5225, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=1, price=17.76, promotionPrice=null)], quoteType=1, unitInfo=GoodsDetailResponse.UnitInfo(unit=件, transUnit=件), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=false, startQuantity=1, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=河南省南阳市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=shipIn48Hours, skuShippingInfoList=null, skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=6068743807949, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807948, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807967, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807966, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807947, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807969, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807968, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807963, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807962, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807965, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807964, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807961, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807960, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807959, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807956, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807955, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807958, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807957, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807952, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807951, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807954, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807953, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807970, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807950, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=6068743807971, width=0.0, length=0.0, height=0.0, weight=1.0, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=false, sellerOpenId=BBB8bw5c9lH30JBQuJ3undjdQ, minOrderQuantity=1, batchNumber=null, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=false), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=false), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=false), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false), GoodsDetailResponse.TagInfoList(key=new30, value=true)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=null, productCargoNumber=Fh(x}&aq^lb*b*$%p@vh(&aq@vh($p, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=1, compositeServiceScore=3.5, logisticsExperienceScore=3.0, disputeComplaintScore=4.0, offerExperienceScore=5.0, consultingExperienceScore=2.0, repeatPurchasePercent=0.405879979182005, afterSalesExperienceScore=3.0, collect30DayWithin48HPercent=null, qualityRefundWithin30Day=0.0), soldOut=0, channelPrice=null, promotionModel=null, tradeScore=0.0, topCategoryId=59, secondCategoryId=201924501, thirdCategoryId=*********, sellingPoint=null, offerIdentities=[tp_member], createDate=2025-07-20 18:12:09, isSelect=false, certificateList=[], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-08-12 15:00:05 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.d.c.product.PdcProductDetailConvertMapping - [1107321438160855040]:[0] 生成新ID: *************** 对应平台商品ID: ************
2025-08-12 15:00:05 INFO  [naya-task-pool3] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - [1107321438160855040]:[0] 开始解密旺旺昵称, openUid: BBB8bw5c9lH30JBQuJ3undjdQ
2025-08-12 15:00:05 INFO  [naya-task-pool3] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107321438160855040]:[0] 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1754982005947access_token5fd99355-518b-47a6-b83e-0503223e0665openUidBBB8bw5c9lH30JBQuJ3undjdQ 签名: 3C9DA1C2A748A2748C74FBF46799037BD499E392
2025-08-12 15:00:05 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] com.fulfillmen.shop.common.util.MetaInfoHashUtils - [1107321438160855040]:[0] 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=5996a7a6ddb22e554f045377ab7e41b4, metaInfo长度=21891
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107321438160855040]:[0] 商品详情同步到数据库完成, id: ************
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107321438160855040]:[0] 商品详情更新到缓存完成, id: ************
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107321438160855040]:[0] 自动同步功能已关闭，跳过事件发布
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107321438160855040]:[0] 获取或同步产品数据: platformProductId=************
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107321438160855040]:[0] 从缓存获取商品详情, id: ************
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107321438160855040]:[0] 从PdcProductMapping同步产品数据: platformProductId=************
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107321438160855040]:[0] 开始同步产品数据，platformProductId: ************
2025-08-12 15:00:06 DEBUG [naya-task-pool4] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107321438160855040]:[0] 从缓存获取商品详情, id: ************
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107321438160855040]:[0] SPU创建成功，spuId: 728181990719621
2025-08-12 15:00:06 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107321438160855040]:[0] 多规格SKU创建成功，数量: 26
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 创建订单项成功: orderId=****************788, orderItemCount=1
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107321438160855040]:[0] 订单数据补齐完成: orderId=****************788, purchaseOrderId=728181985726594, supplierOrderCount=1, orderItemCount=1
2025-08-12 15:00:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.a.threadpool.AsyncAutoConfiguration - [1107321438160855040]:[0] [Fulfillmen Starter] - Auto Configuration 'AsyncConfigurer' completed initialization.
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107321438160855040]:[0] 路由到具体的业务处理逻辑: orderId=****************788, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 15:00:34 INFO  [naya-task-pool7] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107321438160855040]:[0] 处理数据同步完成事件: orderId=****************788, messageType=ORDER_BUYER_VIEW_ORDER_PAY, eventTime=2025-08-12T15:00:34.077432
2025-08-12 15:00:34 INFO  [naya-task-pool7] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107321438160855040]:[0] 数据同步成功: orderId=****************788, purchaseOrderNo=N2508121500051111911, isDataSync=true, isCompatibility=false
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107321438160855040]:[0] 开始处理订单支付事件: orderId=****************788
2025-08-12 15:00:34 DEBUG [naya-task-pool7] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107321438160855040]:[0] 更新同步指标: orderId=****************788
2025-08-12 15:00:34 DEBUG [naya-task-pool7] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107321438160855040]:[0] 清理临时数据: orderId=****************788
2025-08-12 15:00:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107321438160855040]:[0] 更新本地订单状态为已支付: orderId=****************788
2025-08-12 15:00:34 DEBUG [naya-task-pool7] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107321438160855040]:[0] 数据同步完成事件处理完成: orderId=****************788
2025-08-12 15:00:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107321438160855040]:[0] 通知WMS订单已支付: orderId=****************788
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.webhook.handler.OrderContextRecord - [1107321438160855040]:[0] 重新 wms 同步商品信息: WmsPurchaseOrderDetailsItemRes(orderId=****************788, skuId=*************, enName=✅ High purity lead-free solder wire SN993 rosin core repair soldering tin wire ROH, cnName=✅高纯度无铅焊锡丝SN993松香芯维修焊接锡线ROH, weight=0.000, quantity=1, variantId=ce7e67541a8592102c6dbe5adc376b1e, productId=************, unitPrice=23.520, originUnitPrice=23.520, finalUnitPrice=23.520, subTotal=23.520, originSubTotalAmount=23.520, finalSubTotalAmount=30.520, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01JKU8WN1L7vLW1cW2v_!!*************-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=规格型号:☼☍☍☼无铅 0.6mm【50克】☼☍☍☼;, skuAttribEn=Specification and model:☼☍☍☼ Lead free 0.6mm [50g] ☼☍☍☍☼;)
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107321438160855040]:[0] 开始更新 WMS 采购订单请求，采购单号: [{"purchaseNo":"C120922025081118011200003","orderId":****************788,"alibabaFinalAmount":30.52,"alibabaTotalAmount":30.52,"plusDiscount":0.000,"couponDiscount":0,"status":2,"paymentTime":"2025-08-12 11:05:54","orderDetails":[{"quantity":1,"variantId":"ce7e67541a8592102c6dbe5adc376b1e","productId":"************","finalUnitPrice":23.52,"finalSubTotalAmount":23.52,"sku":"*************"}]}]
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107321438160855040]:[0] 订单支付事件处理完成: orderId=****************788
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107321438160855040]:[0] 订单消息处理完成: orderId=****************788, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107321438160855040]:[0] 订单webhook消息处理完成: orderId=****************788, msgId=144548357600, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107321438160855040]:[0] 订单webhook消息处理完成: msgId=144548357600, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=****************788
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107321438160855040]:[0] Webhook消息处理完成，处理时间: 30503ms，处理结果数量: 1
2025-08-12 15:00:34 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107321438160855040]:[0] [POST] /alibaba/callback 200 30503ms
2025-08-12 15:00:34 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107321438160855040]:[0] 清理增强租户上下文
2025-08-12 15:00:34 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107321438160855040]:[0] 过滤器清理租户上下文完成
2025-08-12 15:03:42 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107322356382081024]:[0] 从缓存中获取租户ID: 10000
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107322356382081024]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107322356382081024]:[0] 租户缓存命中: 10000
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107322356382081024]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107322356382081024]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107322356382081024]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107322356382081024]:[0] [POST] /alibaba/callback
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107322356382081024]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107322356382081024]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"****************788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":****************788,"sellerMemberId":"b2b-*************e0d9c","msgSendTime":"2025-08-12 11:05:54"},"gmtBorn":1754967954306,"msgId":144548357600,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: 99333BC02E593D65A6DBB92325FF94EF5218EBD3
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107322356382081024]:[0] 收到阿里巴巴webhook回调，消息长度: 340
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107322356382081024]:[0] 开始处理webhook消息, 消息长度: 340
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107322356382081024]:[0] 解析到 1 个消息事件
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107322356382081024]:[0] 消息已处理过，跳过: msgId=144548357600
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107322356382081024]:[0] Webhook消息处理完成，处理时间: 63ms，处理结果数量: 1
2025-08-12 15:03:43 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107322356382081024]:[0] [POST] /alibaba/callback 200 64ms
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107322356382081024]:[0] 清理增强租户上下文
2025-08-12 15:03:43 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107322356382081024]:[0] 过滤器清理租户上下文完成
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] c.f.shop.manager.service.TenantResolverService - [1107322801049608192]:[0] 从缓存中获取租户ID: 10000
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107322801049608192]:[0] 租户缓存刷新成功: 10000, 延长时间: 7200秒
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.TenantCacheServiceImpl - [1107322801049608192]:[0] 租户缓存命中: 10000
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107322801049608192]:[0] 缓存命中: 租户ID=10000, 访问次数=3
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107322801049608192]:[0] 设置增强租户上下文: tenantId=10000, tenantName=Fulfillmen
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107322801049608192]:[0] 过滤器设置当前请求的租户ID: 10000 (URI: /alibaba/callback)
2025-08-12 15:05:28 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107322801049608192]:[0] [POST] /alibaba/callback
2025-08-12 15:05:28 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.c.interceptor.GlobalRateLimitInterceptor - [1107322801049608192]:[0] 方法 WebhookApi.callBack 有 @RateLimitIgnore 注解，忽略限流
2025-08-12 15:05:29 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107322801049608192]:[0] 收到阿里巴巴webhook回调，消息内容: {"bizKey":"2873679168847540788","data":{"buyerMemberId":"b2b-2207416548807a4d12","currentStatus":"waitsellersend","orderId":2873679168847540788,"sellerMemberId":"b2b-**********42742","msgSendTime":"2025-08-12 11:05:54"},"gmtBorn":1754967954966,"msgId":144548350945,"type":"ORDER_BUYER_VIEW_ORDER_PAY","userInfo":"b2b-2207416548807a4d12"}，签名: 68D3E6BA379246FE31E71478AA63FFAD4E2FBD4F
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107322801049608192]:[0] 收到阿里巴巴webhook回调，消息长度: 337
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107322801049608192]:[0] 开始处理webhook消息, 消息长度: 337
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.support.alibaba.webhook.MessageDispatcher - [1107322801049608192]:[0] 解析到 1 个消息事件
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107322801049608192]:[0] 接收到订单webhook消息: msgId=144548350945, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873679168847540788, status=waitsellersend
2025-08-12 15:05:29 ERROR [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.i.SysAlibabaCallbackLogsRepositoryImpl - [1107322801049608192]:[0] 创建Webhook处理日志异常(含事件与订单)
org.springframework.dao.DataIntegrityViolationException: 
### Error updating database.  Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
### The error may exist in com/fulfillmen/shop/dao/mapper/SysAlibabaCallbackLogsMapper.java (best guess)
### The error may involve com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_alibaba_callback_logs (id, metadata, order_id, event_type, received_timestamp, process_status, gmt_created, gmt_modified) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
### Cause: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
; Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:118)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:93)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:347)
	at jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)
	at org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:224)
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156)
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93)
	at jdk.proxy2/jdk.proxy2.$Proxy150.insert(Unknown Source)
	at com.baomidou.mybatisplus.extension.repository.IRepository.save(IRepository.java:37)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl.createProcessingLog(SysAlibabaCallbackLogsRepositoryImpl.java:87)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:769)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:721)
	at com.fulfillmen.shop.manager.core.repository.impl.SysAlibabaCallbackLogsRepositoryImpl$$SpringCGLIB$$0.createProcessingLog(<generated>)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:126)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.handler.OrderHandler.doHandle(OrderHandler.java:44)
	at com.fulfillmen.support.alibaba.webhook.AbstractTypedMessageHandler.handle(AbstractTypedMessageHandler.java:52)
	at com.fulfillmen.support.alibaba.webhook.MessageRouter.route(MessageRouter.java:59)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.processEvent(MessageDispatcher.java:108)
	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:197)
	at java.base/java.util.AbstractList$RandomAccessSpliterator.forEachRemaining(AbstractList.java:722)
	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:575)
	at java.base/java.util.stream.AbstractPipeline.evaluateToArrayNode(AbstractPipeline.java:260)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:616)
	at java.base/java.util.stream.ReferencePipeline.toArray(ReferencePipeline.java:622)
	at java.base/java.util.stream.ReferencePipeline.toList(ReferencePipeline.java:627)
	at com.fulfillmen.support.alibaba.webhook.MessageDispatcher.dispatch(MessageDispatcher.java:60)
	at com.fulfillmen.shop.manager.support.alibaba.webhook.WebhookApi.callBack(WebhookApi.java:50)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.xiaoymin.knife4j.extend.filter.basic.JakartaServletSecurityBasicAuthFilter.doFilter(JakartaServletSecurityBasicAuthFilter.java:55)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.servlet.resource.ResourceUrlEncodingFilter.doFilter(ResourceUrlEncodingFilter.java:66)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.log.interceptor.handler.LogFilter.doFilterInternal(LogFilter.java:70)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaFirewallCheckFilterForJakartaServlet.doFilter(SaFirewallCheckFilterForJakartaServlet.java:69)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenCorsFilterForJakartaServlet.doFilter(SaTokenCorsFilterForJakartaServlet.java:52)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at cn.dev33.satoken.filter.SaTokenContextFilterForJakartaServlet.doFilter(SaTokenContextFilterForJakartaServlet.java:40)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.GlobalMDCFilter.doFilter(GlobalMDCFilter.java:108)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.shop.config.filter.TenantFilter.doFilter(TenantFilter.java:94)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.fulfillmen.starter.web.autoconfigure.trace.TLogServletFilter.doFilter(TLogServletFilter.java:51)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:861)
	at org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)
	at org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.mysql.cj.jdbc.exceptions.MysqlDataTruncation: Data truncation: Invalid JSON text: "Invalid value." at position 0 in value for column 'sys_alibaba_callback_logs.metadata'.
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:104)
	at com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:912)
	at com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)
	at com.p6spy.engine.wrapper.PreparedStatementWrapper.execute(PreparedStatementWrapper.java:362)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)
	at jdk.proxy2/jdk.proxy2.$Proxy232.update(Unknown Source)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61)
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)
	at jdk.proxy2/jdk.proxy2.$Proxy231.update(Unknown Source)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:184)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:333)
	... 130 common frames omitted
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107322801049608192]:[0] 开始处理订单webhook消息: orderId=2873679168847540788, msgId=144548350945, messageType=ORDER_BUYER_VIEW_ORDER_PAY, currentStatus=waitsellersend
2025-08-12 15:05:29 INFO  [naya-task-pool10] [tid::uId::ip::os::browser:] c.f.shop.manager.support.alibaba.impl.OrderManager - [1107322801049608192]:[0] 获取订单详情请求: OrderDetailRequestRecord[webSite=1688, orderId=2873679168847540788, needBuyerAddressAndPhone=null, needMemoInfo=null, needInvoiceInfo=null]
2025-08-12 15:05:29 INFO  [naya-task-pool10] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107322801049608192]:[0] 签名因子: param2/1/com.alibaba.trade/alibaba.trade.get.buyerView/8390330_aop_timestamp1754982329194access_token5fd99355-518b-47a6-b83e-0503223e0665orderId2873679168847540788webSite1688 签名: 262A3CF9C722E28DE6FD5FBC8CC367B2264F3C01
2025-08-12 15:05:29 INFO  [naya-task-pool11] [tid::uId::ip::os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107322801049608192]:[0] 开始WMS订单详情查询，查询条件: {"orderId":"2873679168847540788"}
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107322801049608192]:[0] 订单数据获取完成: orderId=2873679168847540788, alibabaOrderDetail=true, wmsOrderDetails=1
2025-08-12 15:05:29 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 开始检查订单数据完整性: orderDetail={"baseInfo":{"id":2873679168847540788,"businessType":"cb","refund":0,"shippingFee":8,"status":"waitbuyerreceive","totalAmount":1548,"discount":-11000,"buyerContact":{"phone":"86-752-2313067","name":"汤维政","imInPlatform":"惠州中田贸易","companyName":"惠州市中田贸易有限公司","mobile":"***********"},"sellerContact":{"phone":"86-0755-***********","email":"<EMAIL>","name":"李斌云","imInPlatform":"宾莉科技","companyName":"深圳市宾莉科技有限公司","mobile":"***********","shopName":"深圳市宾莉科技有限公司"},"tradeType":"50060","refundPayment":0,"idOfStr":"2873679168847540788","buyerID":"b2b-2207416548807a4d12","createTime":"2025-08-11 17:19:31","modifyTime":"2025-08-12 11:34:11","sellerID":"b2b-**********42742","allDeliveredTime":"2025-08-12 11:34:11","payTime":"2025-08-12 11:05:55","alipayTradeId":"2025081222001846301446841751","sumProductPayment":1650,"flowTemplateCode":"assureTrade","sellerOrder":false,"buyerLoginId":"惠州中田贸易","sellerLoginId":"宾莉科技","closeOperateType":"","couponFee":0,"receiverInfo":{"toFullName":"中田 12092","toDivisionCode":"441302","toPost":"516000","toArea":"广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092","toMobile":"***********"},"tradeTypeDesc":"担保交易","payChannelList":["跨境宝2.0"],"tradeTypeCode":"assureTrade","payTimeout":432000,"payTimeoutType":0,"payChannelCodeList":["kjpayV2"],"outOrderId":"12092","stepPayAll":false,"newStepOrderList":[{"gmtStart":"2025-08-11 17:19:32","gmtPay":"2025-08-12 11:05:55","stepNo":1,"lastStep":true,"stepName":"全款交易","activeStatus":1,"payStatus":2,"logisticsStatus":2,"payFee":1548,"paidFee":1548,"adjustFee":-110,"discountFee":0,"postFee":8,"paidPostFee":8}],"overSeaOrder":false},"orderBizInfo":{"odsCyd":false,"creditOrder":false,"dropshipping":false,"shippingInsurance":"givenByAnXinGou"},"tradeTerms":[{"payStatus":"2","payTime":"2025-08-12 11:05:55","payWay":"13","phasAmount":1548,"phase":*****************,"cardPay":false,"expressPay":false,"payWayDesc":"支付平台"}],"productItems":[{"itemAmount":1540,"name":"ANBERNIC外款RG557 安卓14掌机大屏电容摇杆复古怀旧款老式串流连","price":1650,"productID":************,"productImgUrl":["http://cbu01.alicdn.com/img/ibank/O1CN01GRITEK1Bs33J0N6uW_!!0-0-cib.80x80.jpg","http://cbu01.alicdn.com/img/ibank/O1CN01GRITEK1Bs33J0N6uW_!!0-0-cib.jpg"],"productSnapshotUrl":"https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873679168847540788","quantity":1,"refund":0,"skuID":*************,"status":"waitbuyerreceive","subItemID":2873679168847540788,"type":"common","unit":"台","productCargoNumber":"RG557","skuInfos":[{"name":"型号","value":"RG557 紫透 英文"},{"name":"产品颜色","value":"标配12+256G 无游戏"}],"entryDiscount":-11000,"specId":"02dcb6fd6d982513dad3db03e91ea24f","quantityFactor":1,"statusStr":"等待买家收货","subItemIDString":"2873679168847540788","logisticsStatus":2,"gmtCreate":"2025-08-11 17:19:31","gmtModified":"2025-08-12 11:34:11","sharePostage":8,"guaranteesTerms":[{"assuranceInfo":"卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。","assuranceType":"qtwlybt","qualityAssuranceType":"7天无理由退货"},{"assuranceInfo":"“满足相应条件时，用户在退货寄出后，享受极速退款到账。","assuranceType":"lsjst_s","qualityAssuranceType":"极速退款"},{"assuranceInfo":"卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。","assuranceType":"ssbxsfh","qualityAssuranceType":"48小时发货"}]}],"nativeLogistics":{"address":"江北 金泽物流园二期一号楼四楼-12092","area":"惠城区","areaCode":"441302","city":"惠州市","contactPerson":"中田 12092","mobile":"***********","province":"广东省","zip":"516000","logisticsItems":[{"id":237188048240929,"deliveredTime":"2025-08-12 11:34:11","logisticsCode":"LP00754686241372","type":"0","status":"alreadysend","gmtModified":"2025-08-12 11:34:11","gmtCreate":"2025-08-12 11:34:11","fromProvince":"广东省","fromCity":"深圳市","fromArea":"龙岗区","fromAddress":"坪地街道 坪西社区高桥第一工业区12号3栋336","fromPhone":"86-0755-***********","fromMobile":"***********","fromPost":"518000","logisticsCompanyId":3,"logisticsCompanyNo":"ZTO","logisticsCompanyName":"中通快递(ZTO)","logisticsBillNo":"78930896233428","subItemIds":"2873679168847540788","toCity":"惠州市","toArea":"惠城区","toAddress":"江北 金泽物流园二期一号楼四楼-12092","toPost":"516000","noLogisticsName":"","noLogisticsTel":"","noLogisticsBillNo":"","noLogisticsCondition":"-1"}]},"orderRateInfo":{"buyerRateStatus":5,"sellerRateStatus":5},"extAttributes":[],"fromEncryptOrder":false}
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 未找到供应商订单: orderDetail=OrderDetailResponse.OrderDetail(baseInfo=TradeBaseInfo(id=2873679168847540788, idOfStr=2873679168847540788, businessType=cb, buyerId=b2b-2207416548807a4d12, completeTime=null, createTime=2025-08-11T17:19:31, modifyTime=2025-08-12T11:34:11, refund=0, sellerId=b2b-**********42742, shippingFee=8, status=waitbuyerreceive, totalAmount=1548, discount=-11000, buyerContact=Contact(phone=86-752-2313067, fax=null, email=null, name=汤维政, imInPlatform=惠州中田贸易, companyName=惠州市中田贸易有限公司, mobile=***********, address=null), sellerContact=TradeSellContact(super=Contact(phone=86-0755-***********, fax=null, email=<EMAIL>, name=李斌云, imInPlatform=宾莉科技, companyName=深圳市宾莉科技有限公司, mobile=***********, address=null), shopName=深圳市宾莉科技有限公司, wgSenderPhone=null, wgSenderName=null), tradeType=50060, refundPayment=0, allDeliveredTime=2025-08-12T11:34:11, payTime=2025-08-12T11:05:55, receivingTime=null, alipayTradeId=2025081222001846301446841751, sumProductPayment=1650, flowTemplateCode=assureTrade, sellerOrder=false, buyerLoginId=惠州中田贸易, sellerLoginId=宾莉科技, closeOperateType=, couponFee=0, receiverInfo=TradeReceiverInfo(toFullName=中田 12092, toDivisionCode=441302, toPost=516000, toArea=广东省 惠州市 惠城区 江北 金泽物流园二期一号楼四楼-12092, toProvince=null, toCity=null, toCounty=null, toAddress=null, toMobile=***********, toPhone=null, toEmail=null, toTownCode=null), tradeTypeDesc=担保交易, payChannelList=[跨境宝2.0], tradeTypeCode=assureTrade, payTimeout=432000, payTimeoutType=0, payChannelCodeList=[kjpayV2], outOrderId=12092, stepPayAll=false, stepOrderList=null, newStepOrderList=[TradeBaseInfo.NewStepOrder(gmtStart=2025-08-11T17:19:32, gmtPay=2025-08-12T11:05:55, gmtEnd=null, stepNo=1, lastStep=true, stepName=全款交易, activeStatus=1, payStatus=2, logisticsStatus=2, payFee=1548, paidFee=1548, goodsFee=null, adjustFee=-110, discountFee=0, postFee=8, paidPostFee=8)], overSeaOrder=false, sellerCreditLevel=null, buyerFeedback=null, subBuyerLoginId=null, closeReason=null, sellerAlipayId=null, buyerUserId=null, buyerMemo=null, buyerRemarkIcon=null, refundStatus=null, remark=null, preOrderId=null, confirmedTime=null, closeRemark=null, stepAgreementPath=null, refundStatusForAs=null, sellerUserId=null, buyerAlipayId=null, refundId=null, inventoryMode=null), orderBizInfo=TradeOrderBizInfo(odsCyd=false, creditOrderDetail=null, preOrderInfo=null, lstOrderInfo=null, accountPeriodTime=null, creditOrder=false, dropShipping=false, erpBuyerUserId=null, erpOrderId=null, erpBuyerOrgId=null, isCz=null, isDz=null, dz=null, dropshipping=false, shippingInsurance=givenByAnXinGou, hyperLinkCangFaOrder=null, hyperLinkOrder=null, hyperLinkSecondStepOrder=null, hyperLinkShipType=null, lightningWarehouse=null, aeDoorPickUp=null), tradeTerms=[TradeTermsInfo(payStatus=2, payTime=2025-08-12T11:05:55, payWay=13, phasAmount=1548, phase=*****************, phaseCondition=null, phaseDate=null, cardPay=false, expressPay=false, payWayDesc=支付平台)], productItems=[TradeProductItem(cargoNumber=null, description=null, itemAmount=1540, name=ANBERNIC外款RG557 安卓14掌机大屏电容摇杆复古怀旧款老式串流连, price=1650, productId=************, productImgUrl=[http://cbu01.alicdn.com/img/ibank/O1CN01GRITEK1Bs33J0N6uW_!!0-0-cib.80x80.jpg, http://cbu01.alicdn.com/img/ibank/O1CN01GRITEK1Bs33J0N6uW_!!0-0-cib.jpg], productSnapshotUrl=https://trade.1688.com/order/offer_snapshot.htm?order_entry_id=2873679168847540788, quantity=1, refund=0, skuId=*************, sort=null, status=waitbuyerreceive, subItemId=2873679168847540788, type=common, unit=台, weight=null, weightUnit=null, productCargoNumber=RG557, skuInfos=[TradeProductItem.TradeSkuInfo(name=型号, value=RG557 紫透 英文), TradeProductItem.TradeSkuInfo(name=产品颜色, value=标配12+256G 无游戏)], entryDiscount=-11000, specId=02dcb6fd6d982513dad3db03e91ea24f, quantityFactor=1, statusStr=等待买家收货, refundStatus=null, refundId=null, refundIdForAs=null, subItemIdString=2873679168847540788, closeReason=null, categoryId=null, unitPrice=null, logisticsStatus=2, gmtCreate=2025-08-11T17:19:31, gmtModified=2025-08-12T11:34:11, gmtCompleted=null, gmtPayExpireTime=null, sharePostage=8, guaranteesTerms=[TradeGuaranteeTermsInfo(assuranceInfo=卖家承诺买家签收货物后7天内，在“商品完好”的情形下，向买家提供退货服务。, assuranceType=qtwlybt, qualityAssuranceType=7天无理由退货, value=null), TradeGuaranteeTermsInfo(assuranceInfo=“满足相应条件时，用户在退货寄出后，享受极速退款到账。, assuranceType=lsjst_s, qualityAssuranceType=极速退款, value=null), TradeGuaranteeTermsInfo(assuranceInfo=卖家在承诺买家保障服务的基础上，向买家提供“48小时发货延必赔”的服务。在买家通过支付宝担保交易完成付款或通过货到付款下单成功后，卖家承诺在买家支付成功起48小时内发货。如卖家未履行前述承诺，买家可在指定期限内发起维权，并申请赔付。, assuranceType=ssbxsfh, qualityAssuranceType=48小时发货, value=null)])], nativeLogistics=TradeNativeLogisticsInfo(address=江北 金泽物流园二期一号楼四楼-12092, area=惠城区, areaCode=441302, city=惠州市, contactPerson=中田 12092, fax=null, mobile=***********, province=广东省, telephone=null, zip=516000, logisticsItems=[TradeNativeLogisticsInfo.LogisticsItem(id=237188048240929, deliveredTime=2025-08-12T11:34:11, logisticsCode=LP00754686241372, type=0, status=alreadysend, gmtModified=2025-08-12T11:34:11, gmtCreate=2025-08-12T11:34:11, carriage=null, fromProvince=广东省, fromCity=深圳市, fromArea=龙岗区, fromAddress=坪地街道 坪西社区高桥第一工业区12号3栋336, fromPhone=86-0755-***********, fromMobile=***********, fromPost=518000, logisticsCompanyId=3, logisticsCompanyNo=ZTO, logisticsCompanyName=中通快递(ZTO), logisticsBillNo=78930896233428, subItemIds=2873679168847540788, toProvince=null, toCity=惠州市, toArea=惠城区, toAddress=江北 金泽物流园二期一号楼四楼-12092, toPhone=null, toMobile=null, toPost=516000, noLogisticsName=, noLogisticsTel=, noLogisticsBillNo=, noLogisticsCondition=-1)], townCode=null, town=null), orderInvoiceInfo=null, guaranteesTerms=null, orderRateInfo=TradeOrderRateInfo(buyerRateStatus=5, sellerRateStatus=5, buyerRateList=null, sellerRateList=null), overseasExtraAddress=null, customs=null, quoteList=null, extAttributes=[], fromEncryptOrder=false, encryptOutOrderInfo=null, overseaLogisticsInfo=null)
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107322801049608192]:[0] 订单数据完整性检查结果: orderId=2873679168847540788, isComplete=false, isNewVersion=false, missing=采购订单, 供应商订单, 订单项
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 开始同步和补齐订单数据: orderId=2873679168847540788, needsSync=true
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 开始补齐缺失的订单数据: orderId=2873679168847540788, missing=采购订单, 供应商订单, 订单项
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 创建采购订单成功: orderId=2873679168847540788, purchaseOrderId=728183315443874
2025-08-12 15:05:29 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 创建供应商订单成功: orderId=2873679168847540788, supplierOrderCount=1
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107322801049608192]:[0] 数据库不存在，从API获取商品详情, id: ************
2025-08-12 15:05:30 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107322801049608192]:[0] 签名因子: param2/1/com.alibaba.fenxiao.crossborder/product.search.queryProductDetail/8390330_aop_timestamp1754982330113access_token5fd99355-518b-47a6-b83e-0503223e0665offerDetailParam{"offerId":************,"country":"en"} 签名: 62B1A525B5FBFE575DF1DA2F00BCE122715FFF31
2025-08-12 15:05:30 DEBUG [reactor-http-nio-6] [tid::uId::ip::os::browser:] c.f.s.manager.support.alibaba.impl.ProductManager - [1107321438160855040]:[0] result: GoodsDetailResponse.Result(success=true, code=200, message=null, result=GoodsDetailResponse.ProductDetail(offerId=************, categoryId=122314006, categoryName=null, subject=ANBERNIC外款RG557 安卓14掌机大屏电容摇杆复古怀旧款老式串流连, subjectTrans=ANBERNIC RG557 Android 14 Handheld Large Screen Capacitive Rocker Retro Nostalgic Old-fashioned String Linger, description=<div id="offer-template-0"></div><div style="width: 790.0px;">
<div class="rich-text-component" style="width: 395.0px;padding: 10.0px;word-break: break-all;white-space: break-spaces;font-family: ali-webfont;zoom: 2;font-size: 0.0px;box-sizing: border-box;">
<p><span style="font-size: 28.0px;">产品控价，请不要乱价，一旦投诉不撤销。具体控价，请咨询客服。</span></p>
<p><span style="font-size: 28.0px;">请将你们上架的链接发过来报备，以免被投诉</span></p>
</div>
<img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01Xuu9Ah20wD3TIvxOL_!!**********-0-cib.jpg" usemap="#_sdmap_1"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01QCxLf820wD3Q1G44G_!!**********-0-cib.jpg" usemap="#_sdmap_2"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01FlyISM20wD3RtGCuB_!!**********-0-cib.jpg" usemap="#_sdmap_3"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01kdg18C20wD3Q1HsKM_!!**********-0-cib.jpg" usemap="#_sdmap_4"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01hLytSS20wD3S8s52N_!!**********-0-cib.jpg" usemap="#_sdmap_5"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01y9dbTJ20wD3RjObPi_!!**********-0-cib.jpg" usemap="#_sdmap_6"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01cAACtw20wD3RjOntC_!!**********-0-cib.jpg" usemap="#_sdmap_7"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01V75Nng20wD3RtG4bI_!!**********-0-cib.jpg" usemap="#_sdmap_8"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01SQTDCc20wD3RjOXGN_!!**********-0-cib.jpg" usemap="#_sdmap_9"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01R6nE1G20wD3LBaHe9_!!**********-0-cib.jpg" usemap="#_sdmap_10"/> <img style="display: block;width: 100.0%;height: auto;" src="https://cbu01.alicdn.com/img/ibank/O1CN01EIDLO620wD3R1H8Ru_!!**********-0-cib.jpg" usemap="#_sdmap_11"/></div>, mainVideo=null, detailVideo=null, productImage=GoodsDetailResponse.ProductImage(images=[https://cbu01.alicdn.com/img/ibank/O1CN01HRc2gl20wD3S8c24L_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01bfOETa20wD3Q0wGDV_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01TKS7u920wD3RG4SA5_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01kQktRh20wD3Rj3rN5_!!**********-0-cib.jpg, https://cbu01.alicdn.com/img/ibank/O1CN01otoZl920wD3Q0yTQr_!!**********-0-cib.jpg], whiteImage=null), productAttribute=[GoodsDetailResponse.ProductAttribute(attributeId=2176, attributeName=品牌, value=ANBERNIC, attributeNameTrans=Brand, valueTrans=ANBERNIC), GoodsDetailResponse.ProductAttribute(attributeId=3151, attributeName=型号, value=RG557 紫透 中文, attributeNameTrans=Model, valueTrans=Rg557 purple transparent chinese), GoodsDetailResponse.ProductAttribute(attributeId=3151, attributeName=型号, value=RG557 紫透 英文, attributeNameTrans=Model, valueTrans=Rg557 purple transparent english), GoodsDetailResponse.ProductAttribute(attributeId=3151, attributeName=型号, value=RG557 白色 中文, attributeNameTrans=Model, valueTrans=Rg557 white chinese), GoodsDetailResponse.ProductAttribute(attributeId=3151, attributeName=型号, value=RG557 白色 英文, attributeNameTrans=Model, valueTrans=Rg557 white english), GoodsDetailResponse.ProductAttribute(attributeId=1398, attributeName=货号, value=RG557, attributeNameTrans=Item number, valueTrans=RG557), GoodsDetailResponse.ProductAttribute(attributeId=346, attributeName=产地, value=中国广东, attributeNameTrans=Origin, valueTrans=Guangdong, china), GoodsDetailResponse.ProductAttribute(attributeId=100000691, attributeName=货源类别, value=现货, attributeNameTrans=Supply category, valueTrans=Spot goods), GoodsDetailResponse.ProductAttribute(attributeId=9886, attributeName=商品类型, value=全新, attributeNameTrans=Product type, valueTrans=Brand new), GoodsDetailResponse.ProductAttribute(attributeId=364, attributeName=产品类别, value=摇杆游戏机, attributeNameTrans=Product category, valueTrans=Joystick game console), GoodsDetailResponse.ProductAttribute(attributeId=7759, attributeName=游戏类型, value=RPG角色扮演, attributeNameTrans=Game type, valueTrans=Rpg role playing), GoodsDetailResponse.ProductAttribute(attributeId=30182, attributeName=游戏版本, value=正版/原版游戏软件, attributeNameTrans=Game version, valueTrans=Genuine/original game software), GoodsDetailResponse.ProductAttribute(attributeId=20573, attributeName=操作系统, value=安卓14, attributeNameTrans=Operating system, valueTrans=Android 14), GoodsDetailResponse.ProductAttribute(attributeId=2059, attributeName=内存, value=12+256G内置, attributeNameTrans=Memory, valueTrans=12+256g built-in), GoodsDetailResponse.ProductAttribute(attributeId=1551, attributeName=接口类型, value=USB, attributeNameTrans=Interface type, valueTrans=USB), GoodsDetailResponse.ProductAttribute(attributeId=138180009, attributeName=视频/音频接口, value=Type-C, attributeNameTrans=Video / audio interface, valueTrans=Type-C), GoodsDetailResponse.ProductAttribute(attributeId=2985, attributeName=网络接口, value=wifi, attributeNameTrans=Network interface, valueTrans=wifi), GoodsDetailResponse.ProductAttribute(attributeId=1862, attributeName=连接方式, value=无线, attributeNameTrans=Connection method, valueTrans=Wireless), GoodsDetailResponse.ProductAttribute(attributeId=21594, attributeName=附加功能, value=MP4视频播放器, attributeNameTrans=Additional features, valueTrans=Mp4 video player), GoodsDetailResponse.ProductAttribute(attributeId=138172009, attributeName=电玩分级, value=入门级, attributeNameTrans=Video game classification, valueTrans=Entry level), GoodsDetailResponse.ProductAttribute(attributeId=138172009, attributeName=电玩分级, value=发烧级, attributeNameTrans=Video game classification, valueTrans=Fever grade), GoodsDetailResponse.ProductAttribute(attributeId=138172009, attributeName=电玩分级, value=精英级, attributeNameTrans=Video game classification, valueTrans=Elite level), GoodsDetailResponse.ProductAttribute(attributeId=138172009, attributeName=电玩分级, value=骨灰级, attributeNameTrans=Video game classification, valueTrans=Hardcore), GoodsDetailResponse.ProductAttribute(attributeId=31480, attributeName=适用人数, value=单人, attributeNameTrans=Applicable number of people, valueTrans=Single), GoodsDetailResponse.ProductAttribute(attributeId=7170, attributeName=适用年龄段, value=通用, attributeNameTrans=Applicable age group, valueTrans=General), GoodsDetailResponse.ProductAttribute(attributeId=357, attributeName=产品尺寸, value=22.3*8.9*1.5CM, attributeNameTrans=Product size, valueTrans=22.3*8.9*1.5CM), GoodsDetailResponse.ProductAttribute(attributeId=6645, attributeName=产品重量, value=600g, attributeNameTrans=Product weight, valueTrans=600g), GoodsDetailResponse.ProductAttribute(attributeId=8446, attributeName=适用送礼场合, value=员工福利, attributeNameTrans=Suitable for gift-giving occasions, valueTrans=Employee benefits), GoodsDetailResponse.ProductAttribute(attributeId=1498, attributeName=加印LOGO, value=可以, attributeNameTrans=Print logo, valueTrans=Can), GoodsDetailResponse.ProductAttribute(attributeId=100000729, attributeName=加工定制, value=是, attributeNameTrans=Processing and customization, valueTrans=Is), GoodsDetailResponse.ProductAttribute(attributeId=1477, attributeName=加工方式, value=贴牌加工, attributeNameTrans=Processing method, valueTrans=Oem processing), GoodsDetailResponse.ProductAttribute(attributeId=100017842, attributeName=最快出货时间, value=1-3天, attributeNameTrans=Fastest shipping time, valueTrans=1-3 days), GoodsDetailResponse.ProductAttribute(attributeId=100020070, attributeName=是否支持一件代发, value=支持, attributeNameTrans=Whether to support dropshipping, valueTrans=Support), GoodsDetailResponse.ProductAttribute(attributeId=100273971, attributeName=包装清单, value=主机、说明书、数据线、彩盒, attributeNameTrans=Package list, valueTrans=Host, manual, data cable, color box), GoodsDetailResponse.ProductAttribute(attributeId=7119, attributeName=上市时间, value=2025.4, attributeNameTrans=Time on the market, valueTrans=2025.4), GoodsDetailResponse.ProductAttribute(attributeId=9910, attributeName=售后服务, value=其他, attributeNameTrans=After sales service, valueTrans=Other), GoodsDetailResponse.ProductAttribute(attributeId=100040752, attributeName=发票, value=其他, attributeNameTrans=Invoice, valueTrans=Other), GoodsDetailResponse.ProductAttribute(attributeId=374, attributeName=产品颜色, value=标配12+256G 无游戏, attributeNameTrans=Product color, valueTrans=Standard configuration 12+256g without games), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=ebay, attributeNameTrans=Main downstream platforms, valueTrans=ebay), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=亚马逊, attributeNameTrans=Main downstream platforms, valueTrans=Amazon), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=wish, attributeNameTrans=Main downstream platforms, valueTrans=wish), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=速卖通, attributeNameTrans=Main downstream platforms, valueTrans=Aliexpress), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=独立站, attributeNameTrans=Main downstream platforms, valueTrans=Independent station), GoodsDetailResponse.ProductAttribute(attributeId=*********, attributeName=主要下游平台, value=LAZADA, attributeNameTrans=Main downstream platforms, valueTrans=LAZADA), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=非洲, attributeNameTrans=Main sales area, valueTrans=Africa), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=欧洲, attributeNameTrans=Main sales area, valueTrans=Europe), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=南美, attributeNameTrans=Main sales area, valueTrans=South america), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=东南亚, attributeNameTrans=Main sales area, valueTrans=Southeast asia), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=北美, attributeNameTrans=Main sales area, valueTrans=North america), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=东北亚, attributeNameTrans=Main sales area, valueTrans=Northeast asia), GoodsDetailResponse.ProductAttribute(attributeId=193290002, attributeName=主要销售地区, value=中东, attributeNameTrans=Main sales area, valueTrans=Middle east), GoodsDetailResponse.ProductAttribute(attributeId=193290003, attributeName=有可授权的自有品牌, value=否, attributeNameTrans=There are licensable private brands, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=182282223, attributeName=是否跨境出口专供货源, value=是, attributeNameTrans=Whether to exclusively supply goods for cross-border export, valueTrans=Is), GoodsDetailResponse.ProductAttribute(attributeId=143220385, attributeName=是否专利货源, value=否, attributeNameTrans=Is it a patented source of goods?, valueTrans=No), GoodsDetailResponse.ProductAttribute(attributeId=401, attributeName=风格, value=电竞, attributeNameTrans=Style, valueTrans=Esports)], productSkuInfos=[GoodsDetailResponse.ProductSkuInfo(amountOnSale=717, price=1650.0, jxhyPrice=null, skuId=*************, specId=14f0512b4723fe59b9e599b359bc8be7, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=374, attributeName=产品颜色, attributeNameTrans=Product color, value=标配12+256G 无游戏, valueTrans=Standard configuration 12+256g without games, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=3151, attributeName=型号, attributeNameTrans=Model, value=RG557 紫透 中文, valueTrans=Rg557 purple transparent chinese, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01BzxEXF20wD3SRyCeB_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=1650.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=1650)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=956, price=1650.0, jxhyPrice=null, skuId=*************, specId=02dcb6fd6d982513dad3db03e91ea24f, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=374, attributeName=产品颜色, attributeNameTrans=Product color, value=标配12+256G 无游戏, valueTrans=Standard configuration 12+256g without games, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=3151, attributeName=型号, attributeNameTrans=Model, value=RG557 紫透 英文, valueTrans=Rg557 purple transparent english, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01BzxEXF20wD3SRyCeB_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=1650.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=1650)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=891, price=1650.0, jxhyPrice=null, skuId=*************, specId=a66443b9c75d18d010acec383de907ef, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=374, attributeName=产品颜色, attributeNameTrans=Product color, value=标配12+256G 无游戏, valueTrans=Standard configuration 12+256g without games, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=3151, attributeName=型号, attributeNameTrans=Model, value=RG557 白色 中文, valueTrans=Rg557 white chinese, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01SQTDCc20wD3RjOXGN_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=1650.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=1650)), GoodsDetailResponse.ProductSkuInfo(amountOnSale=939, price=1650.0, jxhyPrice=null, skuId=*************, specId=f06107b2338e443bbd69664eee27d474, skuAttributes=[GoodsDetailResponse.SkuAttribute(attributeId=374, attributeName=产品颜色, attributeNameTrans=Product color, value=标配12+256G 无游戏, valueTrans=Standard configuration 12+256g without games, skuImageUrl=null), GoodsDetailResponse.SkuAttribute(attributeId=3151, attributeName=型号, attributeNameTrans=Model, value=RG557 白色 英文, valueTrans=Rg557 white english, skuImageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01SQTDCc20wD3RjOXGN_!!**********-0-cib.jpg)], cargoNumber=, promotionPrice=null, consignPrice=1650.0, fenxiaoPriceInfo=GoodsDetailResponse.FenxiaoPriceInfo(onePiecePrice=null, offerPrice=1650))], productSaleInfo=GoodsDetailResponse.ProductSaleInfo(amountOnSale=3503, priceRangeList=[GoodsDetailResponse.PriceRange(startQuantity=1, price=1650.0, promotionPrice=null)], quoteType=1, unitInfo=GoodsDetailResponse.UnitInfo(unit=台, transUnit=unit), fenxiaoSaleInfo=GoodsDetailResponse.FenxiaoSaleInfo(onePieceFreePostage=false, startQuantity=1, onePiecePrice=null, offerPrice=null), consignPrice=null, jxhyPrice=null), productShippingInfo=GoodsDetailResponse.ProductShippingInfo(sendGoodsAddressText=广东省深圳市, weight=null, width=null, height=null, length=null, shippingTimeGuarantee=shipIn48Hours, skuShippingInfoList=[GoodsDetailResponse.SkuShippingInfo(specId=14f0512b4723fe59b9e599b359bc8be7, skuId=*************, width=16.0, length=28.0, height=6.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=02dcb6fd6d982513dad3db03e91ea24f, skuId=*************, width=16.0, length=28.0, height=6.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=a66443b9c75d18d010acec383de907ef, skuId=*************, width=16.0, length=28.0, height=6.0, weight=600), GoodsDetailResponse.SkuShippingInfo(specId=f06107b2338e443bbd69664eee27d474, skuId=*************, width=16.0, length=28.0, height=6.0, weight=600)], skuShippingDetails=[GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=16.0, length=28.0, height=6.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=16.0, length=28.0, height=6.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=16.0, length=28.0, height=6.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填), GoodsDetailResponse.SkuShippingDetail(skuId=*************, width=16.0, length=28.0, height=6.0, weight=0.6, officialLength=null, officialWidth=null, officialHeight=null, officialWeight=null, aiWeight=null, aiWeightAccuracy=null, pkgSizeSource=商家自填)], pkgSizeSource=null), isJxhy=false, sellerOpenId=BBBKXYH_7ICW09GGpxOhBiOEA, minOrderQuantity=1, batchNumber=null, status=published, tagInfoList=[GoodsDetailResponse.TagInfoList(key=isOnePsale, value=false), GoodsDetailResponse.TagInfoList(key=isSupportMix, value=false), GoodsDetailResponse.TagInfoList(key=isOnePsaleFreePostage, value=false), GoodsDetailResponse.TagInfoList(key=noReason7DReturn, value=false), GoodsDetailResponse.TagInfoList(key=1688_yx, value=false)], traceInfo=object_id@************^object_type@offer, sellerMixSetting=null, productCargoNumber=RG557, sellerDataInfo=GoodsDetailResponse.SellerDataInfo(tradeMedalLevel=5, compositeServiceScore=4.5, logisticsExperienceScore=4.0, disputeComplaintScore=5.0, offerExperienceScore=4.0, consultingExperienceScore=3.5, repeatPurchasePercent=0.6980179458913564, afterSalesExperienceScore=4.3, collect30DayWithin48HPercent=1.0, qualityRefundWithin30Day=0.0007272727272727272), soldOut=437, channelPrice=null, promotionModel=null, tradeScore=5.0, topCategoryId=7, secondCategoryId=727, thirdCategoryId=122314006, sellingPoint=null, offerIdentities=[powerful_merchants, tp_member], createDate=2025-04-29 11:30:52, isSelect=false, certificateList=[], promotionUrl=https://detail.1688.com/offer/************.html?kjSource=pc))
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.d.c.product.PdcProductDetailConvertMapping - [1107322801049608192]:[0] 生成新ID: *************** 对应平台商品ID: ************
2025-08-12 15:05:30 INFO  [naya-task-pool12] [tid::uId::ip::os::browser:] c.f.support.alibaba.service.impl.ToolsServiceImpl - [1107322801049608192]:[0] 开始解密旺旺昵称, openUid: BBBKXYH_7ICW09GGpxOhBiOEA
2025-08-12 15:05:30 INFO  [naya-task-pool12] [tid::uId::ip::os::browser:] c.fulfillmen.support.alibaba.sign.AlibabaSignature - [1107322801049608192]:[0] 签名因子: param2/1/com.alibaba.account/wangwangnick.openuid.decrypt/8390330_aop_timestamp1754982330444access_token5fd99355-518b-47a6-b83e-0503223e0665openUidBBBKXYH_7ICW09GGpxOhBiOEA 签名: BF80729314C2649FAF336AFF8EF3FB14F7B07742
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] com.fulfillmen.shop.common.util.MetaInfoHashUtils - [1107322801049608192]:[0] 计算metaInfoHash [pdcProductMappingId=*************** => platformProductId=************]: hash=0ab6d3e79c91c9fd6701d295ebac0c22, metaInfo长度=15009
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107322801049608192]:[0] 商品详情同步到数据库完成, id: ************
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107322801049608192]:[0] 商品详情更新到缓存完成, id: ************
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107322801049608192]:[0] 自动同步功能已关闭，跳过事件发布
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107322801049608192]:[0] 获取或同步产品数据: platformProductId=************
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107322801049608192]:[0] 从缓存获取商品详情, id: ************
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107322801049608192]:[0] 从PdcProductMapping同步产品数据: platformProductId=************
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107322801049608192]:[0] 开始同步产品数据，platformProductId: ************
2025-08-12 15:05:30 DEBUG [naya-task-pool13] [tid::uId::ip::os::browser:] c.f.s.m.c.r.impl.PdcProductMappingRepositoryImpl - [1107322801049608192]:[0] 从缓存获取商品详情, id: ************
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107322801049608192]:[0] SPU创建成功，spuId: 728183319699621
2025-08-12 15:05:30 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.service.impl.ProductSyncServiceImpl - [1107322801049608192]:[0] 多规格SKU创建成功，数量: 4
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 创建订单项成功: orderId=2873679168847540788, orderItemCount=1
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.s.impl.OrderDataSyncServiceImpl - [1107322801049608192]:[0] 订单数据补齐完成: orderId=2873679168847540788, purchaseOrderId=728183315443874, supplierOrderCount=1, orderItemCount=1
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107322801049608192]:[0] 路由到具体的业务处理逻辑: orderId=2873679168847540788, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 15:05:49 INFO  [naya-task-pool16] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107322801049608192]:[0] 处理数据同步完成事件: orderId=2873679168847540788, messageType=ORDER_BUYER_VIEW_ORDER_PAY, eventTime=2025-08-12T15:05:49.585204
2025-08-12 15:05:49 INFO  [naya-task-pool16] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107322801049608192]:[0] 数据同步成功: orderId=2873679168847540788, purchaseOrderNo=N2508121505297471913, isDataSync=true, isCompatibility=false
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107322801049608192]:[0] 开始处理订单支付事件: orderId=2873679168847540788
2025-08-12 15:05:49 DEBUG [naya-task-pool16] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107322801049608192]:[0] 更新同步指标: orderId=2873679168847540788
2025-08-12 15:05:49 DEBUG [naya-task-pool16] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107322801049608192]:[0] 清理临时数据: orderId=2873679168847540788
2025-08-12 15:05:49 DEBUG [naya-task-pool16] [tid::uId::ip::os::browser:] c.f.s.m.s.a.w.event.OrderWebhookEventListener - [1107322801049608192]:[0] 数据同步完成事件处理完成: orderId=2873679168847540788
2025-08-12 15:05:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107322801049608192]:[0] 更新本地订单状态为已支付: orderId=2873679168847540788
2025-08-12 15:05:49 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107322801049608192]:[0] 通知WMS订单已支付: orderId=2873679168847540788
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.webhook.handler.OrderContextRecord - [1107322801049608192]:[0] 重新 wms 同步商品信息: WmsPurchaseOrderDetailsItemRes(orderId=2873679168847540788, skuId=*************, enName=ANBERNIC RG557 Android 14 Handheld Large Screen Capacitive Rocker Retro Nostalgic Old-fashioned String Linger, cnName=ANBERNIC外款RG557 安卓14掌机大屏电容摇杆复古怀旧款老式串流连, weight=0.000, quantity=1, variantId=02dcb6fd6d982513dad3db03e91ea24f, productId=************, unitPrice=1650.000, originUnitPrice=1650.000, finalUnitPrice=1650.000, subTotal=1650.000, originSubTotalAmount=1650.000, finalSubTotalAmount=1548.000, imageUrl=https://cbu01.alicdn.com/img/ibank/O1CN01BzxEXF20wD3SRyCeB_!!**********-0-cib.jpg, productUrl=null, attributes=null, skuAttrib=产品颜色:标配12+256G 无游戏;型号:RG557 紫透 英文;, skuAttribEn=Product color:Standard configuration 12+256g without games;Model:Rg557 purple transparent english;)
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.shop.manager.support.wms.impl.WmsManagerImpl - [1107322801049608192]:[0] 开始更新 WMS 采购订单请求，采购单号: [{"purchaseNo":"C120922025081117193000004","orderId":2873679168847540788,"alibabaFinalAmount":1548,"alibabaTotalAmount":1548,"plusDiscount":110.000,"couponDiscount":0,"status":2,"paymentTime":"2025-08-12 11:05:55","orderDetails":[{"quantity":1,"variantId":"02dcb6fd6d982513dad3db03e91ea24f","productId":"************","finalUnitPrice":1540.00,"finalSubTotalAmount":1540,"sku":"*************"}]}]
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.processor.impl.OrderPaymentProcessor - [1107322801049608192]:[0] 订单支付事件处理完成: orderId=2873679168847540788
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107322801049608192]:[0] 订单消息处理完成: orderId=2873679168847540788, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.a.w.service.impl.OrderWebhookServiceImpl - [1107322801049608192]:[0] 订单webhook消息处理完成: orderId=2873679168847540788, msgId=144548350945, messageType=ORDER_BUYER_VIEW_ORDER_PAY
2025-08-12 15:05:49 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.m.s.alibaba.webhook.handler.OrderHandler - [1107322801049608192]:[0] 订单webhook消息处理完成: msgId=144548350945, type=ORDER_BUYER_VIEW_ORDER_PAY, orderId=2873679168847540788
2025-08-12 15:05:50 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.s.manager.support.alibaba.webhook.WebhookApi - [1107322801049608192]:[0] Webhook消息处理完成，处理时间: 21051ms，处理结果数量: 1
2025-08-12 15:05:50 INFO  [XNIO-1 task-2] [tid:10000:uId::ip:127.0.0.1:os::browser:] c.f.starter.log.interceptor.handler.LogInterceptor - [1107322801049608192]:[0] [POST] /alibaba/callback 200 21051ms
2025-08-12 15:05:50 DEBUG [XNIO-1 task-2] [tid:10000:uId::ip::os::browser:] c.f.shop.common.tenant.EnhancedTenantContextHolder - [1107322801049608192]:[0] 清理增强租户上下文
2025-08-12 15:05:50 DEBUG [XNIO-1 task-2] [tid::uId::ip::os::browser:] com.fulfillmen.shop.config.filter.TenantFilter - [1107322801049608192]:[0] 过滤器清理租户上下文完成
2025-08-12 15:06:57 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Close gracefully!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-3] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [Thread-4] [tid::uId::ip::os::browser:] m.a.cosid.segment.concurrent.DefaultPrefetchWorker - Shutdown!
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.spring.redis.SpringRedisMachineIdDistributor - Revert Remote [MachineState{machineId=2, lastTimeStamp=1754982417566}] instanceId:[InstanceId{instanceId=**************:6915, stable=false}] @ namespace:[fulfillmen-shop].
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Commencing graceful shutdown. Waiting for active requests to complete
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] o.s.boot.web.embedded.undertow.UndertowWebServer - Graceful shutdown complete
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow - stopping server: Undertow - 2.3.18.Final
2025-08-12 15:06:57 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-12 15:06:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] m.a.c.s.concurrent.PrefetchWorkerExecutorService - Shutdown!
2025-08-12 15:06:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.alicp.jetcache.support.DefaultMetricsManager - cache stat canceled
2025-08-12 15:06:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-12 15:06:59 INFO  [SpringApplicationShutdownHook] [tid::uId::ip::os::browser:] com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
