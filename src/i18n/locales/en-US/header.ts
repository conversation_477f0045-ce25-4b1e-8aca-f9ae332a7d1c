import type { HeaderMessages } from '../../types'

const header: HeaderMessages = {
    about: 'About',
    guide: 'Guide',
    faqs: 'FAQs',
    commission: 'Commission',
    services: 'Services',
    login: 'Login',
    register: 'Register',
    imageSizeError: 'Image size must be less than {size}MB',
    uploadingImage: 'Uploading image...',
    uploadSuccess: 'Image uploaded, searching for similar products...',
    uploadError: 'Image upload failed, please try again later',
    searchProducts: 'Search products',
    searchProductsError: 'Search request failed, please try again later',
    urlSearchDetected: 'Detected product link, redirecting to product details page...',
    urlParseFailed: 'Product link parsing failed, please check if the link is correct',
    search: {
        placeholder: 'Search products...',
        button: 'Search',
    },
    trackOrders: 'Track Orders',
    shippingCalculation: 'Shipping Calculation',
    affiliate: 'Affiliate',
    addresses: 'Addresses',
    browseHistory: 'Browse History',
    saleAmount: 'Sale Amount',
    wallet: 'Wallet',
    account: 'Account',
    cart: 'Cart',
    language: 'Language',
    currency: 'Currency',
    settings: 'Settings',
    nav: {
        home: 'Home',
        products: 'Products',
        about: 'About',
        contact: 'Contact',
    },
    popularCategories: {
        allCategories: 'All Categories',
        popularCategories: 'Popular Categories',
        placeholder: 'Search categories...',
        grocery: 'Grocery & Essentials',
        giftFinder: 'Gift Finder',
        toyShop: 'Toy Shop',
        home: 'Home',
        fashion: 'Fashion',
        giftCard: 'Gift Card',
        household: 'Household Goods',
        dropshipping: 'Dropshipping',
        electronics: 'Electronics',
        sports: 'Sports',
        books: 'Books',
        beauty: 'Beauty',
    },
    orderStatus: {
        awaitingPayment: 'Awaiting Payment',
        pending: 'Pending',
        processing: 'Processing',
        dispatched: 'Dispatched',
        completed: 'Completed',
        closed: 'Closed',
    },
}

export default header
