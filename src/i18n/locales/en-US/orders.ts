export default {
    title: 'My Orders',
    subtitle: 'View and manage your orders',
    search: {
        placeholder: 'Search by order number or product name or SKU ID or buyer name',
    },
    loading: 'Loading...',
    orderNo: 'Order No',
    orderInfo: 'Order Info',
    progressAmount: 'Progress & Amount',
    quantity: 'Quantity',
    types: 'types of products',
    buyerMessage: 'Buyer Message',
    totalQuantity: 'Total Quantity',
    productTypes: 'Product Types',
    orderStatus: 'Order Status',
    timeRange: 'Time Range',
    completion: 'Completion',
    quantityLabel: 'Quantity: ',
    quantityValue: '{quantity} items · {types} types',
    timeLabel: 'Time: ',
    buyerMessageLabel: 'Message: ',
    progressTitle: 'Order Progress {percentage}%',
    lastUpdated: 'Updated: ',
    previewModalTitle: 'Product Preview',
    selectAll: 'Select All Current Page',
    memoTitle: 'Edit Memo',
    memoLabel: 'Memo Label',
    memoText: 'Memo Text',
    memoPlaceholder: 'Information is only visible to yourself, and the memo information is recorded as your memo for this order',
    countdownText: 'After, the transaction will be closed',
    productDetails: 'Product Details',
    loadingProducts: 'Loading product information...',
    createTime: 'Order Time',
    clickToPreview: 'Click to Preview',
    clickToViewDetail: 'Click to view order details',
    copySkuHint: 'Copy SKU to Clipboard',
    productsInfo: 'Product Info',
    // Table headers internationalization
    tableHeaders: {
        productImage: 'Product Image',
        productName: 'Product Name',
        unitPrice: 'Unit Price',
        quantity: 'Quantity',
        actions: 'Actions',
        orderNo: 'Order No',
        status: 'Status',
        orderTime: 'Order Time',
        subTotal: 'Subtotal',
        orderAmount: 'Order Amount',
        viewProduct: 'View Product',
        buyAgain: 'Buy Again'
    },
    empty: {
        title: 'No Orders Yet',
        description: "You don't have any orders yet. Let's go shopping!",
        browsing: 'Browse Products',
    },
    status: {
        all: 'All',
        pending: 'Pending Payment',
        paid: 'Paid',
        purchasing: 'Purchasing',
        shipped: 'Shipped',
        delivered: 'Delivered',
        inWarehouse: 'In Warehouse',
        completed: 'Completed',
        cancelled: 'Cancelled',
        unknown: 'Unknown Status',
        label: 'Status',
    },
    itemStatus: {
        pendingPayment: 'Pending Payment',
        paid: 'Paid',
        purchasing: 'Purchasing',
        shipped: 'Shipped',
        inWarehouse: 'In Warehouse',
        completed: 'Completed',
        cancelled: 'Cancelled',
        unknown: 'Unknown Status',
    },
    product: {
        label: 'Product',
    },
    purchaseInfo: {
        label: 'Purchase Info',
    },
    amount: {
        label: 'Amount',
    },
    
    items: {
        title: 'Order Items',
    },
    actions: {
        label: 'Actions',
        viewDetail: 'Details',
        viewTracking: 'Track Package',
        pay: 'Pay Now',
        confirmReceipt: 'Confirm Receipt',
        cancel: 'Cancel Order',
        refund: 'Refund',
        more: 'More',
        preview: 'Preview',
        batchCancel: 'Batch Cancel',
        clickHere: 'Click here',
        cancelHint: 'If you don\'t want to purchase, you can close the transaction',
        addMemo: 'Add memo',
    },
    error: {
        loadFailed: 'Failed to load order list',
        confirmFailed: 'Failed to confirm receipt',
        cancelFailed: 'Failed to cancel order',
        payFailed: 'Failed to pay order',
        reasonRequired: 'Cancellation reason is required',
        batchCancelFailed: 'Failed to batch cancel orders',
        memoRequired: 'Memo is required',
        memoFailed: 'Failed to add memo',
    },
    confirm: {
        title: 'Confirm Action',
        receipt: 'Are you sure you have received the products?',
        payMessage: 'Are you sure you want to pay for this order?',
        cancelReason: 'Please enter the reason for cancellation',
        batchCancelMessageWithReason: 'Are you sure you want to cancel the selected {count} orders? Please enter the reason:',
        reasonPlaceholder: 'For example: Out of stock, incorrect information, etc.',
    },
    cancel: {
        title: 'Cancel Order',
        reason: 'Please enter the reason for cancellation',
        placeholder: 'Enter reason for cancellation (optional)',
        selectReason: 'Select cancellation reason',
        otherReason: 'Other reason',
        description: 'This order has been cancelled',
        reasons: {
            outOfStock: 'Out of stock',
            tooHighPrice: 'Too high price',
            informationError: 'Information error',
            duplicateOrder: 'Duplicate order',
            other: 'Other',
        },
    },
    success: {
        confirmed: 'Receipt confirmed successfully',
        cancelled: 'Order cancelled successfully',
        paid: 'Payment successful',
        batchCancelled: 'Batch cancel orders successfully',
        memoAdded: 'Memo added successfully',
    },
    preview: {
        productTitle: 'Product Title',
        specs: 'Specifications',
        quantity: 'Quantity',
        unitPrice: 'Unit Price',
        loadError: 'Failed to load product information.',
        noSpecs: 'No specification information',
    },
    dateRange: {
        start: 'Start Date',
        end: 'End Date',
        clear: 'Clear',
        separator: 'To',
    },
    dateFilter: {
        last7days: '7d',
        last15days: '15d',
        last30days: '30d',
    },
    productDetail: {
        title: 'Product Details',
        subtitle: 'View detailed product information',
        loading: 'Loading...',
        productImage: 'Product Image',
        productInfo: 'Product Information',
        productName: 'Product Name',
        productSpecs: 'Product Specs',
        productSku: 'Product SKU',
        productPrice: 'Product Price',
        productQuantity: 'Product Quantity',
        productTotalPrice: 'Product Total Price',
        productDescription: 'Product Description',
        productActions: 'Product Action',

    },
    // Order detail page translations
    detail: {
        title: 'Order Details',
        subtitle: 'View detailed order information',
        loading: 'Loading...',
        currentStatus: 'Current Order Status',
        orderInfo: 'Order Information',
        orderNo: 'Purchase Order No',
        orderStatus: 'Order Status',
        createTime: 'Order Time',
        updateTime: 'Update Time',
        payTime: 'Payment Time',
        deliveryTime: 'Delivery Time',
        completedTime: 'Completion Time',
        progress: 'Order Progress',
        buyerMessage: 'Buyer Message',
        items: 'Order Items',
        itemsCount: 'items',
        quantity: 'Quantity',
        noItems: 'No product information available',
        priceDetails: 'Price Details',
        merchandiseAmount: 'Merchandise Amount',
        shippingAmount: 'Shipping Fee',
        serviceFee: 'Service Fee',
        discountAmount: 'Discount Amount',
        totalAmount: 'Total Amount',
        actions: 'Order Actions',
        unitPrice: 'Unit Price',
        totalPrice: 'Total Price',
        merchandise: 'Merchandise',
        logisticsStatus: 'Logistics Status',
        specs: 'Specs',
        memoTitle: 'Edit Memo',
        memoLabel: 'Memo Label',
        memoText: 'Memo Text',
        memoPlaceholder: 'Information is only visible to yourself, and the memo information is recorded as your memo for this order',
        receiverName: 'Receiver Name',
        receiverPhone: 'Receiver Phone',
        deliveryAddress: 'Delivery Address',
        warehouseName: 'Warehouse Name',
        countdownText: 'After, the transaction will be closed',
        countdown: 'Countdown',
        days: 'days',
        hours: 'hours',
        minutes: 'minutes',
        status: {
            paymentPending: 'Payment Pending',
            paid: 'Paid',
            purchasing: 'Purchasing',
            shipped: 'Shipped',
            inWarehouse: 'In Warehouse',
            completed: 'Completed',
        },
        error: {
            title: 'Load Failed',
            invalidOrderNo: 'Invalid order number',
            loadFailed: 'Failed to load order details',
            retry: 'Retry',
        },
        trackingComingSoon: 'Tracking feature coming soon',
        refundComingSoon: 'Refund feature coming soon',
        addMemoPrompt: 'Please enter order memo',
        addMemoTitle: 'Add Memo',
    },
}