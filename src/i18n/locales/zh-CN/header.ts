import type { HeaderMessages } from '../../types'

const header: HeaderMessages = {
    about: '关于我们',
    guide: '使用指南',
    faqs: '常见问题',
    commission: '佣金政策',
    services: '服务',
    login: '登录',
    register: '注册',
    searchProducts: '搜索商品',
    searchProductsError: '搜索请求异常，请稍后重试',
    imageSizeError: '图片大小不能超过{size}MB',
    uploadingImage: '正在上传图片...',
    uploadSuccess: '图片已上传，正在搜索相似商品',
    uploadError: '图片上传异常，请稍后重试',
    urlSearchDetected: '检测到商品链接，正在跳转商品详情页面...',
    urlParseFailed: '商品链接解析失败，请检查链接是否正确',
    search: {
        placeholder: '搜索商品...',
        button: '搜索',
    },
    trackOrders: '订单跟踪',
    shippingCalculation: '运费计算',
    affiliate: '联盟营销',
    addresses: '收货地址',
    browseHistory: '浏览历史',
    saleAmount: '销售金额',
    wallet: '钱包',
    account: '我的账户',
    cart: '购物车',
    language: '语言',
    currency: '货币',
    settings: '设置',
    nav: {
        home: '首页',
        products: '商品',
        about: '关于',
        contact: '联系我们',
    },
    popularCategories: {
        allCategories: '全部分类',
        popularCategories: '热门分类',
        placeholder: '搜索分类...',
        grocery: '食品与日用品',
        giftFinder: '礼品查找',
        toyShop: '玩具店',
        home: '家居',
        fashion: '时尚',
        giftCard: '礼品卡',
        household: '家居用品',
        dropshipping: '代发货',
        electronics: '电子产品',
        sports: '体育用品',
        books: '图书',
        beauty: '美妆',
    },
    orderStatus: {
        awaitingPayment: '待付款',
        pending: '待发货',
        processing: '处理中',
        dispatched: '已发货',
        completed: '已完成',
        closed: '已关闭',
    },
}

export default header
