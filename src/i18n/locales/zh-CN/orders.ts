export default {
    title: '我的订单',
    subtitle: '查看和管理您的订单',
    search: {
        placeholder: '提示：支持输入订单号、商品名称、SKU ID和收货人名称搜索',
    },
    loading: '加载中...',
    orderNo: '订单号',
    orderInfo: '订单信息',
    progressAmount: '进度与金额',
    quantity: '数量',
    types: '种商品',
    buyerMessage: '买家留言',
    totalQuantity: '商品总数',
    productTypes: '商品种类',
    orderStatus: '订单状态',
    timeRange: '时间范围',
    completion: '完成度',
    quantityLabel: '数量：',
    quantityValue: '{quantity} 件 · {types} 种',
    timeLabel: '时间：',
    buyerMessageLabel: '留言：',
    progressTitle: '订单进度 {percentage}%',
    lastUpdated: '更新：',
    previewModalTitle: '商品预览',
    selectAll: '全选当页',
    memoTitle: '编辑备注',
    memoLabel: '标记图标',
    memoText: '备注说明',
    memoPlaceholder: '信息仅自己可见，备注信息作为您对这笔订单的备注记录',
    countdownText: '后,系统将关闭交易',
    productDetails: '商品详情',
    loadingProducts: '正在加载商品信息...',
    createTime: '下单时间',
    clickToPreview: '点击查看大图',
    clickToViewDetail: '点击查看订单详情',
    copySkuHint: '复制SKU',
    productsInfo: '商品信息',
    // 表头国际化配置
    tableHeaders: {
        productImage: '商品图片',
        productName: '商品名称',
        unitPrice: '单价',
        quantity: '数量',
        actions: '操作',
        orderNo: '订单号',
        status: '状态',
        orderTime: '订单时间',
        subTotal: '小计',
        orderAmount: '订单金额',
        viewProduct: '查看商品',
        buyAgain: '再次购买'
    },
    empty: {
        title: '暂无订单',
        description: '您还没有任何订单，去逛逛商城吧！',
        browsing: '浏览商品',
    },
    status: {
        all: '全部',
        pending: '待支付',
        paid: '已支付',
        purchasing: '采购中',
        shipped: '已发货',
        delivered: '已送达',
        inWarehouse: '已入库',
        completed: '已完成',
        cancelled: '已取消',
        unknown: '未知状态',
        label: '状态',
    },
    itemStatus: {
        pendingPayment: '待支付',
        paid: '已支付',
        purchasing: '采购中',
        shipped: '已发货',
        inWarehouse: '已入库',
        completed: '已完成',
        cancelled: '已取消',
        unknown: '未知状态',
    },
    product: {
        label: '商品',
    },
    purchaseInfo: {
        label: '采购信息',
    },
    amount: {
        label: '金额',
    },
    items: {
        title: '订单商品',
    },
    actions: {
        label: '操作',
        viewDetail: '详情',
        viewTracking: '查看物流',
        pay: '立即支付',
        confirmReceipt: '确认收货',
        cancel: '取消订单',
        refund: '申请退款',
        more: '更多',
        preview: '预览',
        batchCancel: '批量取消',
        clickHere: '点击这里',
        cancelHint: '如果您不想购买,可以关闭交易',
        addMemo: '添加备忘',
    },
    error: {
        loadFailed: '加载订单列表失败',
        confirmFailed: '确认收货失败',
        cancelFailed: '取消订单失败',
        payFailed: '支付订单失败',
        reasonRequired: '取消原因是必填的',
        batchCancelFailed: '批量取消订单失败',
        memoRequired: '备注是必填的',
        memoFailed: '添加备注失败',
    },
    confirm: {
        title: '确认操作',
        receipt: '您确定已经收到商品了吗？',
        payMessage: '是否立即支付该订单？',
        cancelReason: '请输入取消订单的原因',
        batchCancelMessageWithReason: '您确定要取消选中的 {count} 个订单吗？请输入原因：',
        reasonPlaceholder: '例如：商品缺货、信息填写错误等',
    },
    cancel: {
        title: '取消订单',
        reason: '请输入取消原因',
        placeholder: '输入取消原因（选填）',
        selectReason: '选择取消原因',
        otherReason: '其他原因',
        description: '此订单已取消',
        reasons: {
            outOfStock: '商品缺货',
            tooHighPrice: '价格太高',
            informationError: '信息填写错误',
            duplicateOrder: '重复下单',
            other: '其他',
        },
    },
    success: {
        confirmed: '确认收货成功',
        cancelled: '订单取消成功',
        paid: '支付成功',
        batchCancelled: '批量取消订单成功',
        memoAdded: '备注添加成功',
    },
    preview: {
        productTitle: '商品标题',
        specs: '规格',
        quantity: '数量',
        unitPrice: '单价',
        loadError: '无法加载商品信息。',
        noSpecs: '无规格信息',
    },
    dateRange: {
        start: '开始日期',
        end: '结束日期',
        clear: '清除',
        separator: '至',
    },
    dateFilter: {
        last7days: '最近7天',
        last15days: '最近15天',
        last30days: '最近30天',
    },
    productDetail: {
        title: '商品详情',
        subtitle: '查看商品的详细信息',
        loading: '加载中...',
        productImage: '商品图片',
        productInfo: '商品信息',
        productName: '商品名称',
        productSpecs: '商品规格',
        productSku: '商品SKU',
        productPrice: '商品价格',
        productQuantity: '商品数量',
        productTotalPrice: '商品总价',
        productDescription: '商品描述',
        productActions: '商品操作',
    },
    // 订单详情页面翻译
    detail: {
        title: '订单详情',
        subtitle: '查看订单的详细信息',
        loading: '加载中...',
        currentStatus: '当前订单状态',
        orderInfo: '订单信息',
        orderNo: '订单号',
        orderStatus: '订单状态',
        createTime: '下单时间',
        updateTime: '更新时间',
        payTime: '支付时间',
        deliveryTime: '发货时间',
        completedTime: '完成时间',
        progress: '订单进度',
        buyerMessage: '买家留言',
        items: '订单商品',
        itemsCount: '件商品',
        quantity: '数量',
        noItems: '暂无商品信息',
        priceDetails: '价格明细',
        merchandiseAmount: '商品金额',
        shippingAmount: '运费',
        serviceFee: '服务费',
        discountAmount: '优惠金额',
        totalAmount: '订单总额',
        actions: '订单操作',
        unitPrice: '单价',
        totalPrice: '总价',
        merchandise: '货品',
        logisticsStatus: '物流状态',
        specs: '规格',
        memoTitle: '编辑备注',
        memoLabel: '备注图标',
        memoText: '备注说明',
        memoPlaceholder: '信息仅自己可见，备注信息作为您对这笔订单的备注记录',
        receiverName: '收货人名称',
        receiverPhone: '收货人电话',
        deliveryAddress: '发货地址',
        warehouseName: '仓库名称',
        countdownText: '后,系统将关闭交易',
        countdown: '倒计时',
        days: '天',
        hours: '小时',
        minutes: '分钟',
        status: {
            paymentPending: '待支付',
            paid: '已支付',
            purchasing: '采购中',
            shipped: '已发货',
            inWarehouse: '已入库',
            completed: '已完成',
        },
        error: {
            title: '加载失败',
            invalidOrderNo: '无效的订单号',
            loadFailed: '加载订单详情失败',
            retry: '重试',
        },
        trackingComingSoon: '物流追踪功能即将上线',
        refundComingSoon: '退款申请功能即将上线',
        addMemoPrompt: '请输入订单备注',
        addMemoTitle: '添加备注',
    },
}