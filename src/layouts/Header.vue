<!-- Header.vue -->
<template>
    <MobileHeader v-if="isMobile" />
    <template v-else>
        <div class="header-wrapper">
            <!-- 顶部信息栏 -->
            <div class="top-bar">
                <div class="container">
                    <div class="left-links">
                        <router-link to="/about" class="top-nav-link">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="nav-icon"
                            >
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M12 16v-4"></path>
                                <path d="M12 8h.01"></path>
                            </svg>
                            <span>{{ t('header.about') }}</span>
                        </router-link>
                        <router-link to="/guide" class="top-nav-link">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="nav-icon"
                            >
                                <path d="M12 20h9"></path>
                                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                            <span>{{ t('header.guide') }}</span>
                        </router-link>
                        <router-link to="/faqs" class="top-nav-link">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="nav-icon"
                            >
                                <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="17" x2="12.01" y2="17"></line>
                            </svg>
                            <span>{{ t('header.faqs') }}</span>
                        </router-link>
                        <router-link to="commission" class="top-nav-link">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="nav-icon"
                            >
                                <path d="M12 2v4"></path>
                                <path d="M16 3l-2 3"></path>
                                <path d="M8 3l2 3"></path>
                                <path d="M20 17a6 6 0 0 0-12 0"></path>
                                <path d="M12 11v3"></path>
                                <path d="M14 14h-4"></path>
                                <circle cx="12" cy="19" r="2"></circle>
                            </svg>
                            <span>{{ t('header.commission') }}</span>
                        </router-link>
                        <router-link to="/services" class="top-nav-link">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                class="nav-icon"
                            >
                                <path
                                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                                ></path>
                            </svg>
                            <span>{{ t('header.services') }}</span>
                        </router-link>
                    </div>
                    <div class="right-links">
                        <!-- 语言货币切换-->
                        <el-dropdown trigger="hover" class="language-currency-dropdown">
                            <div class="language-currency-switch">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="16"
                                    height="16"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="language-icon"
                                >
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                                    <path d="M2 12h20"></path>
                                </svg>
                                <span class="language-text">{{ t('header.language') }}/{{ t('header.currency') }}</span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="12"
                                    height="12"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="arrow-icon"
                                >
                                    <path d="m6 9 6 6 6-6"></path>
                                </svg>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu class="language-currency-menu" style="min-width: 180px !important; width: 180px !important; max-width: 180px !important;">
                                    <!-- 语言部分 -->
                                    <div class="menu-section">
                                        <div class="menu-section-title">{{ t('header.language') }}</div>
                                        <el-dropdown-item :class="{ active: currentLanguage === 'English' }" @click="handleLanguageChange('en-US')">
                                            <div class="lang-item">
                                                <img src="@/assets/flags/US.png" alt="English" class="flag-icon" />
                                                <span>English</span>
                                            </div>
                                        </el-dropdown-item>
                                        <el-dropdown-item :class="{ active: currentLanguage === '中文' }" @click="handleLanguageChange('zh-CN')">
                                            <div class="lang-item">
                                                <img src="@/assets/flags/CN.png" alt="中文" class="flag-icon" />
                                                <span>中文 (简体)</span>
                                            </div>
                                        </el-dropdown-item>
                                    </div>

                                    <!-- 货币部分 -->
                                    <div class="menu-section">
                                        <div class="menu-section-title">{{ t('header.currency') }}</div>
                                        <el-dropdown-item :class="{ active: currentCurrency === 'USD' }" @click="handleCurrencyChange('USD')">
                                            <div class="currency-item">
                                                <span class="currency-symbol">$</span>
                                                <span>USD</span>
                                            </div>
                                        </el-dropdown-item>
                                        <el-dropdown-item :class="{ active: currentCurrency === 'CNY' }" @click="handleCurrencyChange('CNY')">
                                            <div class="currency-item">
                                                <span class="currency-symbol">¥</span>
                                                <span>CNY</span>
                                            </div>
                                        </el-dropdown-item>
                                    </div>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </div>

                    <!-- 用户登录状态 -->
                    <template v-if="!userStore.isLoggedIn">
                        <router-link to="/login" class="login-link">{{ t('header.login') }}</router-link>
                        <router-link to="/register" class="login-link">{{ t('header.register') || '注册' }}</router-link>
                    </template>
                    <template v-else>
                        <el-dropdown trigger="click" class="user-dropdown">
                            <div class="user-info">
                                <div class="user-avatar">{{ userStore.userInfo?.avatar }}</div>
                                <span class="user-name" :title="userStore.userInfo?.username">
                                    {{ userStore.userInfo?.username && userStore.userInfo.username.length > 20 
                                        ? userStore.userInfo.username.substring(0, 20) + '...' 
                                        : userStore.userInfo?.username }}
                                </span>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="12"
                                    height="12"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    class="arrow-icon"
                                >
                                    <path d="m6 9 6 6 6-6"></path>
                                </svg>
                            </div>
                            <template #dropdown>
                                <el-dropdown-menu class="user-dropdown-menu">
                                    <!-- 用户资料头部 -->
                                    <div class="user-profile-header" @click="goToProfile">
                                        <div class="user-avatar-large">
                                            {{ userStore.userInfo?.avatar }}
                                        </div>
                                        <div class="user-info-details">
                                            <div class="user-level">
                                                <span class="level-badge">LV1</span>
                                                <span class="user-name-large" :title="userStore.userInfo?.username">
                                                    {{ userStore.userInfo?.username && userStore.userInfo.username.length > 20 
                                                        ? userStore.userInfo.username.substring(0, 20) + '...' 
                                                        : userStore.userInfo?.username }}
                                                </span>
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="16"
                                                    height="16"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                    class="edit-profile-icon"
                                                    title="修改个人资料"
                                                >
                                                    <path d="M12 20h9"></path>
                                                    <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                                                </svg>
                                            </div>
                                            <div class="user-id">
                                                <span class="id-label">ID</span>
                                                <span class="id-value">{{ userStore.userInfo?.userId || userStore.userInfo?.id || '未获取到ID' }}</span>
                                                <button class="copy-button" @click="copyUserId" title="复制用户ID">
                                                    <svg
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        width="16"
                                                        height="16"
                                                        viewBox="0 0 24 24"
                                                        fill="none"
                                                        stroke="currentColor"
                                                        stroke-width="2"
                                                        stroke-linecap="round"
                                                        stroke-linejoin="round"
                                                    >
                                                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 销售和钱包信息 -->
                                    <div class="user-stats">
                                        <div class="stat-box">
                                            <div class="stat-title">{{ t('header.saleAmount') }}</div>
                                            <div class="stat-value">¥0.00</div>
                                        </div>
                                        <div class="stat-divider"></div>
                                        <div class="stat-box">
                                            <div class="stat-title">{{ t('header.wallet') }}</div>
                                            <div class="stat-value">¥0.00</div>
                                        </div>
                                    </div>

                                    <!-- 订单状态统计 -->
                                    <div class="order-stats">
                                        <div class="order-stat-item">
                                            <div class="order-stat-value">0</div>
                                            <div class="order-stat-label">{{ t('header.orderStatus.awaitingPayment') }}</div>
                                        </div>
                                        <div class="order-stat-item">
                                            <div class="order-stat-value">0</div>
                                            <div class="order-stat-label">{{ t('header.orderStatus.pending') }}</div>
                                        </div>
                                        <div class="order-stat-item">
                                            <div class="order-stat-value">0</div>
                                            <div class="order-stat-label">{{ t('header.orderStatus.processing') }}</div>
                                        </div>
                                        <div class="order-stat-item">
                                            <div class="order-stat-value">0</div>
                                            <div class="order-stat-label">{{ t('header.orderStatus.dispatched') }}</div>
                                        </div>
                                        <div class="order-stat-item">
                                            <div class="order-stat-value">0</div>
                                            <div class="order-stat-label">{{ t('header.orderStatus.completed') }}</div>
                                        </div>
                                        <div class="order-stat-item">
                                            <div class="order-stat-value">0</div>
                                            <div class="order-stat-label">{{ t('header.orderStatus.closed') }}</div>
                                        </div>
                                    </div>

                                    <!-- 功能链接 -->
                                    <div class="feature-links">
                                        <router-link to="/user/track-orders" class="feature-item">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            >
                                                <rect width="18" height="18" x="3" y="4" rx="2" ry="2"></rect>
                                                <line x1="16" x2="16" y1="2" y2="6"></line>
                                                <line x1="8" x2="8" y1="2" y2="6"></line>
                                                <line x1="3" x2="21" y1="10" y2="10"></line>
                                            </svg>
                                            <span>{{ t('header.trackOrders') }}</span>
                                        </router-link>
                                        <router-link to="/user/shipping-calculation" class="feature-item">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            >
                                                <rect width="16" height="16" x="4" y="4" rx="2"></rect>
                                                <line x1="4" x2="20" y1="12" y2="12"></line>
                                                <line x1="12" x2="12" y1="4" y2="20"></line>
                                            </svg>
                                            <span>{{ t('header.shippingCalculation') }}</span>
                                        </router-link>
                                        <router-link to="/user/affiliate" class="feature-item">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="24"
                                                height="24"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            >
                                                <path d="M20.2 7.8l-7.7 7.7-4-4-5.7 5.7"></path>
                                                <path d="M15 7h6v6"></path>
                                            </svg>
                                            <span>{{ t('header.affiliate') }}</span>
                                        </router-link>
                                    </div>

                                    <!-- 历史记录 -->
                                    <router-link to="/user/browse-history" class="history-link">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="24"
                                            height="24"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        >
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <polyline points="12 6 12 12 16 14"></polyline>
                                        </svg>
                                        <span>{{ t('header.browseHistory') }}</span>
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="16"
                                            height="16"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            class="arrow-right-icon"
                                        >
                                            <path d="m9 18 6-6-6-6"></path>
                                        </svg>
                                    </router-link>

                                    <!-- 登出按钮 -->
                                    <button class="logout-button" @click="handleLogout">{{ t('auth.logout') }}</button>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </template>
                </div>
            </div>
        </div>

        <!-- 主导航栏 -->
        <header class="header">
            <div class="container">
                <div class="header-main">
                    <!-- Logo -->
                    <a href="/" class="logo-link">
                        <img src="@/assets/icons/logo.svg" alt="Naya Source Logo" class="logo-icon" />
                    </a>

                    <!-- Search Bar -->
                    <div class="search-wrapper">
                        <div class="search-container">
                            <!-- 隐藏的文件上传输入框 -->
                            <input type="file" ref="fileInput" accept="image/*" class="hidden" @change="handleImageUpload" />
                            <!-- 搜索框和按钮组 -->
                            <div class="search-input-group">
                                <!-- 搜图按钮和预览 -->
                                <div class="image-search-wrapper">
                                    <div class="image-search-content">
                                        <button class="image-search-button" @click="triggerFileInput">
                                            <svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                width="20"
                                                height="20"
                                                viewBox="0 0 24 24"
                                                fill="none"
                                                stroke="currentColor"
                                                stroke-width="2"
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                            >
                                                <rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect>
                                                <circle cx="9" cy="9" r="2"></circle>
                                                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path>
                                            </svg>
                                        </button>
                                        <!-- 图片预览 -->
                                        <div v-if="previewUrl" class="preview-thumbnail">
                                            <img :src="previewUrl" alt="Preview" class="preview-image" />
                                            <!-- 清除按钮 -->
                                            <button class="preview-clear-button" @click.stop="clearImageSearch">
                                                <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="12"
                                                    height="12"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    stroke-width="2"
                                                    stroke-linecap="round"
                                                    stroke-linejoin="round"
                                                >
                                                    <path d="M18 6 6 18"></path>
                                                    <path d="m6 6 12 12"></path>
                                                </svg>
                                            </button>
                                            <!-- 大图预览 -->
                                            <div class="preview-large">
                                                <div class="preview-large-container">
                                                    <img :src="previewUrl" alt="Preview" class="preview-image-large" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 搜索框和清除按钮 -->
                                <div class="search-input-wrapper">
                                    <input
                                        type="text"
                                        v-model="searchQuery"
                                        :placeholder="t('header.searchProducts', '搜索商品')"
                                        class="search-input"
                                        @keyup.enter="handleSearch" 
                                    />
                                    <!-- 清除按钮 -->
                                    <button v-if="searchQuery" class="clear-button" @click="clearSearch">
                                        <svg
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="18"
                                            height="18"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            stroke-width="2"
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                        >
                                            <path d="M18 6 6 18"></path>
                                            <path d="m6 6 12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                                <!-- 搜索按钮 (原版) -->
                                <button class="search-button" @click="handleSearch">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="20"
                                        height="20"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        class="search-icon"
                                    >
                                        <circle cx="11" cy="11" r="8"></circle>
                                        <path d="m21 21-4.3-4.3"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- User Actions -->
                <div class="user-actions">
                    <div class="cart-wrapper" @click="openCartDrawer">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            class="cart-icon"
                        >
                            <circle cx="8" cy="21" r="1"></circle>
                            <circle cx="19" cy="21" r="1"></circle>
                            <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
                        </svg>
                        <!-- 购物车数量显示 -->
                        <span v-if="cartStore.cartCount > 0" class="cart-count"></span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 分类导航栏 -->
        <CategoryMenu />

    <!-- 返回顶部按钮 -->
    <el-backtop :right="40" :bottom="40" />
    </template>
</template>

<script setup lang="ts">
    import MobileHeader from '@/layouts/MobileHeader.vue'
    import { uploadApi } from '@/api'
    import CategoryMenu from '@/components/common/CategoryMenu.vue'
    import type { SupportedLocales } from '@/i18n/types'
    import { useCartStore } from '@/stores/cart'
    import type { CurrencyType } from '@/stores/currency'
    import { useCurrencyStore } from '@/stores/currency'
    import { useLanguageStore } from '@/stores/language'
    import { useSearchStore } from '@/stores/search'
    import { useUserStore } from '@/stores/user'
    import { ElLoading, ElMessage } from 'element-plus'
    import { computed, onMounted, ref, onUnmounted } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter, useRoute } from 'vue-router'
    import { parseOfferIdFromUrl, is1688ProductUrl, isValidOfferId, normalizeUrl } from '@/utils/urlUtils'

    const { locale, t } = useI18n()
    const languageStore = useLanguageStore()
    const currencyStore = useCurrencyStore()
    const userStore = useUserStore()
    const searchStore = useSearchStore()
    const cartStore = useCartStore()
    const router = useRouter()
    const route = useRoute()

    const isMobile = ref(false)

    const searchQuery = ref('')
    const fileInput = ref<HTMLInputElement | null>(null)
    const selectedImage = ref<File | null>(null)
    const previewUrl = ref('')
    const showSearchBar = ref(false)
    const showMobileSidebar = ref(false)

    // 首页判断
    const isHomePage = computed(() => route.path === '/')

    // 移动端判断
    const checkIsMobile = () => {
        isMobile.value = window.innerWidth <= 768
    }

    // 首页固定搜索框显示逻辑 - 优化滚动监听
    const showFixedSearch = ref(false)
    const scrollThreshold = 100

    // 滚动监听（仅用于首页固定搜索框）
    const handleScroll = () => {
        // 只在首页且移动端时进行滚动监听
        if (!isHomePage.value) {
            showFixedSearch.value = false
            return
        }

        const scrollTop = window.pageYOffset || document.documentElement.scrollTop
        showFixedSearch.value = scrollTop > scrollThreshold
    }

    // 切换移动端侧边栏
    const toggleMobileSidebar = () => {
        showMobileSidebar.value = !showMobileSidebar.value
    }

    // 关闭移动端侧边栏
    const closeMobileSidebar = () => {
        showMobileSidebar.value = false
    }

    // 点击外部区域隐藏搜索框
    const handleClickOutside = (event: Event) => {
        const target = event.target as Element
        const searchBar = document.querySelector('.fixed-search-bar')
        const imageSearchButton = document.querySelector('.image-search-button')
        const fileInput = document.querySelector('input[type="file"]')
        
        // 如果点击的是图片搜索按钮或文件输入框，不隐藏搜索框
        if (imageSearchButton?.contains(target) || fileInput?.contains(target)) {
            return
        }
        
        if (showSearchBar.value && searchBar && !searchBar.contains(target)) {
            showSearchBar.value = false
        }
    }

    // 监听路由变化
    const handleRouteChange = () => {
        showSearchBar.value = false
        // 路由变化时重置固定搜索框状态
        if (!isHomePage.value) {
            showFixedSearch.value = false
        }
    }

    // 生命周期
    onMounted(async () => {
        checkIsMobile()
        window.addEventListener('resize', checkIsMobile)
        
        // 如果用户已登录，刷新用户信息以确保显示最新数据
        if (userStore.isLoggedIn) {
            try {
                await userStore.refreshUserInfo()
                console.log('Header - 用户信息刷新成功')
            } catch (error) {
                console.error('Header - 刷新用户信息失败:', error)
            }
            
            // 加载购物车数据
            cartStore.fetchCartList().catch(error => {
                console.error('Header - 获取购物车数据失败:', error)
            })
        }
        
        // 添加滚动监听
        window.addEventListener('scroll', handleScroll, { passive: true })
        // 监听路由变化
        router.afterEach(handleRouteChange)
        // 添加点击外部区域监听
        document.addEventListener('click', handleClickOutside)
    })

    onUnmounted(() => {
        window.removeEventListener('resize', checkIsMobile)
        // 移除滚动监听
        window.removeEventListener('scroll', handleScroll)
        // 移除路由监听
        router.afterEach(() => {})
        // 移除点击外部区域监听
        document.removeEventListener('click', handleClickOutside)
    })

    // 打开购物车抽屉
    const openCartDrawer = () => {
        // 直接跳转到购物车页面，不再使用抽屉
        router.push('/cart')
    }

    // 币种选择相关
    const currentCurrency = computed(() => currencyStore.getCurrentCurrency)

    // 切换货币
    const handleCurrencyChange = (currency: CurrencyType) => {
        currencyStore.setCurrency(currency)
        // 这里价格显示会自动更新，因为我们使用了货币store中的转换函数
    }

    // 当前语言计算属性
    const currentLanguage = computed(() => {
        return locale.value === 'en-US' ? 'English' : '中文'
    })

    // 切换语言
    const handleLanguageChange = (lang: SupportedLocales) => {
        // 更新 i18n locale
        locale.value = lang
        // 更新 store 和 localStorage
        languageStore.setLocale(lang)
        // 更新页面标题
        document.title = t('common.appName')
    }

    // 处理用户登出
    const handleLogout = () => {
        userStore.logout()
        router.push('/')
    }

    // 复制用户ID到剪贴板
    const copyUserId = async () => {
        try {
            const userId = userStore.userInfo?.userId || userStore.userInfo?.id
            if (!userId) {
                ElMessage.warning('用户ID不存在')
                return
            }
            
            // 使用现代浏览器的Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(userId)
            } else {
                // 降级方案：使用传统的document.execCommand
                const textArea = document.createElement('textarea')
                textArea.value = userId
                textArea.style.position = 'fixed'
                textArea.style.left = '-999999px'
                textArea.style.top = '-999999px'
                document.body.appendChild(textArea)
                textArea.focus()
                textArea.select()
                document.execCommand('copy')
                textArea.remove()
            }
            
            ElMessage.success('用户ID已复制到剪贴板')
        } catch (error) {
            console.error('复制用户ID失败:', error)
            ElMessage.error('复制失败，请手动复制')
        }
    }

    // 处理搜索
    const handleSearch = () => {
        try {
            if (!searchQuery.value.trim() && !searchStore.isImageSearchMode()) {
                ElMessage.warning(t('header.searchProducts', '请输入搜索关键词或上传图片'))
                return
            }

            const input = searchQuery.value.trim()
            
            // 智能检测搜索类型
            if (is1688ProductUrl(input)) {
                // URL搜索模式 - 将offerId并且直接跳转商品详情页面
                const offerId = parseOfferIdFromUrl(input)
                if (offerId && isValidOfferId(offerId)) {
                    ElMessage.success(t('header.urlSearchDetected', '检测到商品链接，正在跳转商品详情页面...'))
                    router.push(`/products/${offerId}`)
                    return
                } else {
                    ElMessage.error(t('header.urlParseFailed', '商品链接解析失败，请检查链接是否正确'))
                }
            } 

            // 如果输入的是offerId，则直接跳转商品详情页面
            if (isValidOfferId(input)) {
                ElMessage.success(t('header.urlSearchDetected', '检测到商品链接，正在跳转商品详情页面...'))
                router.push(`/products/${input}`)
                return
            }

            // 普通关键词搜索
            searchStore.setKeyword(input)

            // 构造查询参数
            const query: Record<string, string> = {
                searchType: String(searchStore.searchType),
            }

            if (searchStore.keyword) {
                query.keyword = searchStore.keyword
            }

            if (searchStore.isImageSearchMode()) {
                query.imageId = searchStore.imageId
            }

            // 记录搜索历史
            searchStore.addSearchHistory({
                keyword: searchStore.keyword || undefined,
                imageId: searchStore.imageId || undefined,
                searchType: searchStore.searchType,
            })

            // 跳转到搜索页面
            router.push({
                path: '/products/search',
                query,
            })
        } catch (error) {
            console.error('Header - 处理搜索时发生错误:', error)
            ElMessage.error(t('header.searchProductsError', '搜索请求异常，请稍后重试'))
        }
    }

    // 触发文件选择
    const triggerFileInput = () => {
        fileInput.value?.click()
    }

    // 处理图片上传
    const handleImageUpload = async (event: Event) => {
        const target = event.target as HTMLInputElement
        if (target.files && target.files[0]) {
            const file = target.files[0]
            selectedImage.value = file

            // 文件大小验证 (最大2MB)
            if (file.size > 2 * 1024 * 1024) {
                ElMessage.error(t('header.imageSizeError', { size: 2 }))
                return
            }

            // 创建预览URL
            const reader = new FileReader()
            reader.onload = e => {
                previewUrl.value = e.target?.result as string
            }
            reader.readAsDataURL(file)

            try {
                // 显示上传中状态
                const loadingInstance = ElLoading.service({
                    lock: true,
                    text: t('header.uploadingImage', '正在上传图片...'),
                    background: 'rgba(0, 0, 0, 0.7)',
                })

                // 使用 API 模块上传图片
                const imageId = await uploadApi.uploadImage(file)

                // 关闭加载状态
                loadingInstance.close()

                // 设置图片搜索模式
                searchStore.setImageSearch(imageId)

                // 跳转到搜索页面
                router.push({
                    path: '/products/search',
                    query: {
                        searchType: '2',
                        imageId: imageId,
                        keyword: searchQuery.value || undefined,
                    },
                })

                // 记录搜索历史
                searchStore.addSearchHistory({
                    searchType: 2,
                    imageId: imageId,
                    keyword: searchQuery.value || undefined,
                })

                // 上传成功提示
                ElMessage.success(t('header.uploadSuccess', '图片已上传，正在搜索相似商品'))
            } catch (error) {
                console.error('图片上传异常:', error)
                ElMessage.error(t('header.uploadError', '图片上传异常，请稍后重试'))
                clearImageSearch() // 清理选择的图片
            }
        }
    }

    // 清除搜索内容和图片
    const clearSearch = () => {
        searchQuery.value = ''
    }
    const clearImageSearch = () => {
        // 清除本地图片状态
        selectedImage.value = null
        previewUrl.value = ''
        if (fileInput.value) {
            fileInput.value.value = ''
        }

        // 清除搜索存储中的图片搜索模式
        searchStore.clearImageSearch()
    }

    // 添加用户相关逻辑
    const goToProfile = () => {
        // 跳转到用户个人资料页面
        router.push('/user/profile')
    }
</script>

<style scoped lang="scss">
    /* 样式保持不变 */
    .header-wrapper {
        z-index: 1000;
        background: #fff;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .container {
        width: 95%;
        margin: 0 auto;
        padding: 0 20px;
        box-sizing: border-box;
    }

    .top-bar {
        background-color: #310054;
        height: 51px;
        position: relative;
        z-index: 1000;

        .container {
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .left-links {
            display: flex;
            align-items: center;
            gap: 22px;
        }
        
        .right-links {
            display: flex;
            align-items: center;
            gap: 22px;
            margin-left: auto; /* 确保右侧元素靠右对齐 */
        }

        .top-nav-link {
            display: flex;
            align-items: center;
            gap: 6px;
            color: #fff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 5px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;

            .nav-icon {
                color: rgba(255, 255, 255, 0.8);
                transition: color 0.2s ease;
            }

            span {
                position: relative;

                &:after {
                    content: '';
                    position: absolute;
                    left: 0;
                    bottom: -2px;
                    width: 0;
                    height: 2px;
                    background-color: #fbad00;
                    transition: width 0.2s ease;
                }
            }

            &:hover {
                background-color: rgba(255, 255, 255, 0.1);

                .nav-icon {
                    color: #fbad00;
                }

                span:after {
                    width: 100%;
                }
            }
        }

        // 统一响应式设计
        @media (max-width: 1024px) {
            .left-links {
                gap: 16px;
            }
            .right-links {
                gap: 16px;
            }
        }
        
        @media (max-width: 768px) {
            height: auto;
            padding: 0.5rem 0;
            
            .container {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .left-links {
                display: none; // 移动端隐藏左侧链接
            }
            
            .right-links {
                gap: 1rem;
                width: 100%;
                align-items: center;
                justify-content: space-between;

                .language-currency-switch {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 0.75rem;
                    
                    .language-text {
                        font-size: 14px;
                    }
                }

                // 确保搜索框在所有页面都显示
                .mobile-search-compact {
                    max-width: 250px;
                    flex: 0 1 auto;
                    margin: 0 1rem;
                }
            }
        }
        
        @media (max-width: 480px) {
            .right-links {
                .language-currency-dropdown {
                    // 确保语言货币切换在移动端可见
                }
            }
        }
    }

    .header {
        background-color: #52018d;
        height: 99px;
        position: relative; // For potential absolute positioned elements like mobile menu button

        .container {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 40px; // This gap might be too large for mobile
        }
        @media (max-width: 768px) {
            height: auto; // Adjust height for mobile
            padding: 0.75rem 0;
            .container {
                gap: 1rem; // Reduce gap
                flex-wrap: wrap; // Allow wrapping if needed
            }
        }
    }

    .header-main {
        display: flex;
        align-items: center;
        gap: 2rem;
        flex: 1; // Allow it to take space
        min-width: 0; // Important for flex item shrinking

        @media (max-width: 1024px) {
            gap: 1rem;
        }
        @media (max-width: 768px) {
            // On mobile, logo and search might need different arrangement
            // For now, let's ensure they can shrink and don't overflow
            // We'll handle the search box modal separately
            order: 1; // Example: change order if needed
        }
    }

    .logo-link {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-shrink: 0;
        text-decoration: none;
        transition: all 0.3s ease;
        
        &:hover {
            transform: scale(1.02);
        }
    }

    .logo-icon {
        width: 150px;
        height: 50px;
        object-fit: contain;
        border-radius: 4px;
        
        @media (max-width: 768px) {
            width: 32px;
            height: 32px;
        }
        
        @media (max-width: 480px) {
            width: 28px;
            height: 28px;
        }
    }

    .logo-text {
        font-size: 1.5rem;
        font-weight: 700;
        color: #ffffff;
        letter-spacing: 0.5px;
        
        @media (max-width: 768px) {
            font-size: 1.25rem;
        }
        
        @media (max-width: 480px) {
            font-size: 1.1rem;
        }
    }

    .search-wrapper {
        flex: 1;
        min-width: auto;
        max-width: 90rem;
        position: relative; // For clear button positioning
        @media (max-width: 1024px) {
            min-width: auto; // Allow search to shrink
        }
        @media (max-width: 768px) {
            // The whole search input group will be replaced by an icon triggering a modal
            // For now, we can hide the existing one or prepare for replacement
            display: none; // Hide current search bar on mobile, will add a trigger icon
        }
    }

    .search-container {
        position: relative;
    }

    .hidden {
        display: none;
    }

    .search-input-group {
        display: flex;
        align-items: center;
        background-color: #f9f5ff;
        border-radius: 9999px;
        padding: 0.25rem;
        border: 2px solid transparent;
        transition: all 0.2s ease;

        &:focus-within {
            border-color: #fbad00;
            box-shadow: 0 0 0 2px rgba(251, 173, 0, 0.2);
        }
    }

    .image-search-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        padding: 0.5rem;
        cursor: pointer;
        color: #52018d;
        border-radius: 9999px;
        margin-right: 0.5rem;

        &:hover {
            background-color: #ede9fe;
        }
    }

    .search-input-wrapper {
        position: relative;
        flex: 1;
        display: flex;
        align-items: center;
    }

    .search-input {
        width: 100%;
        border: none;
        background: none;
        padding: 0.5rem 0.75rem;
        padding-right: 4rem; 
        color: #111827;
        font-size: 14px;
        height: 36px;

        &:focus {
            outline: none;
        }
    }

    .clear-button {
        position: absolute;
        right: 2.5rem;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        padding: 0.25rem;
        cursor: pointer;
        color: #6b7280;
        min-width: 20px;
        min-height: 20px;

        &:hover {
            color: #111827;
        }
    }

    .search-button {
        position: absolute;
        right: 0.5rem;
        top: 50%;
        transform: translateY(-50%);
        background-color: #fbad00;
        padding: 0.5rem;
        border-radius: 9999px;
        cursor: pointer;
        border: none;

        &:hover {
            background-color: #f59e0b;
        }
    }

    .search-icon {
        color: #ffffff;
    }

    .image-search-wrapper {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .image-search-content {
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .preview-thumbnail {
        position: relative;
        width: 32px;
        height: 32px;
        border-radius: 0.25rem;
        overflow: visible;
        border: 2px solid #ede9fe;

        &:hover {
            .preview-large {
                opacity: 1;
                visibility: visible;
            }

            .preview-clear-button {
                opacity: 1;
            }
        }
    }

    .preview-clear-button {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 16px;
        height: 16px;
        background-color: #ef4444;
        border: none;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        opacity: 0;
        transition: all 0.2s ease;
        padding: 0;
        z-index: 70;

        &:hover {
            background-color: #dc2626;
        }
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.25rem;
    }

    .preview-large {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 0.5rem;
        padding: 0.5rem;
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s ease;
        z-index: 60;

        &::after {
            content: '';
            position: absolute;
            top: -8px;
            left: 50%;
            transform: translateX(-50%);
            border-width: 0 8px 8px;
            border-style: solid;
            border-color: transparent transparent white;
        }
    }

    .preview-large-container {
        width: 400px;
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .preview-image-large {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    .user-actions {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        @media (max-width: 768px) {
            order: 2; // Example: ensure user actions (like cart) are on the right
            // We might need a mobile menu trigger icon here or near the logo
        }
    }

    .user-menu-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        color: #ffffff;
    }

    .menu-icon,
    .arrow-icon {
        color: #ffffff;
    }

    .cart-wrapper {
        position: relative;
    }

    .cart-icon {
        color: #ffffff;
    }

    .cart-count {
        position: absolute;
        top: -6px;
        right: -6px;
        width: 8px;
        height: 8px;
        background-color: #fbad00;
        border-radius: 50%;
    }

    .category-nav {
        background-color: #fbad00;
        padding: 0.75rem 0;
    }

    .nav-content {
        display: flex;
        align-items: center;
        gap: 2rem;
    }

    .category-dropdown {
        position: relative;
    }

    .category-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 500;
        color: #ffffff;
        background: #52018d;
        border: none;
        cursor: pointer;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
            background-color: #420173;
        }

        .menu-icon {
            color: #ffffff;
        }
    }

    .category-menu-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        margin-top: 0.75rem;
        min-height: 20rem;
        z-index: 50;
        background-color: #ffffff;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        min-width: 75rem;
    }

    .nav-links {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .nav-link {
        color: #52018d;
        text-decoration: none;
        font-weight: 500;
        padding: 0.25rem 0;
        transition: all 0.2s ease;
        position: relative;

        &:after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background-color: #52018d;
            transition: width 0.2s ease;
        }

        &:hover:after,
        &.active:after {
            width: 100%;
        }

        &.active {
            color: #52018d;
            font-weight: 600;
        }
    }

    .language-switch {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .language-icon {
            color: #fff;
        }

        .arrow-icon {
            margin-left: 0.25rem;
            color: #fff;
        }
    }

    .language-text {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
    }

    .search-icon-button {
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        border: none;
        padding: 0.5rem;
        cursor: pointer;
        color: #fff;
        border-radius: 0.25rem;
        transition: all 0.2s ease;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .search-icon-svg {
            color: #fff;
        }

        /* 确保在网页端隐藏 */
        @media (min-width: 769px) {
            display: none;
        }
    }

    .lang-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .right-links {
        gap: 20px;

        a,
        .language-switch {
            color: #fff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;

            &:hover {
                opacity: 0.8;
            }
        }
    }

    /* 添加用户相关样式 */
    .login-link {
        margin-left: 1rem;
        color: #fff;
        font-weight: 500;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s ease;
        width: 125px; /* 固定宽度 */
        height: 40px; /* 固定高度 */
        min-width: 100px; /* 最小宽度 */
        max-width: 125px; /* 最大宽度 */

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
    }

    .user-avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background-color: #fbad00;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .user-name {
        font-size: 0.875rem;
        color: #ffffff;
        font-weight: bold;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        min-width: 0; /* 允许收缩 */
    }

    .dropdown-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #374151;
        text-decoration: none;
        width: 100%;

        &:hover {
            color: #52018d;
        }
    }

    .dropdown-icon {
        color: #6b7280;
    }

    .currency-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    :deep(.language-dropdown) {
        min-width: 120px;
        padding: 4px;
        border-radius: 6px;

        .el-dropdown-menu__item {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
            margin: 2px 0;

            &:hover {
                background-color: #f4f4f5;
            }

            &.active {
                color: #52018d;
                font-weight: 500;
                background-color: #f9f5ff;
            }
        }
    }
    
    .language-currency-switch {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .language-icon {
            color: #fff;
        }

        .arrow-icon {
            margin-left: 0.25rem;
            color: #fff;
        }
    }

    .menu-section {
        padding: 8px 0;

        &:not(:last-child) {
            border-bottom: 1px solid #eee;
        }
    }

    .menu-section-title {
        padding: 0 16px 8px;
        color: #52018d;
        font-weight: 500;
        font-size: 14px;
    }

    .flag-icon {
        width: 18px;
        height: 12px;
        margin-right: 8px;
        border-radius: 2px;
        object-fit: cover;
    }

    .currency-symbol {
        font-weight: bold;
        min-width: 12px;
    }

    :deep(.el-dropdown-menu) {
        min-width: 120px;
        padding: 4px;
        border-radius: 6px;

        .el-dropdown-menu__item {
            padding: 8px 16px;
            font-size: 14px;
            border-radius: 4px;
            margin: 2px 0;

            &:hover {
                background-color: #f4f4f5;
            }

            &.active {
                color: #52018d;
                font-weight: 500;
                background-color: #f9f5ff;
            }
        }
    }

    /* 添加用户下拉菜单样式 */
    :deep(.user-dropdown-menu) {
        width: 300px;
        padding: 0;
        border-radius: 8px;
        overflow: hidden;
        /* 修复下拉菜单定位问题 - 确保菜单相对于触发器居中 */
        transform: translateX(-50%) !important;
        left: 50% !important;
        right: auto !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* 确保用户下拉菜单容器的定位 */
    .user-dropdown {
        position: relative;
        margin-left: 1rem;
        
        :deep(.el-dropdown) {
            position: relative;
        }
        
        /* 针对Element Plus的popper定位修复 */
        :deep(.el-popper) {
            /* 确保下拉菜单相对于触发器居中显示 */
            transform: translateX(-50%) !important;
            left: 50% !important;
            right: auto !important;
            margin-left: 0 !important;
            margin-right: 0 !important;
        }
        
        /* 针对不同的popper实例 */
        :deep(.el-popper.is-pure) {
            transform: translateX(-50%) !important;
            left: 50% !important;
            right: auto !important;
        }
        
        /* 确保下拉菜单箭头也居中 */
        :deep(.el-popper__arrow) {
            left: 50% !important;
            transform: translateX(-50%) !important;
        }
    }

    .user-profile-header {
        display: flex;
        align-items: center;
        padding: 16px;
        background-color: #fff;
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: #f9f9f9;

            .edit-profile-icon {
                opacity: 1;
                color: #fbad00;
            }
        }
    }

    .user-avatar-large {
        width: 64px;
        height: 64px;
        border-radius: 50%;
        background-color: #f5f5f5;
        color: #fbad00;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        font-weight: bold;
        margin-right: 16px;
    }

    .user-info-details {
        flex: 1;
    }

    .user-level {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
    }

    .level-badge {
        background-color: #fbad00;
        color: white;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
        margin-right: 8px;
    }

    .user-name-large {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 180px; /* 限制最大宽度 */
    }

    .user-id {
        display: flex;
        align-items: center;
    }

    .id-label {
        color: #fbad00;
        font-weight: bold;
        margin-right: 4px;
    }

    .id-value {
        color: #666;
        margin-right: 4px;
    }

    .copy-button {
        background: none;
        border: none;
        padding: 2px;
        cursor: pointer;
        color: #aaa;
    }

    .user-stats {
        display: flex;
        background-color: #fff9f0;
        padding: 16px;
    }

    .stat-box {
        flex: 1;
        text-align: center;
    }

    .stat-divider {
        width: 1px;
        background-color: #eee;
        margin: 0 16px;
    }

    .stat-title {
        font-size: 14px;
        color: #555;
        margin-bottom: 4px;
    }

    .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: #fbad00;
    }

    .order-stats {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 1px;
        background-color: #f5f5f5;
        border-top: 1px solid #eee;
        border-bottom: 1px solid #eee;
    }

    .order-stat-item {
        background-color: white;
        padding: 12px 0;
        text-align: center;
    }

    .order-stat-value {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 4px;
    }

    .order-stat-label {
        font-size: 12px;
        color: #666;
    }

    .feature-links {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 1px;
        background-color: #f5f5f5;
        border-bottom: 1px solid #eee;
    }

    .feature-item {
        background-color: white;
        padding: 16px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
    }

    .feature-item svg {
        margin-bottom: 8px;
        color: #52018d;
    }

    .feature-item span {
        font-size: 12px;
        color: #333;
    }

    .history-link {
        display: flex;
        align-items: center;
        padding: 16px;
        background-color: white;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }

    .history-link svg {
        margin-right: 12px;
        color: #52018d;
    }

    .history-link span {
        flex: 1;
        font-size: 14px;
        color: #333;
    }

    .arrow-right-icon {
        color: #aaa;
    }

    .logout-button {
        width: 100%;
        padding: 12px;
        background-color: white;
        border: none;
        text-align: center;
        color: #333;
        font-size: 16px;
        cursor: pointer;

        &:hover {
            background-color: #f5f5f5;
        }
    }

    // Specific styling for user dropdown (avatar part) on mobile - this will be hidden in top-bar
    // but good to have if it appears elsewhere in a mobile context (e.g. main nav burger menu)
    :deep(.el-dropdown.user-dropdown) {
        @media (max-width: 768px) {
            .user-info {
                // Potentially hide text, only show avatar if space is very limited
                .user-name {
                    // display: none;
                }
                .arrow-icon {
                    // display: none;
                }
            }
        }
    }

    // Hide category menu on smaller screens, will be handled by a burger menu
    // This is a placeholder, actual CategoryMenu handling will be more nuanced
    // with its own responsive logic or a drawer trigger.
    // For now, let's assume CategoryMenu component itself handles its mobile view,
    // or we add a trigger in Header.vue later.

    // .category-nav {
    //     @media (max-width: 768px) {
    //         display: none;
    //     }
    // }

    .edit-profile-icon {
        opacity: 0.6;
        color: #666;
        margin-left: 8px;
        transition: all 0.2s ease;
    }
</style>
