import { authApi } from '@/api/modules/auth'
import { userApi } from '@/api/modules/user'
import type { LoginUserInfoRes } from '@/api/types'
import { defineStore } from 'pinia'
import { ref } from 'vue'

/*
==================== 用户数据存储设计说明 ====================

【1. 设计目标】
- 统一管理用户信息和身份验证状态
- 用户相关数据（如偏好等）统一存储在 user-bucket，便于管理和清理

【2. 主要方法说明】
- getUserInfo：获取当前登录用户信息
- setUserInfo：设置用户信息并更新登录状态
- logout：安全退出登录，清理相关存储
- saveUserBucket / loadUserBucket：以 userId 为 key 存储/读取用户相关数据

【3. 用法建议】
- 登录成功后调用 setUserInfo 更新用户状态
- 用户个性化设置通过 saveUserBucket 存储

【4. 与购物车模块关系】
- 购物车相关操作统一由 cart.ts 管理
- 用户模块仅维护用户基本信息和登录状态
- 登录状态变化由 cart.ts 中的钩子函数监听，自动处理购物车同步

=======================================================================
*/

// 用户状态存储
export const useUserStore = defineStore('user', () => {
    // 从本地存储恢复用户信息
    const loadUserInfoFromLocalStorage = (): LoginUserInfoRes | null => {
        try {
            const storedUserData = localStorage.getItem('loginUser')
            if (storedUserData) {
                return JSON.parse(storedUserData)
            }
        } catch (error) {
            console.error('加载用户信息失败:', error)
        }
        return null
    }

    // 用户信息 - 优先从本地存储加载
    const userInfo = ref<LoginUserInfoRes | null>(loadUserInfoFromLocalStorage())

    // 获取token，优先从localStorage读取
    const getToken = () => {
        return localStorage.getItem('token') || sessionStorage.getItem('token')
    }

    // 登录状态 - 根据token和用户信息判断
    const isLoggedIn = ref(!!getToken() && !!userInfo.value)

    // 获取用户信息
    const getUserInfo = async () => {
        // 这里应该调用获取用户信息的API
        return userInfo.value
    }
    // 刷新当前用户信息
    const refreshUserInfo = async (forceRefresh = false) => {
        try {
            // 如果用户信息不完整（例如，只有登录信息而没有详细资料），也需要刷新
            const isInfoIncomplete = userInfo.value && typeof (userInfo.value as any).gender === 'undefined'

            // 如果不是强制刷新，并且已有完整用户信息，直接返回缓存数据
            if (!forceRefresh && userInfo.value && !isInfoIncomplete) {
                return userInfo.value
            }

            // 调用API获取最新的用户信息
            const response: any = await userApi.getUserInfo()

            // 处理响应数据结构
            let userData: any = null
            if (response?.data?.data) {
                userData = response.data.data
            } else if (response?.data) {
                userData = response.data
            } else {
                userData = response
            }

            // 验证用户数据有效性并更新
            if (userData && (userData.userId || userData.id)) {
                // 确保数据结构一致性：如果只有id字段，复制到userId字段
                if (!userData.userId && userData.id) {
                    userData.userId = userData.id
                }
                console.log('刷新用户信息成功:', userData)
                setUserInfo(userData)
                return userData
            } else {
                console.warn('API返回的用户数据格式异常:', userData)
                return userInfo.value
            }

        } catch (error) {
            console.error('刷新用户信息失败:', error)
            // API调用失败时返回本地缓存的用户信息
            return userInfo.value
        }
    }

    // 退出登录
    const logout = async () => {
        try {
            await authApi.logout()
        } catch (e) {
            console.error('退出登录失败', e)
        }
        // 清理用户存储桶
        if (userInfo.value?.userId) {
            localStorage.removeItem(`user-${userInfo.value.userId}`)
        }
        // 清除本地存储
        localStorage.removeItem('token')
        sessionStorage.removeItem('token')
        localStorage.removeItem('loginUser')
        userInfo.value = null
        isLoggedIn.value = false
    }

    // 设置用户信息方法
    const setUserInfo = (info: LoginUserInfoRes) => {
        userInfo.value = info
        isLoggedIn.value = true
        // 保存登录用户信息
        localStorage.setItem('loginUser', JSON.stringify(info))
    }

    // 保存用户存储桶
    const saveUserBucket = (data: any) => {
        if (!userInfo.value?.userId) return
        localStorage.setItem(`user-${userInfo.value.userId}`, JSON.stringify(data))
    }

    // 读取用户存储桶
    const loadUserBucket = (userId: number) => {
        const raw = localStorage.getItem(`user-${userId}`)
        return raw ? JSON.parse(raw) : null
    }

    return {
        getToken,
        userInfo,
        isLoggedIn,
        getUserInfo,
        refreshUserInfo,
        setUserInfo,
        saveUserBucket,
        loadUserBucket,
        logout,
        loadUserInfoFromLocalStorage,
    }
})
