/**
 * URL参数处理工具
 * 用于清理和管理URL中的各种参数
 */

import { useRoute, useRouter } from 'vue-router'

/**
 * WMS登录相关参数
 */
export const WMS_PARAMS = ['Code', 'type', 'cuscode'] as const

/**
 * 清理WMS登录相关参数
 * @param router Vue Router实例
 * @param route 当前路由对象
 * @param targetPath 目标路径，默认为当前路径
 */
export const cleanWmsParams = (
    router: ReturnType<typeof useRouter>,
    route: ReturnType<typeof useRoute>,
    targetPath?: string
) => {
    const { Code, type, cuscode, ...otherQuery } = route.query

    // 如果存在WMS相关参数，清理它们
    if (Code || type === 'wms' || cuscode) {
        console.log('清理WMS登录参数:', { Code, type, cuscode })

        const finalPath = targetPath || route.path

        // 使用replace方法清理URL参数，不产生新的历史记录
        router.replace({
            path: finalPath,
            query: { ...otherQuery }
        })

        return true
    }

    return false
}

/**
 * 检查URL中是否包含WMS参数
 * @param query 路由查询参数
 */
export const hasWmsParams = (query: Record<string, any>) => {
    return WMS_PARAMS.some(param => query[param])
}

/**
 * 获取清理后的查询参数（排除WMS参数）
 * @param query 原始查询参数
 */
export const getCleanQuery = (query: Record<string, any>) => {
    const { Code, type, cuscode, ...cleanQuery } = query
    return cleanQuery
} 
/*
 * URL解析工具函数
 */

/**
 * 从1688商品URL中解析offerId
 * 
 * @param url 1688商品URL
 * @returns 解析出的offerId，如果解析失败返回null
 * 
 * @example
 * // 从URL参数中解析
 * parseOfferIdFromUrl('https://detail.1688.com/offer/678243028318.html?offerId=678243028318&spm=a260k.home2025.recommendpart.4')
 * // 返回: '678243028318'
 * 
 * // 从URL路径中解析
 * parseOfferIdFromUrl('https://detail.1688.com/offer/678243028318.html')
 * // 返回: '678243028318'
 */
export const parseOfferIdFromUrl = (url: string): string | null => {
    if (!url || typeof url !== 'string') {
        return null
    }

    try {
        // 方法1: 从URL参数中解析offerId
        const urlObj = new URL(url)
        const offerIdParam = urlObj.searchParams.get('offerId')
        if (offerIdParam) {
            return offerIdParam
        }

        // 方法2: 从URL路径中解析offerId
        // 匹配 /offer/{数字}.html 的模式
        const pathMatch = url.match(/\/offer\/(\d+)\.html/)
        if (pathMatch && pathMatch[1]) {
            return pathMatch[1]
        }

        // 方法3: 更宽松的匹配，查找.html前的数字
        const htmlMatch = url.match(/(\d+)\.html/)
        if (htmlMatch && htmlMatch[1]) {
            return htmlMatch[1]
        }

        return null
    } catch (error) {
        console.error('解析URL中的offerId失败:', error)
        return null
    }
}

/**
 * 验证offerId格式
 * 
 * @param offerId offerId字符串
 * @returns 是否为有效的offerId
 */
export const isValidOfferId = (offerId: string): boolean => {
    if (!offerId || typeof offerId !== 'string') {
        return false
    }
    
    // offerId应该是纯数字且长度合理
    return /^\d{6,20}$/.test(offerId)
}

/**
 * 从搜索输入中检测是否为URL
 * 
 * @param input 搜索输入内容
 * @returns 是否为1688商品URL
 */
export const is1688ProductUrl = (input: string): boolean => {
    if (!input || typeof input !== 'string') {
        return false
    }

    // 检查是否包含1688.com域名
    return input.includes('1688.com') && input.includes('detail.1688.com')
}

/**
 * 清理和标准化URL
 * 
 * @param url 原始URL
 * @returns 清理后的URL
 */
export const normalizeUrl = (url: string): string => {
    if (!url || typeof url !== 'string') {
        return ''
    }

    // 移除前后空格
    let normalized = url.trim()
    
    // 如果没有协议，添加https://
    if (!normalized.startsWith('http://') && !normalized.startsWith('https://')) {
        normalized = 'https://' + normalized
    }

    return normalized
}
