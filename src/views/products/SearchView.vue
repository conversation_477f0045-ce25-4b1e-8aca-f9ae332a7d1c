<template>
    <div class="bg-white min-h-screen">
        <div class="mx-auto w-[90%] xl:w-[90%] py-6">
            <!-- 页面标题 -->
            <div class="mb-4 md:mb-6">
                <h1 class="text-xl md:text-2xl font-bold text-gray-900">
                    {{ searchKeyword ? t('search.resultsFor', { keyword: searchKeyword }) : t('search.allProducts') }}
                    <p class="text-sm text-gray-500">{{ total }} {{ t('search.foundResults') }}</p>
                </h1>
            </div>

            <!-- 搜索模式指示器 -->
            <SearchStatusIndicator v-if="searchStore.isImageSearchMode()" />

            <!-- Mobile Filter Button -->
            <div class="lg:hidden mb-4">
                <button
                    @click="toggleFiltersModal"
                    class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 transition-colors duration-200"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path
                            fill-rule="evenodd"
                            d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L13 10.414V15a1 1 0 01-.293.707l-2 2A1 1 0 019 17v-6.586L4.293 6.707A1 1 0 014 6V3z"
                            clip-rule="evenodd"
                        />
                    </svg>
                    {{ t('search.filtersTitle', 'Filters') }}
                    <span
                        v-if="minPrice || maxPrice || selectedQualityGuarantees.length || selectedRatings.length || selectedServices.length || selectedPickupRates.length"
                        class="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800"
                    >
                        {{
                            (minPrice || maxPrice ? 1 : 0) +
                            selectedQualityGuarantees.length +
                            (selectedRatings.length ? 1 : 0) +
                            selectedServices.length +
                            selectedPickupRates.length
                        }}
                    </span>
                </button>
            </div>

            <div class="flex flex-col lg:flex-row gap-4 lg:gap-6">
                <!-- 左侧筛选器 - 添加固定定位 -->
                <div class="filters-container lg:w-72" :class="{ 'hidden lg:block': !showFiltersModal }">
                    <div class="lg:sticky lg:top-4">
                        <SearchFilters
                            v-model:minPrice="minPrice"
                            v-model:maxPrice="maxPrice"
                            v-model:selectedQualityGuarantees="selectedQualityGuarantees"
                            v-model:selectedRatings="selectedRatings"
                            v-model:selectedServices="selectedServices"
                            v-model:selectedPickupRates="selectedPickupRates"
                            @reset="resetAllFilters"
                            @apply="handleApplyFilters"
                            @close="closeFiltersModal"
                            :is-mobile-modal="showFiltersModal && isMobile"
                        />
                    </div>
                </div>

                <!-- 右侧商品列表 -->
                <div class="product-content flex-1">
                    <!-- 工具栏 -->
                    <SearchSort v-model:sortBy="sortBy" v-model:gridColumns="gridColumns" />

                    <!-- 搜索结果 -->
                    <SearchResults
                        :products="paginatedProducts"
                        :keyword="searchKeyword"
                        :loading="loading"
                        :error="error"
                        :total="total"
                        :gridColumns="gridColumns"
                        @retry="loadProducts"
                        style="padding-top: 2rem"
                    />

                    <!-- 分页 -->
                    <div class="pagination-container mt-8">
                        <Pagination :current-page="currentPage" :page-size="pageSize" :total-items="total" :max-display-pages="5" @page-change="handlePageChange" />
                    </div>

                    <!-- 调试面板 -->
                    <div v-if="debugEnabled" class="mt-8 bg-gray-100 p-4 rounded-lg">
                        <details>
                            <summary class="font-bold cursor-pointer">调试信息</summary>
                            <div class="mt-4">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <h3 class="font-bold mb-2">搜索参数</h3>
                                        <div class="space-y-1">
                                            <p>
                                                <strong>搜索类型:</strong>
                                                {{ searchStore.searchType === 1 ? t('search.debug.keywordSearch') : searchStore.searchType === 2 ? t('search.debug.imageSearch') : t('search.debug.urlSearch') }}
                                            </p>
                                            <p>
                                                <strong>关键词:</strong>
                                                {{ searchKeyword || '无' }}
                                            </p>
                                            <p v-if="searchStore.imageId">
                                                <strong>图片ID:</strong>
                                                {{ searchStore.imageId }}
                                            </p>
                                            <p v-if="searchStore.imageUrl">
                                                <strong>图片URL:</strong>
                                                {{ searchStore.imageUrl }}
                                            </p>
                                            <p v-if="searchStore.offerId">
                                                <strong>Offer ID:</strong>
                                                {{ searchStore.offerId }}
                                            </p>
                                            <p>
                                                <strong>分类ID:</strong>
                                                {{ categoryId || '无' }}
                                            </p>
                                            <p>
                                                <strong>排序:</strong>
                                                {{ sortBy }}
                                            </p>
                                            <p>
                                                <strong>价格区间:</strong>
                                                {{ minPrice || '0' }} - {{ maxPrice || '不限' }}
                                            </p>
                                        </div>
                                    </div>
                                    <div>
                                        <p>
                                            <strong>产品总数:</strong>
                                            {{ totalProducts }}
                                        </p>
                                        <p>
                                            <strong>当前页:</strong>
                                            {{ currentPage }}
                                        </p>
                                        <p>
                                            <strong>加载状态:</strong>
                                            {{ loading ? '加载中' : '已加载' }}
                                        </p>
                                    </div>
                                    <div class="col-span-2">
                                        <p><strong>请求参数:</strong></p>
                                        <pre class="bg-gray-100 p-2 rounded">{{ getRequestParams() }}</pre>
                                    </div>
                                </div>
                            </div>
                        </details>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import type { ProductInfoVO } from '@/api/modules/product'
    import { productApi, ProductSearchParams } from '@/api/modules/product'
    import Pagination from '@/components/common/Pagination.vue'
    import SearchFilters from '@/components/search/SearchFilters.vue'
    import SearchResults from '@/components/search/SearchResults.vue'
    import SearchSort from '@/components/search/SearchSort.vue'
    import SearchStatusIndicator from '@/components/search/SearchStatusIndicator.vue'
    import { useSearchStore } from '@/stores/search'
    import debug from '@/utils/debug'
    import { computed, onMounted, onUnmounted, ref, watch } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRoute } from 'vue-router'
    import { parseOfferIdFromUrl, is1688ProductUrl } from '@/utils/urlUtils'

    const { t, locale } = useI18n()
    const route = useRoute()
    const searchStore = useSearchStore()

    // Mobile filter modal state
    const showFiltersModal = ref(false)
    const isMobile = ref(window.innerWidth < 768) // 检测是否为移动设备

    // 网格列数 - 默认为2列，移动设备友好
    const gridColumns = ref(2)

    const toggleFiltersModal = () => {
        showFiltersModal.value = !showFiltersModal.value
    }

    const closeFiltersModal = () => {
        showFiltersModal.value = false
    }

    const handleResize = () => {
        const mobileCheck = window.innerWidth < 1024 // lg breakpoint for filters, md for grid might be better
        if (isMobile.value !== mobileCheck) {
            isMobile.value = mobileCheck
            // Adjust grid columns on resize if it crosses the mobile threshold for grid
            if (window.innerWidth < 768) {
                // md breakpoint for grid
                if (gridColumns.value !== 2) gridColumns.value = 2
            } else {
                // Optionally reset to a default desktop column count or keep user selection
                // For now, let's stick to 5 or user's desktop choice if already changed
                if (gridColumns.value === 2) gridColumns.value = 5
            }
        }

        if (!isMobile.value) {
            showFiltersModal.value = false // Close modal if screen is resized to desktop
        }
    }

    // 是否启用调试模式
    const debugEnabled = ref(debug.isEnabled())

    // 定义 props
    const props = defineProps<{
        keyword?: string
        categoryId?: string
    }>()

    // 页面状态
    const loading = ref(false)
    const error = ref<string | null>(null)
    const products = ref<ProductInfoVO[]>([])
    const totalProducts = ref(0)
    const originalProductsData = ref<any[]>([]) // 保存原始API响应数据

    // 分页相关
    const currentPage = ref(1)
    const pageSize = ref(20)
    const total = ref(0)

    // 排序相关
    const sortBy = ref('popular') // 使用默认值'popular'

    // 提取排序字段和排序方向
    const getSortParams = computed(() => {
        // 将UI中的排序选项映射到API需要的参数
        if (sortBy.value === 'popular') {
            // 默认排序，不设置特定的排序字段
            return { sortField: undefined, sortOrder: undefined }
        } else if (sortBy.value === 'price-asc') {
            return { sortField: 'price', sortOrder: 'asc' }
        } else if (sortBy.value === 'price-desc') {
            return { sortField: 'price', sortOrder: 'desc' }
        } else if (sortBy.value === 'rating-desc') {
            // 评分不在API支持的排序字段中，可以替换为复购率或月销量
            return { sortField: 'rePurchaseRate', sortOrder: 'desc' }
        } else if (sortBy.value === 'sales-desc') {
            return { sortField: 'monthSold', sortOrder: 'desc' }
        }
        return { sortField: undefined, sortOrder: undefined }
    })

    // 筛选状态
    const minPrice = ref<string>('')
    const maxPrice = ref<string>('')
    const selectedQualityGuarantees = ref<string[]>([])
    const selectedRatings = ref<string[]>([])
    const selectedServices = ref<string[]>([])
    const selectedPickupRates = ref<string[]>([])

    // 在组件初始化时就立即获取初始关键词和分类ID
    const routeKeyword = route.query.keyword
    const routeCategoryId = route.query.categoryId

    const initialKeyword =
        props.keyword || (typeof routeKeyword === 'string' ? routeKeyword : Array.isArray(routeKeyword) && routeKeyword.length > 0 ? String(routeKeyword[0]) : '')

    const initialCategoryId =
        props.categoryId || (typeof routeCategoryId === 'string' ? routeCategoryId : Array.isArray(routeCategoryId) && routeCategoryId.length > 0 ? String(routeCategoryId[0]) : '')

    // 初始化搜索关键词和分类ID
    const searchKeyword = ref<string>(initialKeyword)
    const categoryId = ref<string>(initialCategoryId)

    // 从URL参数初始化搜索模式
    const initSearchMode = () => {
        // 获取URL参数
        const searchTypeParam = route.query.searchType
        const imageIdParam = route.query.imageId
        const imageUrlParam = route.query.imageUrl
        const keywordParam = route.query.keyword

        // 设置搜索类型
        if (searchTypeParam) {
            searchStore.searchType = Number(searchTypeParam)
        } else {
            searchStore.searchType = 1 // 默认为关键词搜索
        }

        // 设置图片ID
        if (imageIdParam && typeof imageIdParam === 'string') {
            searchStore.imageId = imageIdParam
        } else if (searchStore.searchType === 2) {
            // 如果是图片搜索模式但没有imageId，则检查是否有imageUrl
            if (imageUrlParam && typeof imageUrlParam === 'string') {
                searchStore.imageUrl = imageUrlParam
                searchStore.imageId = '' // 清空imageId，优先使用imageUrl
            } else {
                // 如果既没有imageId也没有imageUrl，则降级为普通搜索
                searchStore.searchType = 1
                searchStore.imageId = ''
                searchStore.imageUrl = ''
            }
        }

        // 设置图片URL（如果有的话）
        if (imageUrlParam && typeof imageUrlParam === 'string') {
            searchStore.imageUrl = imageUrlParam
        }

        // 设置关键词
        if (keywordParam && typeof keywordParam === 'string') {
            searchStore.keyword = keywordParam
            searchKeyword.value = keywordParam
        } else {
            searchStore.keyword = ''
            searchKeyword.value = ''
        }

        debug.log('SearchView', '初始化搜索模式:', {
            searchType: searchStore.searchType,
            imageId: searchStore.imageId,
            imageUrl: searchStore.imageUrl,
            keyword: searchStore.keyword,
            offerId: searchStore.offerId,
        })
    }

    // 分页后的产品数据
    const paginatedProducts = computed(() => {
        return products.value
    })

    // 获取产品数据
    const loadProducts = async () => {
        loading.value = true
        error.value = null
        products.value = []

        try {
            // 验证筛选条件
            validateFilters()

            // 获取排序参数
            const { sortField, sortOrder } = getSortParams.value

            // 将所有筛选条件收集到一个数组中
            let filterConditions: string[] = []

            // 映射质量保证选项到API枚举值并添加到筛选条件
            selectedQualityGuarantees.value.forEach(q => {
                if (q === '认证工厂') {
                    filterConditions.push('certifiedFactory')
                } else if (q === '跨境精选商品') {
                    filterConditions.push('crossBorderSelection')
                } else if (q === '全球优选') {
                    filterConditions.push('globalSelection')
                } else if (q === '精品货源') {
                    filterConditions.push('qualitySources')
                }
            })

            // 映射服务选项到API枚举值并添加到筛选条件
            selectedServices.value.forEach(s => {
                if (s === '当日发货') {
                    filterConditions.push('shipInToday')
                } else if (s === '24小时发货') {
                    filterConditions.push('shipIn24Hours')
                } else if (s === '48小时发货') {
                    filterConditions.push('shipIn48Hours')
                } else if (s === '7天无理由退换') {
                    filterConditions.push('noReason7DReturn')
                } else if (s === '支持代发货') {
                    filterConditions.push('isOnePsale')
                } else if (s === '支持包邮') {
                    filterConditions.push('isOnePsaleFreePost')
                }
            })

            // 处理评分筛选 - 添加到筛选条件
            if (selectedRatings.value.length > 0) {
                const ratingValue = selectedRatings.value[0]
                if (ratingValue === '5星') {
                    filterConditions.push('totalEpScoreLv1')
                } else if (ratingValue === '4.5星-5.0星') {
                    filterConditions.push('totalEpScoreLv2')
                } else if (ratingValue === '4星-4.5星') {
                    filterConditions.push('totalEpScoreLv3')
                } else if (ratingValue === '4星以下') {
                    filterConditions.push('totalEpScoreLv4')
                }
            }

            // 添加24小时揽收率筛选
            selectedPickupRates.value.forEach(rate => {
                if (rate === '24小时揽收率<95%') {
                    filterConditions.push('getRate24HLv1')
                } else if (rate === '24小时揽收率>=95%') {
                    filterConditions.push('getRate24HLv2')
                } else if (rate === '24小时揽收率>=99%') {
                    filterConditions.push('getRate24HLv3')
                } else if (rate === '48小时揽收率<95%') {
                    filterConditions.push('getRate48HLv1')
                } else if (rate === '48小时揽收率>=95%') {
                    filterConditions.push('getRate48HLv2')
                } else if (rate === '48小时揽收率>=99%') {
                    filterConditions.push('getRate48HLv3')
                }
            })

            debug.log('SearchView', `开始加载产品数据 [页码:${currentPage.value}]`)

            // 构建搜索参数
            const searchParams: ProductSearchParams = {
                // 搜索类型和图片ID/URL
                searchType: searchStore.searchType,
                imageId: searchStore.imageId || undefined,
                imageUrl: searchStore.imageUrl || undefined,
                // 关键词（包含offerId）
                keyword: searchKeyword.value,
                // 分页
                page: currentPage.value,
                pageSize: pageSize.value,
                // 分类
                categoryIds: categoryId.value ? [categoryId.value] : undefined,
                // 排序 - 修正字段名与 API 接口定义保持一致
                sortField: sortField,
                sortOrder: sortOrder as 'asc' | 'desc',
                // 价格区间
                minPrice: minPrice.value ? Number(minPrice.value) : undefined,
                maxPrice: maxPrice.value ? Number(maxPrice.value) : undefined,
                // 筛选条件
                filter: filterConditions.length > 0 ? filterConditions.join(',') : undefined,
            }

            debug.log('SearchView', '请求参数:', searchParams)

            // 发送API请求
            const response = await productApi.searchProducts(searchParams)

            debug.log('SearchView', '搜索API响应:', response)

            // 处理API响应 - 修复数据结构匹配
            if (response && response.records) {
                // 后端返回的是 PageDTO<ProductInfoVO> 结构：records, total, pageIndex, pageSize
                originalProductsData.value = response.records || []
                products.value = response.records.map((item: any) => item as ProductInfoVO)
                totalProducts.value = response.total || 0
                total.value = response.total || 0

                debug.log('SearchView', `加载成功: ${products.value.length}/${totalProducts.value}`)
            } else {
                // 处理数据为null或undefined的情况
                originalProductsData.value = []
                products.value = []
                totalProducts.value = 0
                total.value = 0
                // 设置更友好的提示信息
                error.value = response === null ? t('search.emptyResults', { defaultValue: '暂无商品数据' }) : t('search.noResults', { defaultValue: '没有找到符合条件的商品' })

                debug.warn('SearchView', '未找到商品数据，API返回:', response)
            }
        } catch (err: any) {
            error.value = t('common.error.loadProductsFailed', { defaultValue: '加载商品失败，' + (err.message || '请稍后重试') })
            products.value = []
            totalProducts.value = 0
            total.value = 0

            debug.error('SearchView', '加载商品失败:', err)
        } finally {
            loading.value = false
        }
    }

    // 添加筛选条件验证函数
    const validateFilters = () => {
        // 验证价格区间
        if (minPrice.value && maxPrice.value) {
            const min = Number(minPrice.value)
            const max = Number(maxPrice.value)
            if (!isNaN(min) && !isNaN(max) && min > max) {
                throw new Error(t('search.invalidPriceRange', { defaultValue: '最小价格不能大于最大价格' }))
            }
        }

        // 验证价格格式
        if (minPrice.value && isNaN(Number(minPrice.value))) {
            throw new Error(t('search.invalidMinPrice', { defaultValue: '最小价格格式无效' }))
        }

        if (maxPrice.value && isNaN(Number(maxPrice.value))) {
            throw new Error(t('search.invalidMaxPrice', { defaultValue: '最大价格格式无效' }))
        }
    }

    // 更新搜索关键词和分类ID的函数
    const updateSearchParams = () => {
        // 获取路由查询参数
        const routeKeyword = route.query.keyword
        const routeCategoryId = route.query.categoryId
        const routeSearchType = route.query.searchType
        const routeImageId = route.query.imageId
        const routeImageUrl = route.query.imageUrl
        const routeOfferId = route.query.offerId

        // 设置搜索类型
        if (routeSearchType) {
            searchStore.searchType = Number(routeSearchType)
        }

        // 设置图片ID
        if (routeImageId && typeof routeImageId === 'string') {
            searchStore.imageId = routeImageId
        } else if (searchStore.searchType === 2) {
            // 如果是图片搜索模式但没有imageId，检查imageUrl
            if (routeImageUrl && typeof routeImageUrl === 'string') {
                searchStore.imageUrl = routeImageUrl
                searchStore.imageId = ''
            } else {
                // 如果既没有imageId也没有imageUrl，则降级为普通搜索
                searchStore.searchType = 1
                searchStore.imageId = ''
                searchStore.imageUrl = ''
            }
        }

        // 设置图片URL
        if (routeImageUrl && typeof routeImageUrl === 'string') {
            searchStore.imageUrl = routeImageUrl
        }

        // 设置关键词
        if (routeOfferId && typeof routeOfferId === 'string') {
            searchStore.keyword = routeOfferId
        }

        // 优先使用props中的keyword
        if (props.keyword) {
            searchKeyword.value = props.keyword
            searchStore.keyword = props.keyword
        } else if (typeof routeKeyword === 'string') {
            searchKeyword.value = routeKeyword
            searchStore.keyword = routeKeyword
        } else if (Array.isArray(routeKeyword) && routeKeyword.length > 0) {
            searchKeyword.value = String(routeKeyword[0])
            searchStore.keyword = String(routeKeyword[0])
        } else {
            searchKeyword.value = ''
            searchStore.keyword = ''
        }

        // 优先使用props中的categoryId
        if (props.categoryId) {
            categoryId.value = props.categoryId
        } else if (typeof routeCategoryId === 'string') {
            categoryId.value = routeCategoryId
        } else if (Array.isArray(routeCategoryId) && routeCategoryId.length > 0) {
            categoryId.value = String(routeCategoryId[0])
        } else {
            categoryId.value = ''
        }

        debug.log('SearchView', '更新搜索参数', {
            searchType: searchStore.searchType,
            imageId: searchStore.imageId,
            imageUrl: searchStore.imageUrl,
            searchKeyword: searchKeyword.value,
            categoryId: categoryId.value,
            offerId: searchStore.offerId,
        })
    }

    // 初始化时加载
    onMounted(() => {
        // 检查环境变量设置的调试状态
        debugEnabled.value = debug.isEnabled()
        // 初始化搜索模式
        initSearchMode()
        // Initial grid column setup based on current screen size
        if (window.innerWidth < 768) {
            // md breakpoint for grid
            gridColumns.value = 2
        } else {
            gridColumns.value = 5
        }
        debug.log('SearchView', '组件已挂载, 搜索关键词:', searchKeyword.value)
        loadProducts()
        window.addEventListener('resize', handleResize)
    })

    // 监听关键词、分类ID和搜索模式变化
    watch(
        () => [props.keyword, route.query.keyword, props.categoryId, route.query.categoryId, route.query.searchType, route.query.imageId, route.query.imageUrl, route.query.offerId],
        (_newValues, oldValues) => {
            // 保存旧参数用于比较
            const oldKeyword = searchKeyword.value
            const oldCategoryId = categoryId.value
            const oldSearchType = searchStore.searchType
            const oldImageId = searchStore.imageId
            const oldImageUrl = searchStore.imageUrl
            const oldOfferId = searchStore.offerId

            // 更新参数
            updateSearchParams()

            // 检查是否刚加载页面或参数变化
            const isInitialLoad = !oldValues || oldValues[0] === undefined
            const isParamsChanged =
                oldKeyword !== searchKeyword.value ||
                oldCategoryId !== categoryId.value ||
                oldSearchType !== searchStore.searchType ||
                oldImageId !== searchStore.imageId ||
                oldImageUrl !== searchStore.imageUrl ||
                oldOfferId !== searchStore.offerId

            if (isInitialLoad || isParamsChanged) {
                debug.log('SearchView', '参数变化, 重新加载', {
                    oldKeyword,
                    newKeyword: searchKeyword.value,
                    oldCategoryId,
                    newCategoryId: categoryId.value,
                    oldSearchType,
                    newSearchType: searchStore.searchType,
                    oldImageId,
                    newImageId: searchStore.imageId,
                    oldImageUrl,
                    newImageUrl: searchStore.imageUrl,
                    oldOfferId,
                    newOfferId: searchStore.offerId,
                })

                // 重置页码到第一页
                currentPage.value = 1
                // 加载产品
                loadProducts()
            }
        },
        { immediate: true }
    )

    // 监听排序方式变化
    watch(
        () => sortBy.value,
        (newSortBy, oldSortBy) => {
            // 当排序方式变化时，自动触发数据加载
            if (oldSortBy && newSortBy !== oldSortBy) {
                debug.log('SearchView', '排序方式变更为:', newSortBy)
                // 重置页码到第一页
                currentPage.value = 1
                // 加载产品
                loadProducts()
            }
        }
    )

    // 监听语言变化
    watch(
        () => locale.value,
        newLocale => {
            if (products.value.length > 0 && originalProductsData.value.length > 0) {
                debug.log('SearchView', '语言切换为:', newLocale)
                // 使用保存的原始数据重新映射产品
                products.value = originalProductsData.value.map((item: any) => item as ProductInfoVO)
            }
        }
    )

    // 页码改变处理
    const handlePageChange = (page: number) => {
        currentPage.value = page
        debug.log('SearchView', '页码变更为:', page)
        loadProducts()
    }

    // 应用筛选处理
    const handleApplyFilters = () => {
        try {
            validateFilters()
            currentPage.value = 1
            debug.log('SearchView', '应用筛选条件', getActiveFilters())
            loadProducts()
            if (isMobile.value) {
                closeFiltersModal()
            }
        } catch (err) {
            error.value = err instanceof Error ? err.message : String(err)
        }
    }

    // 重置所有筛选条件
    const resetAllFilters = () => {
        // 重置所有筛选条件
        minPrice.value = ''
        maxPrice.value = ''
        selectedQualityGuarantees.value = []
        selectedRatings.value = []
        selectedServices.value = []
        selectedPickupRates.value = []

        // 重置当前页为第一页
        currentPage.value = 1

        debug.log('SearchView', '重置所有筛选条件')

        // 重新加载产品
        loadProducts()
    }

    // 用于调试面板的辅助函数
    const getActiveFilters = () => {
        const filters = []
        if (minPrice.value) filters.push(`${t('search.min')}: ${minPrice.value}`)
        if (maxPrice.value) filters.push(`${t('search.max')}: ${maxPrice.value}`)
        if (selectedQualityGuarantees.value.length) filters.push(`${t('search.qualityGuarantee')}: ${selectedQualityGuarantees.value.length}${t('search.debug.items')}`)
        if (selectedRatings.value.length) filters.push(`${t('search.totalEpScore')}: ${selectedRatings.value.join(', ')}`)
        if (selectedServices.value.length) filters.push(`${t('search.serviceGuarantee')}: ${selectedServices.value.length}${t('search.debug.items')}`)
        if (selectedPickupRates.value.length) filters.push(`${t('search.pickupRate')}: ${selectedPickupRates.value.length}${t('search.debug.items')}`)
        return filters.length ? filters.join(', ') : t('search.debug.none')
    }

    // 用于调试面板的请求参数
    const getRequestParams = () => {
        const categories = categoryId.value ? [categoryId.value] : undefined
        const { sortField, sortOrder } = getSortParams.value

        // 收集所有筛选条件
        let filterConditions: string[] = []

        // 映射质量保证选项
        selectedQualityGuarantees.value.forEach(q => {
            if (q === t('search.quality.certifiedFactory', '认证工厂')) {
                filterConditions.push('certifiedFactory')
            } else if (q === t('search.quality.crossBorderSelection', '跨境精选商品')) {
                filterConditions.push('crossBorderSelection')
            } else if (q === t('search.quality.globalSelection', '全球优选')) {
                filterConditions.push('globalSelection')
            } else if (q === t('search.quality.qualitySources', '精品货源')) {
                filterConditions.push('qualitySources')
            }
        })

        // 映射服务选项
        selectedServices.value.forEach(s => {
            if (s === t('search.service.shipToday', '当日发货')) {
                filterConditions.push('shipInToday')
            } else if (s === t('search.service.shipIn24Hours', '24小时发货')) {
                filterConditions.push('shipIn24Hours')
            } else if (s === t('search.service.fortyEightHourShipping', '48小时发货')) {
                filterConditions.push('shipIn48Hours')
            } else if (s === t('search.service.sevenDayReturn', '7天无理由退换')) {
                filterConditions.push('noReason7DReturn')
            } else if (s === t('search.service.supportDropshipping', '支持代发货')) {
                filterConditions.push('isOnePsale')
            } else if (s === t('search.service.supportFreeShipping', '支持包邮')) {
                filterConditions.push('isOnePsaleFreePost')
            }
        })

        // 处理评分筛选
        if (selectedRatings.value.length > 0) {
            const ratingValue = selectedRatings.value[0]
            if (ratingValue === '5星') {
                filterConditions.push('totalEpScoreLv1')
            } else if (ratingValue === '4.5星-5.0星') {
                filterConditions.push('totalEpScoreLv2')
            } else if (ratingValue === '4星-4.5星') {
                filterConditions.push('totalEpScoreLv3')
            } else if (ratingValue === '4星以下') {
                filterConditions.push('totalEpScoreLv4')
            }
        }

        // 映射揽收率选项
        selectedPickupRates.value.forEach(rate => {
            if (rate === '24小时揽收率<95%') {
                filterConditions.push('getRate24HLv1')
            } else if (rate === '24小时揽收率>=95%') {
                filterConditions.push('getRate24HLv2')
            } else if (rate === '24小时揽收率>=99%') {
                filterConditions.push('getRate24HLv3')
            } else if (rate === '48小时揽收率<95%') {
                filterConditions.push('getRate48HLv1')
            } else if (rate === '48小时揽收率>=95%') {
                filterConditions.push('getRate48HLv2')
            } else if (rate === '48小时揽收率>=99%') {
                filterConditions.push('getRate48HLv3')
            }
        })

        return JSON.stringify(
            {
                searchType: searchStore.searchType,
                imageId: searchStore.imageId || undefined,
                imageUrl: searchStore.imageUrl || undefined,
                keyword: searchKeyword.value || undefined,
                offerId: searchStore.offerId || undefined,
                categories: categories,
                pageIndex: currentPage.value,
                pageSize: pageSize.value,
                sortField: sortField, // 修正字段名
                sortOrder: sortOrder,
                minPrice: minPrice.value ? Number(minPrice.value) : undefined,
                maxPrice: maxPrice.value ? Number(maxPrice.value) : undefined,
                filter: filterConditions.length > 0 ? filterConditions.join(',') : undefined,
            },
            null,
            2
        )
    }

    // 添加 onUnmounted 钩子来移除事件监听器
    onUnmounted(() => {
        window.removeEventListener('resize', handleResize)
    })
</script>

<style scoped>
    /* 样式保持简洁 */
    :deep(.gap-2) {
        gap: 0.2rem;
    }
    :deep(.gap-3) {
        gap: 0.5rem;
    }
    :deep(.gap-4) {
        gap: 0.7rem;
    }
    :deep(.gap-5) {
        gap: 1.8rem;
    }
</style>
