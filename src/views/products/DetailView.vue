<template>
    <div class="bg-white min-h-screen">
        <div class="container max-w-7xl mx-auto px-4 py-6 pb-20 lg:pb-6">
            <!-- 面包屑导航 -->
            <div class="mb-4 md:mb-6">
                <nav class="flex text-sm">
                    <router-link to="/" class="text-gray-500 hover:text-gray-900">
                        {{ t('common.home') }}
                    </router-link>
                    <span class="mx-2 text-gray-400">/</span>
                    <span class="text-gray-800 truncate">{{ productTitle }}</span>
                </nav>
            </div>

            <!-- 加载状态 -->
            <div v-if="loading" class="flex justify-center items-center py-12">
                <div class="loading-spinner"></div>
                <p class="ml-4">{{ t('product.detail.loading') }}</p>
            </div>

            <!-- 错误提示 -->
            <div v-else-if="error" class="bg-red-50 p-4 rounded-lg mb-4">
                <p class="text-red-600">{{ error }}</p>
                <button @click="loadProductDetail" class="mt-2 btn-primary">
                    {{ t('common.retry') }}
                </button>
            </div>

            <!-- 商品详情内容 -->
            <div v-else-if="product" class="flex flex-col lg:flex-row gap-4 lg:gap-6">
                <!-- 左侧区域 - 移动端全宽，桌面端60% -->
                <div class="w-full lg:w-3/5 lg:pr-6 pb-4 lg:pb-8">
                    <!-- 商品图片区域 - 移动端优化 -->
                    <div class="mb-4 lg:mb-8">
                        <ProductGallery :product="product" :selected-sku-image="selectedSkuImage" class="max-w-full lg:max-w-2xl lg:mx-auto" />
                    </div>

                    <!-- 移动端：商品规格和购买选项放在图片下方 -->
                    <div class="w-full lg:hidden mb-4">
                        <div class="bg-white rounded-lg p-4 shadow-md border border-gray-100">
                            <!-- 移动端显示商品名称 -->
                            <h1 class="text-lg font-bold mb-3 line-clamp-2">{{ productTitle }}</h1>

                            <!-- 商品基本信息 -->
                            <ProductInfo :product="product" :selectedSku="currentSelectedSku" class="mb-3" />
                        </div>
                    </div>

                    <!-- 商品详情区域 -->
                    <div class="mt-4 lg:mt-12 relative w-full" ref="detailSectionRef">
                        <ProductDetail ref="productDetailRef" :product="product" class="product-detail-with-fixed-tabs w-full" v-model:active-tab="activeTab" />
                    </div>
                </div>

                <!-- 右侧区域 - 仅桌面端显示，移动端已移到图片下方 -->
                <div class="hidden lg:block w-full lg:w-2/5 lg:sticky lg:top-4 self-start rounded-lg bg-white p-4 md:p-6 shadow-md border border-gray-100 mt-4 lg:mt-0">
                    <!-- 右侧内容容器 - 添加最大高度和滚动 -->
                    <div class="right-panel-scrollable overflow-y-auto">
                        <!-- 商品基本信息 -->
                        <ProductInfo :product="product" :selectedSku="currentSelectedSku" class="mb-3 lg:mb-4" />

                        <!-- 商品规格和购买选项 -->
                        <ProductOptions
                            ref="productOptionsRef"
                            :product="product"
                            v-model:selectedOptions="selectedOptions"
                            :is-buy-now-loading="isBuyNowLoading"
                            @update:selectedOptions="updateSelectedSku"
                            @add-to-cart="addToCart"
                            @add-to-favorites="addToFavorites"
                            @buy-now="handleBuyNow"
                            @sku-image-selected="handleSkuImageSelected"
                        />
                    </div>
                </div>
            </div>

            <!-- 商品不存在提示 -->
            <div v-else class="text-center py-12">
                <h2 class="text-xl lg:text-2xl font-bold text-gray-800 mb-4">{{ t('product.detail.notFound') }}</h2>
                <p class="text-gray-600 mb-6">{{ t('common.notFoundMessage') }}</p>
                <router-link to="/" class="btn-primary">
                    {{ t('product.detail.backToHome') }}
                </router-link>
            </div>
        </div>

        <!-- 移动端购买操作下浮窗 -->
        <MobilePurchaseBar
            v-if="product"
            :product="product"
            :selected-options="selectedOptions"
            :is-buy-now-loading="isBuyNowLoading"
            @update:selectedOptions="updateSelectedSku"
            @add-to-cart="addToCart"
            @add-to-favorites="addToFavorites"
            @buy-now="handleBuyNow"
            @sku-image-selected="handleSkuImageSelected"
        />
    </div>
</template>

<script setup lang="ts">
    import { orderApi, type OrderPreviewRequest } from '@/api/modules/order'
    import { productApi, type ProductDetailVO, type ProductSkuInfoVO } from '@/api/modules/product'
    import { useCartStore } from '@/stores/cart'
    import { computed, onMounted, ref, watch } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRoute, useRouter } from 'vue-router'

    // 导入子组件
    import ProductDetail from '@/components/product/detail/ProductDetail.vue'
    import ProductGallery from '@/components/product/detail/ProductGallery.vue'
    import ProductInfo from '@/components/product/detail/ProductInfo.vue'
    import ProductOptions from '@/components/product/detail/ProductOptions.vue'
    import MobilePurchaseBar from '@/components/product/detail/MobilePurchaseBar.vue'

    // 国际化
    const { t, locale } = useI18n()

    // 路由
    const route = useRoute()
    const router = useRouter()
    const productId = computed(() => Number(route.params.offerId))

    // 状态初始化
    const product = ref<ProductDetailVO | null>(null)
    const loading = ref(true)
    const error = ref<string | null>(null)
    const quantity = ref(1)
    const selectedOptions = ref<Record<string, string>>({})
    const selectedSkuImage = ref<string | null>(null)
    const isBuyNowLoading = ref(false)

    // 详情区域引用
    const detailSectionRef = ref<HTMLElement | null>(null)
    const productDetailRef = ref<InstanceType<typeof ProductDetail> | null>(null)
    const productOptionsRef = ref<InstanceType<typeof ProductOptions> | null>(null)
    const activeTab = ref('details')

    // 计算属性
    const productTitle = computed(() => {
        if (!product.value) return t('product.detail.title')
        return locale.value === 'zh-CN' ? product.value.title : product.value.titleEn || product.value.title
    })

    // 当前选中的SKU
    const currentSelectedSku = computed(() => {
        return findMatchingSku()
    })

    // 方法
    const loadProductDetail = async () => {
        loading.value = true
        error.value = null

        try {
            if (!productId.value || isNaN(productId.value)) {
                throw new Error(t('common.invalidId'))
            }

            const detailResponse = await productApi.getProductDetail(productId.value)

            product.value = detailResponse

            const minOrderQty = product.value?.minOrderQuantity || 1
            if (minOrderQty > 1) {
                quantity.value = minOrderQty
            }

            document.title = `${productTitle.value} | Fulfillmen`
        } catch (err: any) {
            error.value = err.message || t('common.error.loadFailed')
            console.error('加载商品详情失败:', err)
        } finally {
            loading.value = false
        }
    }

    // 更新选中的SKU
    const updateSelectedSku = () => {
        if (!product.value?.productSkuInfos || product.value.productSkuInfos.length === 0) return

        try {
            const matchingSku = findMatchingSku()

            if (matchingSku) {
                const availableStock = typeof matchingSku.amountOnSale === 'number' ? matchingSku.amountOnSale : 99999
                if (quantity.value > availableStock) {
                    quantity.value = Math.max(availableStock, product.value.minOrderQuantity || 1)
                }
            }
        } catch (err) {
            console.error('更新SKU时发生错误:', err)
        }
    }

    // 查找匹配当前选项的SKU
    const findMatchingSku = (): ProductSkuInfoVO | null => {
        if (!product.value?.productSkuInfos) return null

        for (const sku of product.value.productSkuInfos) {
            if (!sku.specs || sku.specs.length === 0) {
                // For single-SKU products (which now have a default SKU)
                return sku
            }
            let isMatch = true
            for (const spec of sku.specs) {
                const specName = spec.specName
                const selectedValueId = selectedOptions.value[specName]

                if (!selectedValueId || selectedValueId !== spec.specValue) {
                    isMatch = false
                    break
                }
            }
            if (isMatch) {
                return sku
            }
        }
        return null
    }

    const handleSkuImageSelected = (imageUrl: string | null) => {
        selectedSkuImage.value = imageUrl
    }

    const addToCart = async (cartItems: Array<{ options: Record<string, string>; quantity: number; skuId: number }>) => {
        if (!product.value || cartItems.length === 0) return

        try {
            const cart = useCartStore()

            // 准备批量添加的参数
            const batchParams = {
                productId: String(product.value.id),
                skuList: cartItems.map(item => ({
                    skuId: String(item.skuId),
                    quantity: item.quantity,
                })),
                productInfo: {
                    name: product.value.title,
                    nameTranslated: product.value.titleEn || product.value.title,
                    image: product.value.images?.[0] || '',
                },
            }

            await cart.addMultipleSkusToCart(batchParams)
            
            // 成功添加到购物车后，清零ProductOptions中的数量选择器
            if (productOptionsRef.value && typeof productOptionsRef.value.resetCartData === 'function') {
                productOptionsRef.value.resetCartData()
            }
        } catch (error) {
            console.error('添加到购物车失败:', error)
            // 这里可以显示错误消息，但不影响清零逻辑
        }
    }

    const addToFavorites = () => {
        if (!product.value) return
        console.log('添加到收藏:', product.value.id)
        // TODO: 实现添加到收藏逻辑
    }

    const handleBuyNow = async (cartItems: Array<{ options: Record<string, string>; quantity: number; skuId: number }>) => {
        if (!product.value) return

        isBuyNowLoading.value = true
        try {
            // 准备订单预览数据 - 使用后端期望的完整格式
            const orderData: OrderPreviewRequest = {
                productList: cartItems.map(item => ({
                    skuId: item.skuId,
                    productQuantity: item.quantity,
                })),
                isShoppingCart: 0, // 立即购买不来自购物车
                shoppingCartIds: '', // 立即购买时为空字符串
            }

            // 调用订单预览接口
            const orderPreview = await orderApi.previewOrder(orderData)

            // 检查预览是否成功
            if (orderPreview.orderPreviewSummary.success && orderPreview.idempotentToken) {
                // 优先使用后端返回的幂等令牌进行跳转
                if (orderPreview.idempotentToken && orderPreview.idempotentToken.length > 16) {
                    // 使用幂等令牌跳转
                    router.push({
                        name: 'checkout',
                        query: {
                            token: orderPreview.idempotentToken,
                            source: 'buyNow',
                            productId: String(product.value.id),
                            timestamp: String(Date.now()),
                        },
                    })
                } else {
                    // 降级到sessionStorage方式
                    const previewId = `preview_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
                    sessionStorage.setItem(previewId, JSON.stringify(orderPreview))

                    router.push({
                        name: 'checkout',
                        query: {
                            previewId: previewId,
                            source: 'buyNow',
                            productId: String(product.value.id),
                            timestamp: String(Date.now()),
                        },
                    })
                }
            } else {
                // 预览失败，显示错误信息
                const errorMessages = orderPreview.errors?.map(err => err.errorMessage).join(', ') || '订单预览失败'
                console.error('订单预览失败:', errorMessages)
                // ElMessage.error('订单预览失败: ' + errorMessages)

                // 仍然跳转到checkout页面但不传递预览数据
                router.push({
                    name: 'checkout',
                    query: {
                        source: 'buyNow',
                        productId: String(product.value.id),
                        timestamp: String(Date.now()),
                    },
                })
            }
        } catch (error) {
            console.error('订单预览失败:', error)
            // 如果订单预览失败，仍然跳转到checkout页面但不传递预览数据
            router.push({
                name: 'checkout',
                query: {
                    source: 'buyNow',
                    productId: String(product.value.id),
                    timestamp: String(Date.now()),
                },
            })
        } finally {
            isBuyNowLoading.value = false
        }
    }

    watch(
        () => route.params.offerId,
        newId => {
            if (newId && newId !== String(productId.value)) {
                loadProductDetail()
            }
        }
    )

    onMounted(() => {
        loadProductDetail()
    })
</script>

<style scoped>
    /* 加载动画 */
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 按钮样式 */
    .btn-primary {
        @apply px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors;
    }

    /* 文本截断 */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* 商品详情区域样式 */
    .product-detail-with-fixed-tabs {
        width: 100%;
        overflow: hidden;
    }

    .product-detail-with-fixed-tabs :deep(.tab-content) {
        width: 100%;
        overflow: hidden;
        padding: 1rem 0;
    }

    .product-detail-with-fixed-tabs :deep(.product-images-container) {
        width: 100%;
        max-width: 100%;
    }

    .product-detail-with-fixed-tabs :deep(.product-gallery) {
        width: 100%;
        max-width: 100%;
    }

    /* 详情内容中的图片样式 */
    .product-detail-with-fixed-tabs :deep(.details-tab img) {
        max-width: 100% !important;
        width: 100% !important;
        height: auto !important;
        margin: 0 auto !important;
        display: block !important;
    }

    /* 特别处理阿里巴巴/淘宝图片 */
    .product-detail-with-fixed-tabs :deep(img[src*='alicdn.com']),
    .product-detail-with-fixed-tabs :deep(img[src*='taobao.com']),
    .product-detail-with-fixed-tabs :deep(img[src*='cbu01']),
    .product-detail-with-fixed-tabs :deep(img[src*='ibank']) {
        max-width: 100% !important;
        width: 100% !important;
        height: auto !important;
        object-fit: contain !important;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .container {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .product-detail-with-fixed-tabs :deep(.tab-navigation) {
            overflow-x: auto;
            white-space: nowrap;
            -webkit-overflow-scrolling: touch;
            padding: 0;
            margin: 0 -1rem;
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .product-detail-with-fixed-tabs :deep(.tab-item) {
            display: inline-block;
            padding: 0.75rem 1rem;
            flex-shrink: 0;
        }

        .product-detail-with-fixed-tabs :deep(.tab-content) {
            padding: 0.75rem 0;
        }

        /* 移动端图片优化 */
        .product-detail-with-fixed-tabs :deep(.product-gallery) {
            margin: 0 -1rem;
            padding: 0 1rem;
        }

        /* 移动端右侧面板优化 */
        .right-panel-scrollable {
            max-height: none;
            overflow-y: visible;
        }
    }

    /* 小屏幕优化 */
    @media (max-width: 640px) {
        .container {
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .product-detail-with-fixed-tabs :deep(.tab-navigation) {
            margin: 0 -0.75rem;
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        .product-detail-with-fixed-tabs :deep(.product-gallery) {
            margin: 0 -0.75rem;
            padding: 0 0.75rem;
        }
    }

    /* 针对平板设备的优化 */
    @media (min-width: 768px) and (max-width: 1366px) {
        .product-detail-with-fixed-tabs {
            width: 100%;
            overflow: hidden;
        }

        .product-detail-with-fixed-tabs :deep(.tab-content) {
            width: 100%;
            overflow: hidden;
            padding: 1rem 0;
        }

        .product-detail-with-fixed-tabs :deep(.product-images-container) {
            width: 100%;
            max-width: 100%;
        }

        .product-detail-with-fixed-tabs :deep(.product-gallery) {
            width: 100%;
            max-width: 100%;
        }

        /* 专门处理iPad分辨率下的图片显示 */
        .product-detail-with-fixed-tabs :deep(.details-tab img) {
            max-width: 100% !important;
            width: 100% !important;
            height: auto !important;
        }
    }

    /* 右侧面板滚动样式 */
    .right-panel-scrollable {
        max-height: calc(100vh - 4rem);
        overflow-y: auto;
        scrollbar-width: thin;
        scrollbar-color: #cbd5e0 #f7fafc;
        padding-right: 4px; /* 为滚动条预留空间 */
    }

    .right-panel-scrollable::-webkit-scrollbar {
        width: 6px;
    }

    .right-panel-scrollable::-webkit-scrollbar-track {
        background: #f7fafc;
        border-radius: 8px;
    }

    .right-panel-scrollable::-webkit-scrollbar-thumb {
        background-color: #cbd5e0;
        border-radius: 8px;
    }

    /* 媒体查询 - 适配不同屏幕高度 */
    @media (max-height: 768px) {
        .right-panel-scrollable {
            max-height: calc(100vh - 2rem);
        }
    }

    @media (min-height: 900px) {
        .right-panel-scrollable {
            max-height: calc(100vh - 6rem);
        }
    }

    /* 触摸设备优化 */
    @media (hover: none) and (pointer: coarse) {
        .product-detail-with-fixed-tabs :deep(.tab-item) {
            min-height: 44px; /* 确保触摸目标足够大 */
        }

        .btn-primary {
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
</style>
