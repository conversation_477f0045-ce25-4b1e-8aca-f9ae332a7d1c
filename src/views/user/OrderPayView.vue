<template>
    <div class="pay-container">
        <div v-if="loading" class="loading-state">
            <div class="loading-spinner"></div>
            <p>{{ t('payment.loading', '正在加载支付信息...') }}</p>
        </div>

        <div v-else-if="orderDetail" class="payment-content">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">{{ t('payment.title', '支付订单') }}</h1>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 左侧内容 -->
                <div class="left-section">
                    <!-- 收货地址信息 -->
                    <div class="address-card">
                        <div class="address-header">
                            <div class="location-icon">📍</div>
                            <div class="address-info">
                                <div class="address-title">{{ t("orders.detail.receiverName", "收货人") }}: {{ orderDetail?.deliveryInfo?.receiverName }}</div>
                                <div class="address-phone">{{ orderDetail?.deliveryInfo?.receiverPhone }}</div>
                            </div>
                        </div>
                        <div class="address-detail">
                            {{ orderDetail?.deliveryInfo?.receiverAddress }}
                        </div>
                    </div>

                    <!-- 商品列表 -->
                    <div class="product-section">
                        <div class="section-header">
                            <span class="section-title">{{ t('payment.productInfo', '商品信息') }}</span>
                            <span class="section-subtitle">{{ t('payment.orderNo', '订单号') }}: {{ orderDetail.orderNo }}</span>
                        </div>
                        
                        <!-- 商品列表表格 -->
                        <div class="product-table">
                            <!-- 表格头部 -->
                            <div class="table-header">
                                <div class="header-cell product-info-header">{{ t('payment.table.productInfo', '商品信息') }}</div>
                                <div class="header-cell quantity-header">{{ t('payment.table.quantity', '数量') }}</div>
                                <div class="header-cell unit-price-header">{{ t('payment.table.unitPrice', '单价') }}</div>
                                <div class="header-cell subtotal-header">{{ t('payment.table.subtotal', '小计') }}</div>
                            </div>
                            
                            <!-- 商品行 -->
                            <div v-for="item in orderDetail.items" :key="item.id" class="product-row">
                                <!-- 商品信息列 -->
                                <div class="table-cell product-info-cell">
                                    <div class="product-info-content">
                                        <div class="product-image">
                                            <el-image
                                                style="width: 100%; height: 100%;"
                                                :src="item.productImage"
                                                :alt="item.productTitle"
                                                :preview-src-list="[item.productImage]"
                                            />
                                        </div>
                                        <div class="product-details">
                                            <h4 class="product-title">{{ getProductDisplayName(item) }}</h4>
                                            <div class="product-specs" v-if="item.productSpecs">
                                                {{ getProductSpec(item) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 数量列 -->
                                <div class="table-cell quantity-cell">
                                    <div class="quantity-controls">
                                        <span class="quantity-display">{{ item.quantity }}</span>
                                    </div>
                                </div>
                                
                                <!-- 单价列 -->
                                <div class="table-cell unit-price-cell">
                                    <div class="price-display">{{ getUnitPrice(item) }}</div>
                                </div>
                                
                                <!-- 小计列 -->
                                <div class="table-cell subtotal-cell">
                                    <div class="subtotal-display">{{ getSubtotal(item) }}</div>
                                </div>
                            </div>
                        </div>

                        <!-- 备注 -->
                        <div class="order-note">
                            <div class="note-header">
                                <span class="note-label">{{ t('payment.note', '备注') }}</span>
                                <span class="note-optional">{{ t('payment.optional', '选填') }}</span>
                            </div>
                            <div class="note-input-wrapper">
                                <textarea 
                                    class="note-input" 
                                    :placeholder="t('payment.notePlaceholder', '请先和商家协商一致')" 
                                    v-model="orderDetail.note"
                                    rows="3"
                                    maxlength="200">
                                </textarea>
                                <div class="note-counter">
                                    <span class="char-count">{{ (orderDetail.note || '').length }}/200</span>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>

                <!-- 右侧价格明细 -->
                <div class="right-section">
                    <div class="price-summary">
                        <h3 class="summary-title">{{ t('payment.priceSummary', '价格明细') }}</h3>
                        
                        <div class="price-item">
                            <span class="price-label">{{ t('payment.merchandiseAmount', '商品金额') }}</span>
                            <span class="price-value">{{ merchandiseAmount }}</span>
                        </div>
                        
                        <div class="price-item">
                            <span class="price-label">{{ t('payment.shippingAmount', '运费') }}</span>
                            <span class="price-value">{{ shippingAmount }}</span>
                        </div>
                        
                        <div class="total-price">
                            <span class="total-label">{{ t('payment.payAmount', '实付金额') }}</span>
                            <span class="total-value">{{ payAmount }}</span>
                        </div>
                        
                        <!-- 余额支付确认 -->
                        <div class="balance-payment-section">
                            <div class="payment-method">
                                <div class="method-icon">💰</div>
                                <div class="method-info">
                                    <div class="method-title">{{ t('payment.balancePayment', '余额支付') }}</div>
                                    <div class="method-desc">{{ t('payment.balancePaymentDesc', '使用账户余额完成支付') }}</div>
                                </div>
                            </div>
                            <button @click="handleConfirmPayment" class="pay-button">
                                {{ t('payment.actions.confirmBalance', '确认余额支付') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div v-else class="error-state">
            <p>{{ t('payment.error.loadFailed', '加载订单信息失败，请重试。') }}</p>
            <button @click="loadOrderDetail" class="btn btn-primary">
                {{ t('payment.actions.retry', '重试') }}
            </button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, shallowRef, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { orderApi } from '@/api/modules/order'
import { ElMessage, ElMessageBox, ElImage } from 'element-plus'
import { useCurrencyStore } from '@/stores/currency'

const route = useRoute()
const router = useRouter()
const { t, locale } = useI18n()
const currencyStore = useCurrencyStore()

const loading = ref(true)
const orderDetail = ref<any>(null)

// 货币相关
const useUSD = computed(() => currencyStore.getCurrentCurrency === 'USD')

// 是否为人民币
const isCNY = computed(() => {
    return currencyStore.getCurrentCurrency === 'CNY'
})

const merchandiseAmount = computed(() => {
    if (isCNY.value) {
        return `￥${(orderDetail.value?.priceInfo?.subtotal || 0).toFixed(2)}`
    } else {
        return `$${(orderDetail.value?.priceInfo?.subtotalUsd || 0).toFixed(2)}`
    }
})

const shippingAmount = computed(() => {
    if (isCNY.value) {
        return `￥${(orderDetail.value?.priceInfo?.shippingFee || 0).toFixed(2)}`
    } else {
        return `$${(orderDetail.value?.priceInfo?.shippingFeeUsd || 0).toFixed(2)}`
    }
})

const payAmount = computed(() => {
    if (isCNY.value) {
        return `￥${(orderDetail.value?.priceInfo?.totalAmount || 0).toFixed(2)}`
    } else {
        return `$${(orderDetail.value?.priceInfo?.totalAmountUsd || 0).toFixed(2)}`
    }
})

// 移除了复杂的支付方式选择，现在只支持余额支付

const loadOrderDetail = async () => {
    loading.value = true
    try {
        const orderNo = route.params.orderNo as string
        const result = await orderApi.getOrderDetail(orderNo)
        if (result) {
            orderDetail.value = result
        } else {
            throw new Error('No order detail returned')
        }
    } catch (error) {
        console.error('Failed to load order details:', error)
        ElMessage.error(t('payment.error.loadFailed', '加载订单信息失败'))
        orderDetail.value = null
    } finally {
        loading.value = false
    }
}

const formatDate = (dateString: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleString()
}

// 是否为中文环境
const isChineseLocale = computed(() => {
    return locale.value === 'zh-CN' || locale.value === 'zh'
})

const getProductDisplayName = (productGroup: { productTitle: string; productTitleEn: string }) => {
    return isChineseLocale.value ? productGroup.productTitle : productGroup.productTitleEn
}

const getProductSpec = (productGroup: { productSpecs: string; productSpecsEn: string }) => {
    return isChineseLocale.value ? productGroup.productSpecs : productGroup.productSpecsEn
}

const getUnitPrice = (item: any) => {
    // 根据系统货币设置显示价格
    if (isCNY.value) {
        // 人民币显示
        return `￥${(item.price || 0).toFixed(2)}`
    } else {
        // 美元显示
        const usdPrice = item.priceUsd || 0
        return `$${usdPrice.toFixed(2)}`
    }
}

// 计算商品小计
const getSubtotal = (item: any) => {
    const price = isCNY.value ? (item.price || 0) : (item.priceUsd || 0)
    const subtotal = price * item.quantity
    
    if (isCNY.value) {
        return `￥${subtotal.toFixed(2)}`
    } else {
        return `$${subtotal.toFixed(2)}`
    }
}

// 增加商品数量
const increaseQuantity = (item: any) => {
    item.quantity += 1
    // 这里可以添加更新后端数据的逻辑
}

// 减少商品数量
const decreaseQuantity = (item: any) => {
    if (item.quantity > 1) {
        item.quantity -= 1
        // 这里可以添加更新后端数据的逻辑
    }
}

const handleCancelPayment = () => {
    ElMessageBox.confirm(t('payment.confirm.cancelMessage', '您确定要取消支付吗？'), t('payment.confirm.cancelTitle', '取消支付'), {
        confirmButtonText: t('common.confirm', '确认'),
        cancelButtonText: t('common.cancel', '取消'),
        type: 'warning',
    })
        .then(() => {
            router.push('/user/orders')
        })
        .catch(() => {
            // User canceled the dialog
        })
}

const handleConfirmPayment = async () => {
    if (!orderDetail.value) return

    try {
        // 构建确认支付的消息，显示货币符号和金额
        const paymentAmount = isCNY.value 
            ? `￥${(orderDetail.value.priceInfo?.totalAmount || 0).toFixed(2)}` 
            : `$${(orderDetail.value.priceInfo?.totalAmountUsd || 0).toFixed(2)}`
            
        const confirmMessage = t(
            'payment.confirm.balancePaymentMessage',
            `确认使用余额支付 ${paymentAmount} 吗？支付后将从您的账户余额中扣除相应金额。`
        )
        
        await ElMessageBox.confirm(
            confirmMessage,
            t('payment.confirm.balancePaymentTitle', '确认余额支付'),
            {
                confirmButtonText: t('payment.actions.confirmBalance', '确认余额支付'),
                cancelButtonText: t('common.cancel', '取消'),
                type: 'info',
            }
        )

        ElMessage.info(t('payment.success.processing', '正在处理余额支付，请稍候...'))

        // 调用余额支付API
        await orderApi.payOrder(orderDetail.value.orderNo, 'balance')

        ElMessage.success(t('payment.success.balancePaymentSuccess', '余额支付成功！'))
        
        // 支付成功后跳转到订单追踪页面
        setTimeout(() => {
            router.push('/user/track-orders')
        }, 1500)

    } catch (error) {
        if (error !== 'cancel') {
            console.error('Balance payment failed:', error)
            ElMessage.error(t('payment.error.balancePaymentFailed', '余额支付失败，请检查账户余额是否充足'))
        }
    }
}


onMounted(() => {
    loadOrderDetail()
})
</script>

<style scoped lang="scss">
.pay-container {
    background-color: #f5f5f5;
    min-height: 100vh;
    padding: 20px;
}

.payment-content {
    max-width: 1200px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 20px;
}

.page-title {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.main-content {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-section {
    width: 300px;
    flex-shrink: 0;
}

// 地址卡片样式
.address-card {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.address-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 12px;
}

.location-icon {
    font-size: 18px;
    margin-top: 2px;
}

.address-info {
    flex: 1;
}

.address-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.address-phone {
    font-size: 14px;
    color: #666;
}

.address-detail {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

// 商品区域样式
.product-section {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.section-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.section-subtitle {
    font-size: 14px;
    color: #52018d;
}

// 商品表格样式
.product-table {
    margin-bottom: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

// 表格头部样式
.table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.header-cell {
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    text-align: center;
    
    &.product-info-header {
        text-align: left;
    }
}

// 商品行样式
.product-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    border-bottom: 1px solid #f0f0f0;
    
    &:last-child {
        border-bottom: none;
    }
    
    &:hover {
        background-color: #f9fafb;
    }
}

.table-cell {
    padding: 16px;
    display: flex;
    align-items: center;
    
    &.product-info-cell {
        justify-content: flex-start;
    }
    
    &.quantity-cell,
    &.unit-price-cell,
    &.subtotal-cell {
        justify-content: center;
    }
}

// 商品信息内容样式
.product-info-content {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 100%;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
    border: 1px solid #e5e7eb;

    :deep(.el-image__inner) {
        object-fit: cover;
        width: 100%;
        height: 100%;
    }
}

.product-details {
    flex: 1;
    min-width: 0;

    .product-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .product-specs {
        font-size: 12px;
        color: #6b7280;
        margin-bottom: 4px;
    }
}

// 配送信息样式
.product-delivery-info {
    margin-top: 4px;
}

.delivery-tag {
    font-size: 11px;
    color: #6b7280;
    background-color: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    border: 1px solid #e5e7eb;
}

// 数量控制样式
.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0;
    overflow: hidden;
}

.quantity-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: #6b7280;
    transition: all 0.2s;

    &:hover {
        background: #f3f4f6;
        color: #374151;
    }
    
    &.decrease {
        border-right: 1px solid #e5e7eb;
    }
    
    &.increase {
        border-left: 1px solid #e5e7eb;
    }
}

.quantity-display {
    font-size: 14px;
    color: #333;
    min-width: 40px;
    text-align: center;
    padding: 6px 8px;
    background: white;
}

// 价格显示样式
.price-display {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.subtotal-display {
    font-size: 14px;
    font-weight: 600;
    color: #dc2626;
}

// 备注样式
.order-note {
    background: #fafafa;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    
    &:hover {
        border-color: #52018d;
        box-shadow: 0 4px 12px rgba(82, 1, 141, 0.1);
    }
}

.note-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.note-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    
    &::before {
        content: "📝";
        margin-right: 8px;
        font-size: 18px;
    }
}

.note-optional {
    font-size: 12px;
    color: #999;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.note-input-wrapper {
    position: relative;
}

.note-input {
    width: 100%;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #333;
    background: white;
    resize: vertical;
    min-height: 80px;
    transition: all 0.3s ease;
    font-family: inherit;
    line-height: 1.5;
    
    &:focus {
        outline: none;
        border-color: #52018d;
        box-shadow: 0 0 0 3px rgba(82, 1, 141, 0.1);
    }
    
    &::placeholder {
        color: #999;
        font-style: italic;
    }
}

.note-counter {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
}

.char-count {
    font-size: 12px;
    color: #999;
    font-weight: 500;
}

// 价格明细样式
.price-summary {
    background: white;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 20px;
}

.summary-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 16px 0;
    padding-bottom: 12px;
    border-bottom: 1px solid #f0f0f0;
}

.price-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 14px;
}

.price-label {
    color: #666;
}

.price-value {
    color: #333;
    font-weight: 500;
}

.total-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid #f0f0f0;
    margin: 16px 0;
}

.total-label {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.total-value {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
}

.pay-button {
    width: 100%;
    height: 44px;
    background: #52018d;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        background: #3a016d;
    }

    &:active {
        transform: translateY(1px);
    }
}

// 余额支付区域样式
.balance-payment-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.payment-method {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.method-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #52018d;
    border-radius: 50%;
    color: white;
}

.method-info {
    flex: 1;
}

.method-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.method-desc {
    font-size: 14px;
    color: #666;
    line-height: 1.4;
}

// 通用按钮样式
.btn {
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid transparent;
}

.btn-primary {
    background: #ff6b35;
    color: white;
    border-color: #ff6b35;
    
    &:hover { 
        background: #e55a2b; 
    }
}

.btn-secondary {
    background: white;
    color: #666;
    border-color: #ddd;
    
    &:hover { 
        background: #f5f5f5; 
    }
}

// 加载和错误状态样式
.loading-state, .error-state {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 20px auto;
    max-width: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f0f0f0;
    border-top: 3px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

// 响应式设计
@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .right-section {
        width: 100%;
        margin-top: 20px;
    }
    
    .price-summary {
        position: static;
    }
    
    // 移动端表格样式调整
    .table-header {
        display: none; // 隐藏表格头部
    }
    
    .product-row {
        display: block;
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        
        &:hover {
            background-color: transparent;
        }
    }
    
    .table-cell {
        padding: 0;
        display: block;
        margin-bottom: 12px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    
    .product-info-content {
        margin-bottom: 12px;
    }
    
    .quantity-cell,
    .unit-price-cell,
    .subtotal-cell {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        &::before {
            content: attr(data-label);
            font-weight: 600;
            color: #374151;
            font-size: 14px;
        }
    }
    
    .quantity-cell::before {
        content: "数量:";
    }
    
    .unit-price-cell::before {
        content: "单价:";
    }
    
    .subtotal-cell::before {
        content: "小计:";
    }
    
    // 移动端备注样式调整
    .order-note {
        padding: 16px;
        margin: 16px 0;
    }
    
    .note-label {
        font-size: 14px;
        
        &::before {
            font-size: 16px;
        }
    }
    
    .note-input {
        min-height: 70px;
        padding: 10px 12px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .pay-container {
        padding: 12px;
    }
    
    .page-title {
        font-size: 20px;
    }
    
    .address-card, .product-section, .price-summary {
        padding: 12px;
        border-radius: 6px;
    }
    
    .product-image {
        width: 50px;
        height: 50px;
    }
    
    .product-title {
        font-size: 13px !important;
    }
    
    .quantity-btn {
        width: 20px;
        height: 20px;
    }
    
    // 小屏幕备注样式调整
    .order-note {
        padding: 12px;
        margin: 12px 0;
        border-radius: 8px;
    }
    
    .note-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .note-label {
        font-size: 13px;
    }
    
    .note-optional {
        font-size: 11px;
        padding: 3px 6px;
    }
    
    .note-input {
        min-height: 60px;
        padding: 8px 10px;
        font-size: 12px;
    }
    
    .char-count {
        font-size: 11px;
    }
}
</style>
