<template>
    <div class="order-detail-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="header-content">
                <div class="header-info">
                    <h1 class="page-title">{{ t('orders.detail.title', '订单详情') }}</h1>
                    <p class="page-subtitle">{{ t('orders.detail.subtitle', '查看订单的详细信息') }}</p>
                </div>
                <div class="back-button" @click="goBack">
                    <ArrowLeft :size="20" />
                    <span>{{ t('common.back', '返回') }}</span>
                </div>
            </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p>{{ t('orders.detail.loading', '加载中...') }}</p>
        </div>

        <!-- 订单详情内容 -->
        <div v-else-if="orderDetail" class="order-detail-content">
            <!-- 订单状态进度条 -->
            <div class="info-card order-status-progress">
                <div class="card-content">
                    <!-- 已取消订单显示 -->
                    <div v-if="Number(orderDetail?.status) === 11" class="cancelled-order-display">
                        <div class="cancelled-icon">
                            <XCircle :size="24" />
                        </div>
                        <div class="cancelled-text">
                            <h4 class="cancelled-title">{{ t('orders.status.cancelled', '已取消订单') }}</h4>
                            <p class="cancelled-description">{{ t('orders.cancel.description', '此订单已取消') }}</p>
                        </div>
                    </div>
                    <!-- 正常订单进度显示 -->
                    <div v-else class="order-progress-steps">
                        <div class="text-center mb-2">
                            <span class="text-base font-bold text-gray-800">{{ t('orders.progressTitle', { percentage: getProgressPercentage(orderDetail?.status || '') }) }}</span>
                        </div>
                        <div class="flex w-full items-start">
                            <template v-for="(step, index) in getOrderSteps(orderDetail?.status || '')" :key="step.status">
                                <div class="flex flex-col items-center text-center">
                                    <div
                                        class="step-icon"
                                        :class="{
                                            'step-completed': step.completed,
                                            'step-current step-active': step.current,
                                            'step-pending': !step.completed && !step.current,
                                        }"
                                    >
                                        <component :is="getIconComponent(step.icon)" :size="16" class="step-icon-svg" />
                                    </div>
                                    <span
                                        class="step-title"
                                        :class="{
                                            'text-green-600': step.completed,
                                            'text-orange-600': step.current,
                                            'text-gray-500': !step.completed && !step.current,
                                        }"
                                    >
                                        {{ t(step.title) }}
                                    </span>
                                </div>
                                <div
                                    v-if="index < getOrderSteps(orderDetail?.status || '').length - 1"
                                    class="flex-grow h-1 rounded-full transition-all duration-500 mx-1 sm:mx-2 mt-[1.125rem]"
                                    :class="step.completed ? 'bg-gradient-to-r from-green-500 to-green-400 shadow-sm' : 'bg-gray-200'"
                                ></div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 当前订单状态信息 -->
            <div class="info-card current-status-info">
                <div class="card-content">
                    <div class="status-header">
                        <div class="status-title-row">
                            <span class="status-label">{{ t('orders.detail.currentStatus', '当前订单状态') }}:</span>
                            <el-tag :type="getStatusTagType(orderDetail?.status)" size="large">
                                {{ getOrderStatusText(orderDetail?.status) }}
                            </el-tag>
                            <span v-if="orderDetail?.status === 'PAYMENT_PENDING'" class="countdown-text">
                                <Clock :size="16" class="mr-2" />{{ getCountdownText() }} {{ t('orders.detail.countdownText', '后,系统将关闭交易') }}
                            </span>
                        </div>
                    </div>
                    
                    <!-- 订单操作区域 - 仅在待支付状态时显示 -->
                    <div v-if="orderDetail?.status === 'PAYMENT_PENDING' || orderDetail?.status === 1" class="action-list">
                        <div class="action-item">
                            <span class="action-text">{{ t('orders.actions.clickHere', '点击这里') }}</span>
                            <el-button
                                v-if="canPayOrder"
                                type="primary"
                                size="large"
                                @click="payOrder"
                                :loading="actionLoading"
                                class="custom-pay-button"
                            >
                                <CreditCard :size="16" class="mr-2" />
                                {{ t('orders.actions.pay', '立即支付') }}
                            </el-button>
                        </div>
                        <div class="action-item">
                            <span class="action-text">{{ t('orders.actions.cancelHint', '如果您不想购买,可以关闭交易') }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单基本信息和商品列表 -->
            <div class="info-card order-basic-info">
                <div class="card-header">
                    <h3 class="card-title">{{ t('orders.detail.orderInfo', '订单信息') }}</h3>
                </div>
                <div class="card-content">
                    <!-- 订单基本信息网格 -->
                    <div class="order-info-grid">
                        <!-- 第一行：订单号和订单状态 -->
                        <div class="info-row">
                            <div class="info-item">
                                <span class="label">{{ t('orders.detail.orderNo', '订单号') }}:</span>
                                <span class="value order-no">{{ orderDetail?.orderNo }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">{{ t('orders.detail.orderStatus', '订单状态') }}:</span>
                                <span class="value">
                                    <el-tag :type="getStatusTagType(orderDetail?.status)" size="large">
                                        {{ getOrderStatusText(orderDetail?.status) }}
                                    </el-tag>
                                </span>
                            </div>
                        </div>
                        
                        <!-- 第二行：下单时间和更新时间 -->
                        <div class="info-row">
                            <div class="info-item">
                                <span class="label">{{ t('orders.detail.createTime', '下单时间') }}:</span>
                                <span class="value">{{ formatDate(orderDetail?.createTime) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">{{ t('orders.detail.updateTime', '更新时间') }}:</span>
                                <span class="value">{{ formatDate(orderDetail?.updateTime) }}</span>
                            </div>
                        </div>
                        
                        <!-- 第三行：收货人信息 -->
                        <div class="info-row">
                            <div class="info-item">
                                <span class="label">{{ t('orders.detail.receiverName', '收货人名称') }}:</span>
                                <span class="value">{{ orderDetail?.deliveryInfo?.receiverName }}</span>
                            </div>
                            <div class="info-item">
                                <span class="label">{{ t('orders.detail.receiverPhone', '收货人电话') }}:</span>
                                <span class="value">{{ orderDetail?.deliveryInfo?.receiverPhone }}</span>
                            </div>
                        </div>
                        
                        <!-- 第四行：发货地址（占满整行） -->
                        <div class="info-row full-width">
                            <div class="info-item full-width">
                                <span class="label">{{ t('orders.detail.deliveryAddress', '发货地址') }}:</span>
                                <span class="value address">{{ orderDetail?.deliveryInfo?.address }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 商品列表部分 -->
                    <div class="items-section">
                        <div class="items-header">
                            <h4 class="items-title">{{ t('orders.detail.items', '订单商品') }}</h4>
                            <span class="items-count">{{ orderDetail?.items?.length || 0 }} {{ t('orders.detail.itemsCount', '件商品') }}</span>
                        </div>
                        
                        <div v-if="orderDetail?.items && orderDetail.items.length > 0" class="items-cards">
                            <!-- 商品卡片列表 -->
                            <div class="cards-container">
                                <div
                                    v-for="item in orderDetail.items"
                                    :key="item.id"
                                    class="product-item"
                                >
                                    <!-- 商品图片 -->
                                    <div class="product-image-wrapper">
                                        <el-image
                                            class="product-image"
                                            :src="item.productImage"
                                            :alt="item.productTitle"
                                            :preview-src-list="[item.productImage]"
                                        />
                                    </div>
                                    
                                    <!-- 商品信息 -->
                                    <div class="product-info-wrapper">
                                        <!-- 商品标题 -->
                                        <h3 class="product-title">
                                            {{ getProductDisplayName(item) }}
                                        </h3>
                                        
                                        <!-- 商品规格 -->
                                        <div v-if="item.productSpecs" class="product-specs">
                                            {{ getProductSpec(item) }}
                                        </div>
                                        
                                        <!-- SKU信息 -->
                                        <div class="product-sku">
                                            SKU: {{ item.skuId || item.sku_id || item.id || 'N/A' }}
                                        </div>
                                        
                                        <!-- 数量和价格 -->
                                        <div class="quantity-price-row">
                                            <div class="quantity-info">
                                                <span class="quantity-label">{{ t('orders.detail.quantity', '数量') }}</span>
                                                <span class="quantity-value">{{ item.quantity || 0 }}</span>
                                            </div>
                                            <span class="separator">|</span>
                                            <div class="price-info">
                                                <span class="price-label">{{ t('orders.detail.unitPrice', '单价') }}</span>
                                                <span class="price-value">{{ getUnitPrice(item) }}</span>
                                            </div>
                                        </div>
                                        
                                        <!-- 物流状态 -->
                                        <div class="logistics-status">
                                            <span class="status-badge">
                                                {{ getItemStatusText(item.logisticsStatus) }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else class="empty-items">
                            <p>{{ t('orders.detail.noItems', '暂无商品信息') }}</p>
                        </div>

                        <!-- 价格明细 -->
                        <div class="price-breakdown mt-6 border-t pt-6">
                            <div class="price-item">
                                <span class="price-label">{{ t('orders.detail.merchandiseAmount', '商品金额') }}:</span>
                                <span class="price-value">
                                    {{ currencyStore.formatPriceWithSymbol(getCurrentMerchandiseAmount()) }}
                                </span>
                            </div>
                            <div class="price-item">
                                <span class="price-label">{{ t('orders.detail.shippingAmount', '运费') }}:</span>
                                <span class="price-value">
                                    {{ currencyStore.formatPriceWithSymbol(getCurrentShippingFee()) }}
                                </span>
                            </div>
                            <div class="price-item total">
                                <span class="price-label">{{ t('orders.detail.totalAmount', '订单总额') }}:</span>
                                <span class="price-value total-value">
                                    {{ currencyStore.formatPriceWithSymbol(getCurrentTotalAmount()) }}
                                </span>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="order-actions">
                                <div class="actions-grid">
                                    <!-- 取消订单按钮 - 改进显示逻辑 -->
                                    <el-button
                                        v-if="canCancelOrder"
                                        type="danger"
                                        size="large"
                                        @click="cancelOrder"
                                        :loading="actionLoading"
                                    >
                                        <XCircle :size="16" class="mr-2" />
                                        {{ t('orders.actions.cancel', '取消订单') }}
                                    </el-button>
                                    
                                    <!-- 支付订单按钮 -->
                                    <el-button
                                        v-if="canPayOrder"
                                        type="primary"
                                        size="large"
                                        @click="payOrder"
                                        :loading="actionLoading"
                                        class="custom-pay-button"
                                    >
                                        <CreditCard :size="16" class="mr-2" />
                                        {{ t('orders.actions.pay', '立即支付') }}
                                    </el-button>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-container">
            <div class="error-icon">
                <XCircle :size="48" />
            </div>
            <h3>{{ t('orders.detail.error.title', '加载失败') }}</h3>
            <p>{{ error }}</p>
            <el-button type="primary" @click="loadOrderDetail">
                {{ t('orders.detail.error.retry', '重试') }}
            </el-button>
        </div>

        <!-- 取消订单对话框 -->
        <el-dialog
            v-model="cancelDialogVisible"
            :title="t('orders.cancel.title', '取消订单')"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="cancel-reason-form">
                <div class="form-item">
                    <label class="form-label">{{ t('orders.cancel.reason', '取消原因') }}</label>
                    <el-select 
                        v-model="selectedCancelReason" 
                        :placeholder="t('orders.cancel.reason', '请选择取消原因')"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="reason in cancelReasons"
                            :key="reason"
                            :label="reason"
                            :value="reason"
                        />
                    </el-select>
                </div>
                <div v-if="selectedCancelReason === t('orders.cancel.reasons.other', '其他')" class="form-item">
                    <label class="form-label">{{ t('orders.cancel.otherReason', '其他原因') }}</label>
                    <el-input
                        v-model="otherCancelReason"
                        type="textarea"
                        :rows="3"
                        :placeholder="t('orders.cancel.placeholder', '请详细说明取消原因')"
                    />
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelDialogVisible = false">{{ t('common.cancel', '取消') }}</el-button>
                    <el-button type="primary" :loading="actionLoading" @click="handleCancelConfirm">{{ t('common.confirm', '确认') }}</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRoute, useRouter } from 'vue-router'
import { orderApi } from '@/api/modules/order'
import { ElMessage, ElMessageBox, ElImage, ElDialog, ElSelect, ElOption, ElInput, ElButton } from 'element-plus'
import { useCurrencyStore } from '@/stores/currency'
import {
    ArrowLeft,
    CreditCard,
    XCircle,
    CheckCircle,
    Truck,
    ShoppingCart,
    Clock,
    Loader,
    Home,
    Trophy,
    HelpCircle,
    Save,
    Info,
    Edit3,
} from 'lucide-vue-next'
import { time } from 'console'

const { t, locale } = useI18n()
const route = useRoute()
const router = useRouter()
const currencyStore = useCurrencyStore()

// 响应式数据
const loading = ref(false)
const actionLoading = ref(false)
const error = ref<string | null>(null)
const orderDetail = ref<any>(null)

// 计算属性
const orderNo = computed(() => route.params.orderNo as string)

// 强制响应式更新的计算属性
const currentLocale = computed(() => locale.value)

// 判断是否可以取消订单
const canCancelOrder = computed(() => {
    if (!orderDetail.value) {
        return false
    }
    
    // 优先检查 actionButtons.canCancel
    if (orderDetail.value.actionButtons?.canCancel) {
        return true
    }
    
    // 检查订单状态 - 待支付状态可以取消
    const status = orderDetail.value.status || orderDetail.value.orderStatus
    
    if (status === 'PAYMENT_PENDING' || status === 'PAYMENT_COMPLETED' || status === 'PENDING_REVIEW' || status === 'PROCUREMENT_IN_PROGRESS') {
        return true
    }
    
    // 检查数字状态 - 1:待支付, 2:已支付(可取消)
    if (typeof status === 'number' && (status === 1 || status === 2)) {
        return true
    }
    
    // 检查字符串状态 - 已支付状态也可以取消
    if (status === 'PAID') {
        return true
    }
    
    return false
})

// 判断是否可以支付订单
const canPayOrder = computed(() => {
    if (!orderDetail.value) return false
    
    // 检查订单状态 - 如果是已取消状态，不能支付
    const status = orderDetail.value.status || orderDetail.value.orderStatus
    if (status === 'CANCELLED' || status === 11) {
        return false
    }
    
    // 优先检查 actionButtons.canPay
    if (orderDetail.value.actionButtons?.canPay) {
        return true
    }
    
    // 检查订单状态 - 待支付状态可以支付
    if (status === 'PAYMENT_PENDING' || status === 1) {
        return true
    }
    
    return false
})

// 订单状态筛选选项 (改为计算属性以支持i18n动态更新)
const statusFilters = computed(() => [
    { label: t('orders.status.all'), value: undefined, count: undefined },
    { label: t('orders.status.pending'), value: 1, count: undefined },
    { label: t('orders.status.paid'), value: 2, count: undefined },
    { label: t('orders.status.purchasing'), value: 3, count: undefined }, // 采购中状态3、4、5
    { label: t('orders.status.shipped'), value: 7, count: undefined },
    { label: t('orders.status.inWarehouse'), value: 10, count: undefined },
    { label: t('orders.status.completed'), value: 13, count: undefined },
    { label: t('orders.status.cancelled'), value: 11, count: undefined },
])

// 图标映射
const iconMap: { [key: string]: any } = {
    save: Save,
    'credit-card': CreditCard,
    'check-circle': CheckCircle,
    'shopping-cart': ShoppingCart,
    'clock-circle': Clock,
    truck: Truck,
    loading: Loader,
    home: Home,
    trophy: Trophy,
    'x-circle': XCircle,
    'help-circle': HelpCircle,
    info: Info,
    edit3: Edit3,
}

// 是否为中文环境
const isChineseLocale = computed(() => {
    return locale.value === 'zh-CN' || locale.value === 'zh'
})

const getProductDisplayName = (productGroup: { productTitle: string; productTitleEn: string }) => {
    return isChineseLocale.value ? productGroup.productTitle : productGroup.productTitleEn
}

const getProductSpec = (productGroup: { productSpecs: string; productSpecsEn: string }) => {
    return isChineseLocale.value ? productGroup.productSpecs : productGroup.productSpecsEn
}

// 货币相关
const useUSD = computed(() => currencyStore.getCurrentCurrency === 'USD')

// 是否为人民币
const isCNY = computed(() => {
    return currencyStore.getCurrentCurrency === 'CNY'
})

const getUnitPrice = (item: any) => {
    // 根据系统货币设置显示价格
    if (isCNY.value) {
        // 人民币显示
        return `￥${(item.price || 0).toFixed(2)}/${item.unit}`
    } else {
        // 美元显示
        const usdPrice = item.priceUsd || 0
        return `$${usdPrice.toFixed(2)}/${item.unitEn || item.unit}`
    }
}

// 方法
const getOrderStatusText = (status: string | number): string => {
    // 处理字符串状态
    if (typeof status === 'string') {
        const statusMap: { [key: string]: string } = {
            'PAYMENT_PENDING': t('orders.status.pending', '待支付'),
            'PAID': t('orders.status.paid', '已支付'),
            'PURCHASING': t('orders.status.purchasing', '采购中'),
            'SHIPPED': t('orders.status.shipped', '已发货'),
            'IN_WAREHOUSE': t('orders.status.inWarehouse', '已入库'),
            'COMPLETED': t('orders.status.completed', '已完成'),
            'CANCELLED': t('orders.status.cancelled', '已取消'),
        }
        return statusMap[status] || t('orders.status.unknown', '未知状态')
    }
    
    // 处理数字状态（保持兼容性）
    const statusMap: { [key: number]: string } = {
        1: t('orders.status.pending', '待支付'),
        2: t('orders.status.paid', '已支付'),
        3: t('orders.status.purchasing', '采购中'),
        4: t('orders.status.purchasing', '采购中'),
        5: t('orders.status.purchasing', '采购中'),
        6: t('orders.status.purchasing', '采购中'),
        7: t('orders.status.shipped', '已发货'),
        8: t('orders.status.shipped', '已发货'),
        9: t('orders.status.shipped', '已发货'),
        10: t('orders.status.inWarehouse', '已入库'),
        13: t('orders.status.completed', '已完成'),
        11: t('orders.status.cancelled', '已取消'),
    }
    return statusMap[status] || t('orders.status.unknown', '未知状态')
}

const getStatusTagType = (status: string | number): string => {
    // 处理字符串状态
    if (typeof status === 'string') {
        const typeMap: { [key: string]: string } = {
            'PAYMENT_PENDING': 'warning',
            'PAID': 'success',
            'PURCHASING': 'info',
            'SHIPPED': 'primary',
            'IN_WAREHOUSE': 'info',
            'COMPLETED': 'success',
            'CANCELLED': 'danger',
        }
        return typeMap[status] || 'info'
    }
    
    // 处理数字状态（保持兼容性）- 与getOrderStatusText函数保持一致
    const typeMap: { [key: number]: string } = {
        1: 'warning',   // 待支付
        2: 'success',   // 已支付
        3: 'info',      // 采购中
        4: 'info',      // 采购中
        5: 'info',      // 采购中
        6: 'info',      // 采购中
        7: 'primary',   // 已发货
        8: 'primary',   // 已发货
        9: 'primary',   // 已发货
        10: 'info',     // 已入库
        13: 'success',  // 已完成
        11: 'danger',   // 已取消
    }
    return typeMap[status] || 'info'
}

const formatDate = (dateString: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
    })
}

const loadOrderDetail = async () => {
    if (!orderNo.value) {
        error.value = t('orders.detail.error.invalidOrderNo', '无效的订单号')
        return
    }

    loading.value = true
    error.value = null

    try {
        const data = await orderApi.getOrderDetail(orderNo.value)
        orderDetail.value = data
    } catch (err: any) {
        console.error('加载订单详情失败:', err)
        error.value = err.message || t('orders.detail.error.loadFailed', '加载订单详情失败')
    } finally {
        loading.value = false
    }
}

const goBack = () => {
    router.back()
}

const payOrder = () => {
    router.push(`/user/orders/${orderNo.value}/pay`)
}

// 添加取消订单对话框相关的响应式变量
const cancelDialogVisible = ref(false)
const selectedCancelReason = ref('')
const otherCancelReason = ref('')

// 将 cancelReasons 改为计算属性以支持国际化
const cancelReasons = computed(() => [
    t('orders.cancel.reasons.outOfStock', '商品缺货'),
    t('orders.cancel.reasons.tooHighPrice', '价格太高'),
    t('orders.cancel.reasons.informationError', '信息填写错误'),
    t('orders.cancel.reasons.duplicateOrder', '重复下单'),
    t('orders.cancel.reasons.other', '其他')
])

const cancelOrder = async () => {
    if (!orderDetail.value) return
    selectedCancelReason.value = ''
    otherCancelReason.value = ''
    cancelDialogVisible.value = true
}

const handleCancelConfirm = async () => {
    if (!selectedCancelReason.value) {
        ElMessage.error(t('orders.cancel.selectReason', '请选择取消原因'))
        return
    }
    
    if (selectedCancelReason.value === t('orders.cancel.reasons.other', '其他') && !otherCancelReason.value.trim()) {
        ElMessage.error(t('orders.cancel.otherReason', '请输入其他原因'))
        return
    }
    
    let reason = selectedCancelReason.value
    if (selectedCancelReason.value === t('orders.cancel.reasons.other', '其他')) {
        reason = otherCancelReason.value.trim()
    }
    
    try {
        actionLoading.value = true
        await orderApi.cancelOrder(orderDetail.value.orderNo, reason)
        ElMessage.success(t('orders.success.cancelled', '订单取消成功'))
        cancelDialogVisible.value = false
        loadOrderDetail() // 重新加载订单详情
    } catch (err: any) {
        console.error('取消订单失败:', err.response || err)
        const errorMessage = err?.response?.data?.message || err?.message || t('orders.error.cancelFailed', '取消订单失败')
        ElMessage.error(errorMessage)
    } finally {
        actionLoading.value = false
    }
}

const confirmReceipt = async () => {
    if (!orderDetail.value) return

    try {
        actionLoading.value = true
        await ElMessageBox.confirm(
            t('orders.confirm.receipt', '您确定已经收到商品了吗？'),
            t('orders.confirm.title', '确认操作'),
            {
                confirmButtonText: t('common.confirm', '确认'),
                cancelButtonText: t('common.cancel', '取消'),
                type: 'warning',
            }
        )

        await orderApi.confirmReceipt(orderDetail.value.orderNo)
        ElMessage.success(t('orders.success.confirmed', '确认收货成功'))
        loadOrderDetail() // 重新加载订单详情
    } catch (err: any) {
        if (err !== 'cancel') {
            console.error('确认收货失败:', err)
            const errorMessage = err?.response?.data?.message || err?.message || t('orders.error.confirmFailed', '确认收货失败')
            ElMessage.error(errorMessage)
        }
    } finally {
        actionLoading.value = false
    }
}

const viewTracking = () => {
    if (!orderDetail.value) return
    // TODO: 实现物流追踪功能
    ElMessage.info(t('orders.detail.trackingComingSoon', '物流追踪功能即将上线'))
}

const applyRefund = () => {
    if (!orderDetail.value) return
    // TODO: 实现退款申请功能
    ElMessage.info(t('orders.detail.refundComingSoon', '退款申请功能即将上线'))
}

const getItemStatusText = (status: string | number): string => {
    // 处理字符串状态
    if (typeof status === 'string') {
        const statusMap: { [key: string]: string } = {
            'PENDING_PAYMENT': t('orders.itemStatus.pendingPayment', '待支付'),
            'PAID': t('orders.itemStatus.paid', '已支付'),
            'PURCHASING': t('orders.itemStatus.purchasing', '采购中'),
            'SHIPPED': t('orders.itemStatus.shipped', '已发货'),
            'IN_WAREHOUSE': t('orders.itemStatus.inWarehouse', '已入库'),
            'COMPLETED': t('orders.itemStatus.completed', '已完成'),
            'CANCELLED': t('orders.itemStatus.cancelled', '已取消'),
        }
        return statusMap[status] || t('orders.itemStatus.unknown', '未知状态')
    }
    
    // 处理数字状态（保持兼容性）- 与getOrderStatusText函数保持一致
    const statusMap: { [key: number]: string } = {
        1: t('orders.itemStatus.pendingPayment', '待支付'),
        2: t('orders.itemStatus.paid', '已支付'),
        3: t('orders.itemStatus.purchasing', '采购中'),
        4: t('orders.itemStatus.purchasing', '采购中'),
        5: t('orders.itemStatus.purchasing', '采购中'),
        6: t('orders.itemStatus.purchasing', '采购中'),
        7: t('orders.itemStatus.shipped', '已发货'),
        8: t('orders.itemStatus.shipped', '已发货'),
        9: t('orders.itemStatus.shipped', '已发货'),
        10: t('orders.itemStatus.inWarehouse', '已入库'),
        13: t('orders.itemStatus.completed', '已完成'),
        11: t('orders.itemStatus.cancelled', '已取消'),
    }
    return statusMap[status] || t('orders.itemStatus.unknown', '未知状态')
}

const isStatusActive = (status: string) => {
    return orderDetail.value?.orderStatus === status
}

const getCountdownText = () => {
    if (!orderDetail.value?.createTime) return '--'
    const createTime = new Date(orderDetail.value.createTime).getTime()
    const now = Date.now()
    const maxDuration = 5 * 24 * 60 * 60 * 1000 // 5天
    const diff = now - createTime
    const left = maxDuration - diff

    if (left <= 0) return '订单已关闭'

    const days = Math.floor(left / (1000 * 60 * 60 * 24))
    const hours = Math.floor((left % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((left % (1000 * 60 * 60)) / (1000 * 60))

    let result = ''
    if (days > 0) result += `${days} ${t('orders.detail.days', '天')}   `
    if (hours > 0 || days > 0) result += `${hours} ${t('orders.detail.hours', '小时')}`
    result += ` ${minutes} ${t('orders.detail.minutes', '分钟')}`
    return result
}

// 获取订单步骤 - 与 TrackOrdersView 保持一致
const getOrderSteps = (orderStatus: string | number) => {
    const steps = [
        { title: 'orders.status.pending', icon: 'credit-card', status: 1 },
        { title: 'orders.status.paid', icon: 'check-circle', status: 2 },
        { title: 'orders.status.purchasing', icon: 'shopping-cart', status: 4 },
        { title: 'orders.status.shipped', icon: 'truck', status: 7 },
        { title: 'orders.status.inWarehouse', icon: 'home', status: 10 },
        { title: 'orders.status.completed', icon: 'trophy', status: 13 },
    ]

    // 处理字符串状态
    let currentStatus = orderStatus
    if (typeof orderStatus === 'string') {
        const statusMap: { [key: string]: number } = {
            'PAYMENT_PENDING': 1,
            'PAID': 2,
            'PURCHASING': 4,
            'SHIPPED': 7,
            'IN_WAREHOUSE': 10,
            'COMPLETED': 13,
            'CANCELLED': 11,
        }
        currentStatus = statusMap[orderStatus] || 1
    }

    return steps.map(step => {
        const numStatus = Number(currentStatus)
        
        // 特殊处理采购中状态（3、4、5都对应采购中步骤）
        if (step.status === 4) { // 采购中步骤
            return {
                ...step,
                completed: numStatus > 5, // 状态大于5才算完成采购中
                current: numStatus >= 3 && numStatus <= 5, // 状态3、4、5都是当前采购中状态
            }
        }

        // 特殊处理已发货状态（7、8、9都对应已发货步骤）
        if (step.status === 7) { // 已发货步骤
            return {
                ...step,
                completed: numStatus > 9, // 状态大于9才算完成已发货
                current: numStatus >= 7 && numStatus <= 9, // 状态7、8、9都是当前已发货状态
            }
        }
        
        return {
            ...step,
            completed: numStatus > step.status,
            current: numStatus === step.status,
        }
    })
}

// 获取进度百分比 - 与 TrackOrdersView 保持一致
const getProgressPercentage = (orderStatus: string | number) => {
    let status = orderStatus
    if (typeof orderStatus === 'string') {
        const statusMap: { [key: string]: number } = {
            'PAYMENT_PENDING': 1,
            'PAID': 2,
            'PURCHASING': 4,
            'SHIPPED': 7,
            'IN_WAREHOUSE': 10,
            'COMPLETED': 13,
            'CANCELLED': 11,
        }
        status = statusMap[orderStatus] || 1
    }
    
    const progressMap: { [key: number]: number } = {
        1: 10,
        2: 20,
        3: 30,  // 采购中
        4: 40,  // 采购中
        5: 50,  // 采购中
        7: 60,
        10: 80,
        13: 100,
        11: 0
    }
    
    return progressMap[Number(status)] || 0
}

const getIconComponent = (iconName: string) => {
    return iconMap[iconName] || null
}

// 计算属性
const merchandiseAmountUsd = computed(() => {
    return orderDetail.value?.priceInfo?.subtotalUsd || 0
})

const merchandiseAmountCny = computed(() => {
    return orderDetail.value?.priceInfo?.subtotal || 0
})

const shippingFeeUsd = computed(() => {
    return orderDetail.value?.priceInfo?.shippingFeeUsd || 0
})

const shippingFeeCny = computed(() => {
    return orderDetail.value?.priceInfo?.shippingFee || 0
})

const totalAmountUsd = computed(() => {
    return orderDetail.value?.priceInfo?.totalAmountUsd || 0
})

const totalAmountCny = computed(() => {
    return orderDetail.value?.priceInfo?.totalAmount || 0
})

// 根据当前选择的货币获取相应金额
const getCurrentMerchandiseAmount = () => {
    if (currencyStore.currentCurrency === 'CNY') {
        return merchandiseAmountCny.value
    } else {
        return merchandiseAmountUsd.value
    }
}

const getCurrentShippingFee = () => {
    if (currencyStore.currentCurrency === 'CNY') {
        return shippingFeeCny.value
    } else {
        return shippingFeeUsd.value
    }
}

const getCurrentTotalAmount = () => {
    if (currencyStore.currentCurrency === 'CNY') {
        return totalAmountCny.value
    } else {
        return totalAmountUsd.value
    }
}

// 生命周期
onMounted(() => {
    loadOrderDetail()
})
</script>

<style lang="scss" scoped>
.order-detail-container {
    background-color: #f9fafb;
    min-height: 100vh;
    padding: 1rem 400px;

    @media (min-width: 768px) {
        padding: 2rem 400px;
    }
}

.page-header {
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: #111827;

    .header-content {
        display: flex;
        align-items: center;
        gap: 1.5rem;
    }

    .back-button {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        background: #f3f4f6;
        color: #374151;
        cursor: pointer;
        transition: all 0.2s;

        &:hover {
            background: #e5e7eb;
            color: #111827;
        }
    }

    .header-info {
        flex: 1;

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: #111827;
            margin: 0;
        }

        .page-subtitle {
            color: #6b7280;
            font-size: 1rem;
            margin: 0.5rem 0 0 0;
        }
    }
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f4f6;
        border-top: 4px solid #f97316;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
    }

    p {
        color: #6b7280;
        font-size: 1rem;
    }
}

.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);

    .error-icon {
        color: #ef4444;
        margin-bottom: 1rem;
    }

    h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #111827;
        margin: 0 0 0.5rem 0;
    }

    p {
        color: #6b7280;
        margin: 0 0 1.5rem 0;
        text-align: center;
    }
}

.order-detail-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.info-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .card-header {
        padding: 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: #52018D;
        color: white;

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: white;
            margin: 0;
        }

        .items-count {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
        }
    }

    .card-content {
        padding: 1.5rem;
    }
}

.order-status-progress {
    .order-progress-steps {
        .step-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            border: 3px solid;
            transition: all 0.3s ease-in-out;
            position: relative;
            z-index: 10;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

            &.step-completed {
                background: linear-gradient(135deg, #10b981, #059669);
                border-color: #10b981;
                color: white;
                box-shadow: 0 6px 12px rgba(16, 185, 129, 0.3);
            }

            &.step-current {
                background: linear-gradient(135deg, #f97316, #ea580c);
                border-color: #f97316;
                color: white;
                box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4);

                &.step-active {
                    animation: pulse-orange 2s infinite;
                }
            }

            &.step-pending {
                background-color: white;
                border-color: #e2e8f0;
                color: #94a3b8;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            }

            .step-icon-svg {
                transition: transform 0.2s ease-in-out;
            }
        }

        .step-title {
            margin-top: 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
        }
    }
}

// 动画效果
@keyframes pulse-orange {
    0%,
    100% {
        box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(249, 115, 22, 0.15);
        transform: scale(1.05);
    }
}

.current-status-info {
    .status-header {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 1.5rem;
        padding: 0.75rem 1rem;

        .status-title-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            width: 100%;
        }

        .status-label {
            font-weight: 600;
            color: #1e293b;
            font-size: 1.125rem;
            flex-shrink: 0;
        }

        .countdown-text {
            display: inline-flex;
            align-items: center;
            font-size: 0.875rem;
            color: #ffffff;
            background: linear-gradient(135deg, #ff9800, #f57c00);
            padding: 0.75rem 1.25rem;
            border-radius: 0.5rem;
            font-weight: 500;
            white-space: nowrap;
        }
        
        @media (max-width: 768px) {
            gap: 0.75rem;
            
            .status-title-row {
                flex-direction: row !important; 
                align-items: center !important; 
                gap: 0.5rem !important;
                flex-wrap: wrap !important;
            }
            
            .status-label {
                font-size: 0.875rem !important;
                flex-shrink: 0 !important;
            }
            
            .el-tag {
                flex-shrink: 0 !important;
                white-space: nowrap !important;
            }
            
            .countdown-text {
                font-size: 0.75rem;
                padding: 0.5rem 0.75rem;
                width: 100% !important;
                flex-basis: 100% !important;
                justify-content: center !important;
                margin-top: 0.5rem !important;
            }
        }
    }

    .action-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        padding: 0.5rem;

        .action-item {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 1rem;
            padding: 0.75rem 1rem;
            transition: all 0.2s ease;
            
            &:hover {
                background-color: #f9fafb;
            }

            .action-text {
                font-size: 0.875rem;
                color: #4b5563;
                font-weight: 500;
                flex-shrink: 0;
            }

            .el-button {
                flex-shrink: 0;
                min-width: 80px;
            }
        }
    }
}

.custom-pay-button {
    background-color: #52018D;
    border-color: #52018D;
    padding: 0.25rem 1rem;
    
    &:hover {
        background-color: #6a02b3;
        border-color: #6a02b3;
    }
    
    &:active {
        background-color: #42016a;
        border-color: #42016a;
    }
}

.order-basic-info {
    // 新的订单信息网格布局
    .order-info-grid {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 2rem;
        
        .info-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            align-items: center;
            
            // 占满整行的样式
            &.full-width {
                grid-template-columns: 1fr;
            }
            
            .info-item {
                 display: flex;
                 align-items: center;
                 gap: 0.75rem;
                 padding: 1rem;
                 background-color: transparent;
                 border-radius: 0.5rem;
                 transition: all 0.3s ease;
                 
                 &:hover {
                     background-color: #f8fafc;
                 }
                
                // 占满整行的信息项
                &.full-width {
                    grid-column: 1 / -1;
                }
                
                .label {
                    font-weight: 600;
                    color: #475569;
                    min-width: 100px;
                    font-size: 0.875rem;
                    flex-shrink: 0;
                }
                
                .value {
                    color: #1e293b;
                    font-weight: 500;
                    font-size: 0.875rem;
                    flex: 1;
                    
                    // 订单号特殊样式
                    &.order-no {
                        font-family: 'Courier New', monospace;
                        padding: 0.25rem 0.5rem;
                        border-radius: 0.25rem;
                        color: #52018D;
                        font-weight: 600;
                        letter-spacing: 0.5px;
                        font-size: 16px;
                    }
                    
                    // 地址特殊样式
                    &.address {
                        line-height: 1.5;
                        word-break: break-all;
                    }
                }
            }
        }
    }
    
    // 保留原有的info-grid样式作为备用
    .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1rem;
        margin-bottom: 2rem;

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .label {
                font-weight: 500;
                color: #374151;
                min-width: 80px;
            }

            .value {
                color: #111827;
                font-weight: 500;
            }
        }
    }

    .items-section {
        border-top: 1px solid #e5e7eb;
        padding-top: 1.5rem;

        .items-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;

            .items-title {
                font-size: 1.125rem;
                font-weight: 600;
                color: #111827;
                margin: 0;
            }

            .items-count {
                font-size: 0.875rem;
                color: #6b7280;
            }
        }

        /* 商品卡片容器样式 - 使用主色调#52018d */
        .items-cards {
            .cards-container {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }
        }

        /* 商品卡片样式 - 主色调#52018d */
        .product-card {
            display: flex;
            align-items: flex-start;
            background: #ffffff;
            border: 2px solid #f8fafc;
            border-radius: 20px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(82, 1, 141, 0.08);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            margin-bottom: 12px;
            overflow: hidden;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, #52018d, #7c3aed, #a855f7);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:hover {
                border-color: #52018d;
                box-shadow: 0 8px 25px rgba(82, 1, 141, 0.15);
                transform: translateY(-2px);
                
                &::before {
                    opacity: 1;
                }
            }

            &:last-child {
                margin-bottom: 0;
            }
        }

        /* 商品列表样式已使用 Tailwind CSS 类替代 */

        /* 空商品状态 */
        .empty-items {
            text-align: center;
            padding: 2rem;
            color: #6b7280;
        }

        /* 响应式设计已通过 Tailwind CSS 类实现 */

        .price-breakdown {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            margin-top: 2rem;
            border-top: 1px solid #e5e7eb;
            padding-top: 1.5rem;

            .price-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.75rem 0;

                &.total {
                    border-top: 2px solid #e5e7eb;
                    padding-top: 1rem;
                    margin-top: 0.5rem;

                    .price-label {
                        font-weight: 600;
                        font-size: 1.125rem;
                    }

                    .total-value {
                        font-weight: 700;
                        font-size: 1.25rem;
                        color: #f97316;
                    }
                }

                .price-label {
                    color: #374151;
                    font-weight: 500;
                }

                .price-value {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    font-weight: 500;
                    color: #111827;

                    .price-cny {
                        font-size: 0.875rem;
                        color: #6b7280;
                        font-weight: normal;
                    }
                }
            }
        }

        .order-actions {
            margin-top: 1rem;
            padding-top: 0.5rem;
            .actions-grid {
                display: flex;
                flex-wrap: wrap;
                gap: 1rem;
                justify-content: center;
                .el-button {
                    flex: 1;
                    min-width: 150px;
                }
            }
        }
    }
}

:deep(.table-row) {
    font-size: 4rem;
    padding: 3rem 4rem;
    min-height: 200px;
    .product-image {
        width: 200px;
        height: 200px;
        display: flex;
        justify-content: center;
        align-items: center;
        
        :deep(.el-image__inner) {
            object-fit: cover;
            width: 100%;
            height: 100%;
        }
    }
    .product-title {
        font-size: 1.2rem;
    }
    .quantity-value,
    .status-text,
    .unit-price {
        font-size: 1rem;
        font-weight: 500;
    }
    .spec-item {
        font-size: 0.8rem;
    }
}

// 已取消订单样式
    .cancelled-order-display {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 1.5rem 1rem;
        background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 50%, #fecaca 100%);
        border-radius: 1rem;
        border: 2px solid #fca5a5;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
        position: relative;
        overflow: hidden;
        margin-bottom: 1rem;
        
        // 背景装饰效果
        &::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(220, 38, 38, 0.05) 0%, transparent 70%);
            animation: pulse-bg 3s ease-in-out infinite;
        }
        
        .cancelled-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            margin-right: 1rem;
            box-shadow: 0 6px 16px rgba(220, 38, 38, 0.3);
            position: relative;
            z-index: 1;
            
            // 图标动画效果
            animation: shake 0.5s ease-in-out;
            
            svg {
                width: 1.25rem;
                height: 1.25rem;
            }
        }

        .cancelled-text {
            flex: 1;
            position: relative;
            z-index: 1;
            
            .cancelled-title {
                color: #dc2626;
                font-size: 1.25rem;
                font-weight: 700;
                margin: 0 0 0.25rem 0;
                text-shadow: 0 1px 2px rgba(220, 38, 38, 0.1);
                letter-spacing: 0.5px;
            }
            
            .cancelled-description {
                color: #991b1b;
                font-size: 0.875rem;
                font-weight: 500;
                margin: 0;
                opacity: 0.9;
            }
        }
        
        // 响应式设计
        @media (max-width: 768px) {
            flex-direction: row;
            text-align: left;
            padding: 1rem;
            
            .cancelled-icon {
                margin-right: 0.75rem;
                margin-bottom: 0;
                width: 2.5rem;
                height: 2.5rem;
                
                svg {
                    width: 1rem;
                    height: 1rem;
                }
            }
            
            .cancelled-text {
                text-align: left;
                
                .cancelled-title {
                    font-size: 1rem;
                    text-align: left;
                }
                
                .cancelled-description {
                    font-size: 0.75rem;
                    text-align: left;
                }
            }
        }
    }
    
    // 动画效果
    @keyframes pulse-bg {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            opacity: 0.3;
        }
        50% {
            transform: scale(1.1) rotate(180deg);
            opacity: 0.1;
        }
    }
    
    @keyframes shake {
        0%, 100% {
            transform: translateX(0);
        }
        25% {
            transform: translateX(-2px);
        }
        75% {
            transform: translateX(2px);
        }
    }

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

// 取消订单对话框样式
.cancel-reason-form {
    .form-item {
        margin-bottom: 15px;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 500;
        color: #1e293b;
        margin-bottom: 0.5rem;
    }
}

// 取消订单对话框样式
:deep(.el-dialog) {
    border-radius: 1rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    
    .el-dialog__header {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-bottom: 1px solid #e2e8f0;
        padding: 1rem 1.5rem;
        border-radius: 1rem 1rem 0 0;
        
        .el-dialog__title {
            font-weight: 600;
            color: #1e293b;
        }
    }
    
    .el-dialog__body {
        padding: 1.5rem;
    }
    
    .el-dialog__footer {
        background: linear-gradient(135deg, #f8fafc, #e2e8f0);
        border-top: 1px solid #e2e8f0;
        padding: 1rem 1.5rem;
        border-radius: 0 0 1rem 1rem;
        
        .dialog-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.75rem;
        }
    }
}

.memo-dialog-body {
  .memo-label-row {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    span:first-child {
      width: 80px;
      color: #333;
      font-weight: 500;
    }
    .el-input, .el-textarea {
      flex: 1;
    }
    .memo-color-group {
      margin-left: 8px;
      .memo-color-icon {
        display: inline-block;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        border: 2px solid #eee;
        margin: 0 4px;
        &.orange { background: #ff9800; }
        &.blue { background: #2196f3; }
        &.green { background: #4caf50; }
        &.yellow { background: #ffc107; }
      }
    }
  }
}
:deep(.large-memo-dialog) {
  min-width: 480px;
  max-width: 600px;
}
:deep(.large-memo-dialog .el-textarea__inner) {
  min-height: 120px;
  font-size: 16px;
}
:deep(.large-memo-dialog .el-input__inner) {
  min-width: 400px;
  font-size: 16px;
}
.delivery-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 1rem;
  .info-delivery {
    flex: 1;
    min-width: 0;
    .label {
      font-weight: 500;
      color: #374151;
      min-width: 80px;
    }
    .value {
      color: #111827;
      font-weight: 500;
      word-break: break-all;
    }
  }
}
@media (max-width: 768px) {
  .order-detail-container {
    padding: 0.5rem !important;
  }
  .page-header {
    padding: 1rem !important;
    margin-bottom: 1rem !important;
    .header-content {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.75rem;
    }
    .page-title {
      font-size: 1.2rem !important;
    }
    .page-subtitle {
      font-size: 0.85rem !important;
    }
  }
  .info-card .card-header,
  .info-card .card-content {
    padding: 1rem !important;
  }
  .order-status-progress .order-progress-steps {
    .flex {
      gap: 0.1rem !important;
    }
    .step-icon {
      width: 2rem !important;
      height: 2rem !important;
      min-width: 2rem !important;
      min-height: 2rem !important;
      font-size: 1rem !important;
    }
    .step-title {
      font-size: 0.35rem !important;
      margin-top: 0.25rem !important;
    }
  }
  
  .cancelled-order-display {
    justify-content: flex-start !important;
    text-align: left !important;
    
    .cancelled-icon {
      margin-right: 0.75rem !important;
    }
    
    .cancelled-text {
      text-align: left !important;
      
      .cancelled-title {
        font-size: 1rem !important;
        text-align: left !important;
      }
      
      .cancelled-description {
        font-size: 0.8rem !important;
        text-align: left !important;
      }
    }
  }
  .countdown-text {
    font-size: 0.75rem !important;
    padding: 0.2rem 0.5rem !important;
    border-radius: 0.25rem !important;
    white-space: normal !important; // 允许换行
    text-align: center !important; // 文本居中
    line-height: 1.3 !important; // 增加行高
    max-width: 100% !important; // 限制最大宽度
  }
  .order-basic-info {
    .order-info-grid {
      gap: 1rem !important;
      
      .info-row {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
        
        .info-item {
          padding: 0.75rem !important;
          
          .label {
            min-width: 80px !important;
            font-size: 0.8rem !important;
          }
          
          .value {
            font-size: 0.8rem !important;
            
            &.order-no {
              padding: 0.2rem 0.4rem !important;
              font-size: 0.75rem !important;
            }
          }
        }
      }
    }
    
    .info-grid {
      grid-template-columns: 1fr !important;
      gap: 0.5rem !important;
      margin-bottom: 1rem !important;
    }
  }
  .delivery-row {
    flex-direction: column;
    gap: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .order-basic-info .items-section {
    padding-top: 1rem !important;
    .items-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.25rem;
      margin-bottom: 0.5rem !important;
      .items-title {
        font-size: 1rem !important;
      }
    }
    .items-table {
      .table-header {
        display: none !important;
      }
      .table-body {
        gap: 0.5rem !important;
        .table-row {
          grid-template-columns: 1fr !important;
          gap: 0.5rem !important;
          padding: 0.5rem !important;
          .table-cell {
            flex-direction: row !important;
            align-items: flex-start !important;
            .product-info {
              flex-direction: row !important;
              gap: 1rem !important;
              .product-image {
                width: 80px !important;
                height: 80px !important;
              }
              .product-title {
                font-size: 0.95rem !important;
              }
            }
            .quantity-value,
            .status-text,
            .unit-price {
              font-size: 0.9rem !important;
            }
          }
        }
      }
    }
    .price-breakdown {
      margin-top: 1rem !important;
      padding-top: 1rem !important;
      gap: 0.5rem !important;
      .price-item {
        flex-direction: column !important;
        align-items: flex-start !important;
        padding: 0.5rem 0 !important;
        .price-label {
          font-size: 0.95rem !important;
        }
        .price-value {
          font-size: 0.95rem !important;
        }
      }
      .price-item.total .price-label {
        font-size: 1rem !important;
      }
      .price-item.total .total-value {
        font-size: 1.1rem !important;
      }
    }
    .order-actions {
      margin-top: 0.5rem !important;
      .actions-grid {
        flex-direction: column !important;
        gap: 0.5rem !important;
        align-items: center !important; // 新增：移动端按钮居中
        .el-button {
          min-width: 0 !important;
          width: 100% !important;
        }
      }
    }
  }
  .loading-container,
  .error-container {
    padding: 2rem 0.5rem !important;
  }
  // 对话框适配
  :deep(.el-dialog) {
    width: 95vw !important;
    min-width: unset !important;
    max-width: 98vw !important;
    .el-dialog__body {
      padding: 1rem !important;
    }
    .el-dialog__footer {
      padding: 0.5rem 1rem !important;
    }
  }
}
// 移动端商品信息适配
@media (max-width: 768px) {
  .order-basic-info .items-section {
    .items-table {
      .table-body {
        .table-row {
          display: block !important;
          padding: 0.75rem 0.5rem !important;
          margin-bottom: 0.75rem !important;
          .table-cell {
            display: block !important;
            width: 100% !important;
            &.merchandise {
              .product-info {
                flex-direction: column !important;
                align-items: center !important;
                .product-image {
                  width: 100px !important;
                  height: 100px !important;
                  min-width: 100px !important;
                  min-height: 100px !important;
                  margin-bottom: 0.25rem !important;
                }
                .product-details {
                  width: 100%;
                  text-align: left !important;
                  .product-title {
                    font-size: 0.95rem !important;
                    margin-bottom: 0.25rem !important;
                    text-align: left !important;
                  }
                  .product-specs {
                    font-size: 0.75rem !important;
                    text-align: left !important;
                  }
                }
              }
            }
            &.unit-price, &.quantity, &.status {
              margin-top: 0.25rem;
              text-align: left !important; // 改为左对齐
              .unit-price, .quantity-value, .status-text {
                text-align: left !important; // 改为左对齐
                width: 100%;
                display: block;
              }
            }
          }
        }
      }
    }
    // 移动端按钮适配
    .order-actions {
      margin-top: 0.5rem !important;
      .actions-grid {
        flex-direction: row !important;
        gap: 0.75rem !important;
        justify-content: center !important;
        align-items: center !important;
        .el-button {
          min-width: 0 !important;
          width: 48% !important;
          max-width: 200px !important;
          display: inline-block !important;
        }
      }
    }
  }
}
/* 商品信息样式 */
.product-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image-wrapper {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  margin-right: 1rem;
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  object-fit: cover;
}

.product-info-wrapper {
  flex: 1;
  min-width: 0;
}

.product-title {
  font-size: 1rem;
  font-weight: 800;
  color: #111827;
  margin-bottom: 0.25rem;
  line-height: 1.25;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-specs {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  border-radius: 0.375rem;
  display: inline-block;
  width: fit-content;
}

.product-sku {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-bottom: 0.5rem;
}

.quantity-price-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 0.5rem;
}

.quantity-info {
  font-size: 0.875rem;
  font-weight: 800;
  color: #111827;
}

.quantity-label {
  margin-right: 0.25rem;
}

.quantity-value {
  font-weight: 500;
  color: #374151;
}

.separator {
  color: #9ca3af;
  font-size: 0.875rem;
  margin: 0 0.25rem;
}

.price-info {
  font-size: 0.875rem;
  font-weight: 800;
  color: #111827;

  .price-label {
    margin-right: 0.25rem;
  }

  .price-value {
    font-weight: 500;
    color: #374151;
  }

}

.logistics-status {
  margin-top: 0.5rem;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  background-color: #fef3c7;
  color: #b45309;
}

/* 添加文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
