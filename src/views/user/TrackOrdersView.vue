<template>
    <div class="orders-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">{{ t('orders.title', '我的订单') }}</h1>
            <p class="page-subtitle">{{ t('orders.subtitle', '查看和管理您的订单') }}</p>
        </div>

        <!-- 筛选和订单管理区域 -->
        <div class="filters-and-orders-section">
            <!-- 批量操作区域 -->
            <div class="batch-operations-row" v-if="isAnyOrderSelected">
                <div class="batch-operations">
                    <el-button type="danger" plain @click="handleBatchCancel">
                        <Trash2 :size="16" class="mr-2" />
                        {{ t('orders.actions.batchCancel', '批量取消') }}
                    </el-button>
                </div>
            </div>

            <!-- 主要筛选区域 - 整合为一行 -->
            <div class="unified-filters-row">
                <div class="filters-content">
                    <!-- 搜索框 -->
                    <div class="search-group">
                        <div class="search-input-wrapper">
                            <input
                                v-model="searchKeyword"
                                type="text"
                                :placeholder="t('orders.search.placeholder', '搜索订单号、商品名称、收货人姓名或SKU ID')"
                                class="search-input"
                                @keyup.enter="searchOrders"
                            />
                            <button v-if="searchKeyword" @click="clearSearchConditions" class="clear-search-btn" :title="t('orders.search.clear', '清空搜索条件')">
                                ×
                            </button>
                        </div>
                        <button @click="searchOrders" class="search-btn">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </div>

                    <!-- 状态筛选 -->
                    <div class="status-group">
                        <label class="filter-label">{{ t('orders.orderStatus', '订单状态') }}</label>
                        <el-select
                            v-model="currentStatus"
                            placeholder="All"
                            @change="setStatusFilter"
                            class="status-select"
                            size="default"
                        >
                            <el-option
                                v-for="statusFilter in statusFilters"
                                :key="statusFilter.value"
                                :label="statusFilter.label + (statusFilter.count !== undefined ? ' (' + statusFilter.count + ')' : '')"
                                :value="statusFilter.value"
                            />
                        </el-select>
                    </div>

                    <!-- 时间筛选 -->
                    <div class="time-group">
                        <label class="filter-label">{{ t('orders.timeRange', '时间范围') }}</label>
                        <div class="time-controls">
                            <!-- 快捷时间按钮 -->
                            <div class="quick-buttons">
                                <button 
                                    v-for="range in quickDateRanges" 
                                    :key="range.key"
                                    @click="setQuickDateRange(range.key)"
                                    class="quick-btn"
                                    :class="{ 'active': currentQuickRange === range.key }"
                                >
                                    {{ range.label }}
                                </button>
                            </div>
                            <!-- 自定义日期 -->
                            <div class="date-range">
                                <el-date-picker
                                    v-model="startDate"
                                    type="date"
                                    :placeholder="t('orders.dateRange.start', '开始日期')"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    @change="handleDateChange"
                                    clearable
                                    class="date-picker"
                                    size="default"
                                />
                                <span class="date-separator">{{ t('orders.dateRange.separator', '至') }}</span>
                                <el-date-picker
                                    v-model="endDate"
                                    type="date"
                                    :placeholder="t('orders.dateRange.end', '结束日期')"
                                    format="YYYY-MM-DD"
                                    value-format="YYYY-MM-DD"
                                    @change="handleDateChange"
                                    clearable
                                    class="date-picker"
                                    size="default"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单表格 -->
            <div class="orders-table-container">
            <el-table 
                ref="ordersTableRef"
                :data="orders" 
                style="width: 100%" 
                class="orders-table"
                :row-key="(row) => row.orderNo"
                @selection-change="handleSelectionChange"
                stripe
            >
                <!-- 订单信息列 -->
                <el-table-column prop="orderNo" min-width="800">
                    <template #default="{ row }">
                        <div class="order-detail-container">
                            
                            <!-- 订单头部信息 -->
                            <div class="order-header">
                                <div class="order-header-left" @click="viewOrderDetail(row.orderNo)" style="cursor: pointer;">
                                    <!-- 选择框放在订单号旁边 -->
                                    <el-checkbox 
                                        v-if="row.canCancel && Number(row.orderStatus) !== 11"
                                        :model-value="selectedOrders.includes(row.orderNo)"
                                        @change="(checked) => handleSingleOrderSelection(row.orderNo, checked)"
                                        @click.stop
                                        class="order-checkbox"
                                    />
                                    <div class="order-info">
                                        <span class="order-label">{{ t('orders.orderNo', 'Order No') }}：</span>
                                        <span 
                                            class="order-number clickable-order-number" 
                                            :title="t('orders.clickToViewDetail', '点击查看订单详情')"
                                        >
                                            {{ row.orderNo }}
                                        </span>
                                        <!-- 复制订单号图标 -->
                                        <button 
                                            class="copy-order-btn"
                                            @click.stop="copyOrderNo(row.orderNo)"
                                            :title="t('orders.copyOrderHint', '点击复制订单号')"
                                        >
                                            <svg class="copy-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                                <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
                                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                                <div class="order-header-center">
                                    <span class="order-label">{{ t('orders.orderTime', 'Order Time') }}：</span>
                                    <span class="order-time">{{ formatDate(row.createTime) }}</span>
                                </div>
                                <div class="order-header-right">
                                    <!-- 展开/收起图标 -->
                                    <el-icon 
                                        class="toggle-icon" 
                                        :class="{ 'expanded': isOrderExpanded(row.orderNo) }"
                                        @click="toggleOrderExpansion(row.orderNo)"
                                        :title="isOrderExpanded(row.orderNo) ? t('orders.collapse', '收起') : t('orders.expand', '展开')"
                                    >
                                        <ArrowDown />
                                    </el-icon>
                                </div>
                            </div>

                            <!-- 商品详细信息表格 - 根据展开状态显示 -->
                            <div v-show="isOrderExpanded(row.orderNo)" class="order-products-table">
                                <div class="table-header">
                                    <div class="col-product-image">{{ t('orders.tableHeaders.productImage', 'Product Image') }}</div>
                                    <div class="col-product-name">{{ t('orders.tableHeaders.productName', 'Product Name') }}</div>
                                    <div class="col-specification">{{ t('orders.tableHeaders.specification', 'Specification') }}</div>
                                    <div class="col-quantity">{{ t('orders.tableHeaders.quantity', 'Quantity') }}</div>
                                    <div class="col-unit-price">{{ t('orders.tableHeaders.unitPrice', 'Unit Price') }}</div>
                                    <div class="col-sku">{{ t('orders.tableHeaders.sku', 'SKU') }}</div>
                                    <div class="col-order-status">{{ t('orders.tableHeaders.orderStatus', 'Order Status') }}</div>
                                    <div class="col-total">{{ t('orders.tableHeaders.subTotal', 'Total') }}</div>
                                </div>
                                
                                <!-- 商品行 - 显示订单中的所有商品 -->
                                <div v-if="getOrderProducts(row.orderNo)?.length > 0">
                                    <div v-for="(product, index) in getOrderProducts(row.orderNo)" :key="product.skuId || index" class="table-row">
                                        <div class="col-product-image">
                                            <img 
                                                :src="product.productImage"
                                                :alt="getProductName(product)"
                                                @error="handleImageError"
                                                @click="previewImage(product.productImage, getProductName(product))"
                                                class="product-image-small clickable-image"
                                                :title="t('orders.clickToPreview', '点击查看大图')"
                                            />
                                        </div>
                                        <div class="col-product-name">
                                            <div class="product-name">{{ getProductName(product) }}</div>
                                        </div>
                                        <div class="col-specification">
                                            <div class="specification-text">{{ getProductSpecs(product) || '-' }}</div>
                                        </div>
                                        <div class="col-quantity">
                                            <span class="quantity-text">{{ product.orderedQuantity || product.quantity || 0 }}</span>
                                        </div>
                                        <div class="col-unit-price">
                                            <span class="price-text">{{ getProductUnitPrice(product) }}</span>
                                        </div>
                                        <div class="col-sku">
                                            <span class="sku-text clickable-sku" @click="copySkuId(product.skuId)" :title="t('orders.copySkuHint', '点击复制SKU')">{{ product.skuId || '-' }}</span>
                                        </div>
                                        <div class="col-order-status">
                                            <el-tag :type="getStatusTagType(row.orderStatus)" size="large">
                                                {{ getOrderStatusText(row.orderStatus) }}
                                            </el-tag>
                                        </div>
                                        <div class="col-total">
                                            <span class="total-text">{{ currencyStore.formatPriceWithSymbol(getProductTotalPrice(product)) }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div v-else class="table-row loading-row">
                                    <div class="loading-text">{{ t('orders.loadingProducts', '正在加载商品信息...') }}</div>
                                </div>
                            </div>

                            <!-- 订单汇总信息和操作按钮 - 始终显示 -->
                            <div class="order-footer">
                                <div class="order-summary">
                                    <div class="summary-row">
                                        <span class="summary-label">{{ t('orders.totalPrice', 'Total Price') }}</span>
                                        <span class="summary-value total-price">{{ currencyStore.formatPriceWithSymbol(getCurrentOrderAmount(row)) }}</span>
                                    </div>
                                </div>

                                <!-- 操作按钮 -->
                                <div class="order-actions">
                                <el-button
                                    v-if="row.canCancel"
                                    size="small"
                                    type="danger"
                                    plain
                                    @click="cancelOrder(row.orderNo)"
                                >
                                    {{ t('orders.actions.cancel', '取消订单') }}
                                </el-button>
                                <el-button
                                    v-if="row.canPay"
                                    size="small"
                                    type="success"
                                    @click="payOrder(row.orderNo)"
                                >
                                    {{ t('orders.actions.pay', '支付') }}
                                </el-button>
                                <el-button
                                    v-if="row.orderStatus === 2"
                                    size="small"
                                    type="danger"
                                    @click="refundOrder(row.orderNo)"
                                >
                                    {{ t('orders.actions.refund', '申请退款') }}
                                </el-button>
                                </div>
                            </div>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            </div>

            <!-- 空状态 -->
            <div v-if="!loading && orders.length === 0" class="empty-state">
                <div class="empty-icon">
                    <svg class="w-16 h-16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="1"
                            d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                    </svg>
                </div>
                <h3>{{ t('orders.empty.title', '暂无订单') }}</h3>
                <p>{{ t('orders.empty.description', '您还没有任何订单，去逛逛商城吧！') }}</p>
                <router-link to="/products" class="btn btn-primary">
                    {{ t('orders.empty.browsing', '浏览商品') }}
                </router-link>
            </div>

            <!-- 分页器 -->
            <div v-if="pagination.totalElements > 0" class="pagination-container">
                <el-pagination
                    v-model:current-page="pagination.page"
                    :page-size="pagination.size"
                    :total="pagination.totalElements"
                    layout="total, prev, pager, next, jumper"
                    @current-change="changePage"
                    background
                />
            </div>
        </div>



        <!-- 取消订单对话框 -->
        <el-dialog
            v-model="cancelDialogVisible"
            :title="t('orders.cancel.title', '确认取消订单')"
            width="400px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div class="cancel-reason-form">
                <div class="form-item">
                    <label class="form-label">{{ t('orders.cancel.reason', '取消原因') }}</label>
                    <el-select 
                        v-model="selectedCancelReason" 
                        :placeholder="t('orders.cancel.reason', '请选择取消原因')"
                        style="width: 100%"
                    >
                        <el-option
                            v-for="reason in cancelReasons"
                            :key="reason"
                            :label="reason"
                            :value="reason"
                        />
                    </el-select>
                </div>
                <div v-if="selectedCancelReason === t('orders.cancel.reasons.other', '其他')" class="form-item">
                    <label class="form-label">{{ t('orders.cancel.otherReason', '其他原因') }}</label>
                    <el-input
                        v-model="otherCancelReason"
                        type="textarea"
                        :rows="3"
                        :placeholder="t('orders.cancel.placeholder', '请详细说明取消原因')"
                    />
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="cancelDialogVisible = false">{{ t('common.cancel', '取消') }}</el-button>
                    <el-button type="primary" :loading="actionLoading" @click="handleCancelConfirm">{{ t('common.confirm', '确认') }}</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 图片预览对话框 -->
        <el-dialog
            v-model="imagePreviewVisible"
            :title="previewImageTitle"
            width="80%"
            :close-on-click-modal="true"
            :close-on-press-escape="true"
            class="image-preview-dialog"
        >
            <div class="image-preview-container">
                <img 
                    :src="previewImageUrl"
                    :alt="previewImageTitle"
                    class="preview-image"
                    @error="handlePreviewImageError"
                />
            </div>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
    import { ref, onMounted, computed, watch, nextTick } from 'vue'
    import { useI18n } from 'vue-i18n'
    import { useRouter } from 'vue-router'
    import { useCurrencyStore } from '@/stores/currency'
    import { orderApi, type OrderListRequest, type UserPurchaseOrderListVO } from '@/api/modules/order'
    import { ElMessage, ElMessageBox, ElDialog, ElSelect, ElOption, ElInput, ElButton } from 'element-plus'
    import {
        Trash2,
        ArrowDown,
    } from 'lucide-vue-next'

    const { t, locale } = useI18n()
    const router = useRouter()
    const currencyStore = useCurrencyStore()

    // 响应式数据
    const loading = ref(false)
    const actionLoading = ref(false)
    const orders = ref<UserPurchaseOrderListVO[]>([])
    const searchKeyword = ref('')
    const currentStatus = ref<number | null>(null) // 默认为All状态
    const selectedOrders = ref<string[]>([])
    const startDate = ref<string | null>(null)
    const endDate = ref<string | null>(null)
    const currentQuickRange = ref<string>('7days') // 默认选择7天
    
    // 表格引用
    const ordersTableRef = ref()
    
    // 订单商品数据管理
    const orderProductsData = ref(new Map<string, any>())
    
    // 商品信息展开/收起状态管理
    const expandedOrders = ref(new Set<string>())
    
    // 图片预览相关
    const imagePreviewVisible = ref(false)
    const previewImageUrl = ref('')
    const previewImageTitle = ref('')
    
    // 快捷时间范围选项
    const quickDateRanges = computed(() => [
        { key: '7days', label: t('orders.dateFilter.last7days', '七天') },
        { key: '15days', label: t('orders.dateFilter.last15days', '十五天') },
        { key: '30days', label: t('orders.dateFilter.last30days', '三十天') },
    ])



    // 分页数据
    const pagination = ref({
        page: 1,
        size: 10,
        totalElements: 0,
        totalPages: 1,
    })

    // 订单状态筛选选项 (改为计算属性以支持i18n动态更新)
    const statusFilters = computed(() => [
        { label: t('orders.status.all'), value: null, count: undefined },
        { label: t('orders.status.pending'), value: 1, count: undefined },
        { label: t('orders.status.paid'), value: 2, count: undefined },
        { label: t('orders.status.purchasing'), value: 4, count: undefined },
        { label: t('orders.status.shipped'), value: 7, count: undefined },
        { label: t('orders.status.inWarehouse'), value: 10, count: undefined },
        { label: t('orders.status.completed'), value: 13, count: undefined },
        { label: t('orders.status.cancelled'), value: 11, count: undefined },
    ])



    // 是否为中文环境
    const isChineseLocale = computed(() => {
        return locale.value === 'zh-CN' || locale.value === 'zh'
    })

    // 为商品详情展开区域提供的方法
    const getProductName = (product: { productTitle: string; productTitleEn: string }) => {
        return isChineseLocale.value ? product.productTitle : product.productTitleEn
    }

    const getProductSpecs = (product: any) => {
        // 处理skuSpecs数组格式的规格数据
        if (product.skuSpecs && Array.isArray(product.skuSpecs)) {
            return product.skuSpecs.map((spec: any) => {
                if (typeof spec === 'object' && spec.attrKey && spec.attrValue) {
                    return `${spec.attrKey}: ${spec.attrValue}`
                }
                return spec
            }).join(', ')
        }
        
        // 处理字符串格式的规格数据
        if (product.productSpecs || product.productSpecsEn) {
            return isChineseLocale.value ? product.productSpecs : product.productSpecsEn
        }
        
        return ''
    }

    // 是否为人民币
    const isCNY = computed(() => {
        return currencyStore.getCurrentCurrency === 'CNY'
    })

    // 获取商品单价（用于商品详情展示）
    const getProductUnitPrice = (product: any) => {
        // 根据系统货币设置显示价格
        if (isCNY.value) {
            // 人民币显示
            const price = product.unitPrice || product.price || 0
            return `￥${price.toFixed(2)}`
        } else {
            // 美元显示
            const usdPrice = product.unitPriceUsd || product.priceUsd || 0
            return `$${usdPrice.toFixed(2)}`
        }
    }

    // 获取商品总价（单价 × 数量）
    const getProductTotalPrice = (product: any) => {
        if (!product) return 0
        const quantity = product.orderedQuantity || product.quantity || 0
        
        // 根据系统货币设置计算价格
        if (isCNY.value) {
            const unitPrice = product.unitPrice || product.price || 0
            return unitPrice * quantity
        } else {
            const unitPriceUsd = product.unitPriceUsd || product.priceUsd || 0
            return unitPriceUsd * quantity
        }
    }

    // 根据当前选择的货币获取订单金额
    const getCurrentOrderAmount = (order: UserPurchaseOrderListVO) => {
    if (currencyStore.getCurrentCurrency === 'CNY') {
        // 返回数字类型的人民币金额
        return order.totalAmount || 0
    } else {
        // 返回数字类型的美元金额
            return order.totalAmountUsd || 0
        }
    }

    // 计算属性 (批量操作)
    const isAnyOrderSelected = computed(() => selectedOrders.value.length > 0)

    // 监听订单和状态变化，清空选项
    watch([orders, currentStatus], () => {
        selectedOrders.value = []
        // 同时清空表格的选择状态
        if (ordersTableRef.value) {
            ordersTableRef.value.clearSelection()
        }
    })

    // 方法
    const getOrderStatusText = (status: number): string => {
        const statusEntry = statusFilters.value.find(f => f.value === status)
        return statusEntry ? statusEntry.label : ''
    }

    const getStatusTagType = (status: string | number): string => {
    // 处理字符串状态
    if (typeof status === 'string') {
        const typeMap: { [key: string]: string } = {
            'PAYMENT_PENDING': 'warning',
            'PAID': 'success',
            'PURCHASING': 'info',
            'SHIPPED': 'primary',
            'IN_WAREHOUSE': 'info',
            'COMPLETED': 'success',
            'CANCELLED': 'danger',
        }
        return typeMap[status] || 'info'
    }
    
    // 处理数字状态（保持兼容性）- 与getOrderStatusText函数保持一致
    const typeMap: { [key: number]: string } = {
        1: 'warning',   // 待支付
        2: 'success',   // 已支付
        4: 'info',      // 采购中
        7: 'primary',   // 已发货
        10: 'info',     // 已入库
        13: 'success',  // 已完成
        11: 'danger',   // 已取消
    }
    return typeMap[status] || 'info'
    }

    const loadOrders = async (params: OrderListRequest = {}) => {
        loading.value = true
        try {
            // 构建请求参数
            const requestParams: OrderListRequest = {
                page: pagination.value.page,
                size: pagination.value.size,
                status: currentStatus.value === null ? undefined : currentStatus.value,
                ...params,
            }

            // 智能判断搜索类型
            if (searchKeyword.value) {
                const searchValue = searchKeyword.value.trim()
                // 判断是否为纯数字（SKU ID通常是数字）
                if (/^\d+$/.test(searchValue)) {
                    // 纯数字，作为SKU ID搜索
                    requestParams.skuId = searchValue
                } else {
                    // 包含字母或其他字符，作为关键词搜索（订单号、商品名称）
                    requestParams.keyword = searchValue
                } 
            }

            // 添加日期范围参数
            if (startDate.value || endDate.value) {
                // 手动构建时间字符串，确保格式正确
                // 开始时间：当天的00:00:00，结束时间：当天的23:59:59
                if (startDate.value) {
                    requestParams.startTime = `${startDate.value}T00:00:00`
                }
                if (endDate.value) {
                    requestParams.endTime = `${endDate.value}T23:59:59`
                }
            }

            const result = await orderApi.getOrderList(requestParams)

            if (result && typeof result.total === 'number' && Array.isArray(result.records)) {
                orders.value = result.records
                pagination.value.page = result.pageIndex
                pagination.value.size = result.pageSize
                pagination.value.totalElements = result.total
                pagination.value.totalPages = result.pageSize > 0 ? Math.ceil(result.total / result.pageSize) : 1
                
                // 加载所有订单的商品数据，并设置默认展开状态
                nextTick(async () => {
                    for (const order of result.records) {
                        if (order.orderNo) {
                            // 设置订单默认展开状态
                            expandedOrders.value.add(order.orderNo)
                            
                            // 加载订单商品详情
                            if (!orderProductsData.value.has(order.orderNo)) {
                                try {
                                    const orderDetail = await orderApi.getOrderDetail(order.orderNo)
                                    orderProductsData.value.set(order.orderNo, orderDetail)
                                } catch (error) {
                                    console.error('加载订单商品详情失败:', error)
                                }
                            }
                        }
                    }
                })
            } else {
                orders.value = []
                pagination.value.totalElements = 0
                pagination.value.page = 1
                pagination.value.totalPages = 1
            }
        } catch (error) {
            console.error('加载订单列表失败:', error)
            ElMessage.error(t('orders.error.loadFailed', '加载订单列表失败'))
        } finally {
            loading.value = false
        }
    }

    const setStatusFilter = (status: number | null) => {
        currentStatus.value = status
        pagination.value.page = 1
        loadOrders()
    }

    const searchOrders = () => {
        pagination.value.page = 1
        loadOrders()
    }

    // 清空搜索条件
    const clearSearchConditions = () => {
        searchKeyword.value = ''
        pagination.value.page = 1
        loadOrders()
    }

    const handleDateChange = () => {
        currentQuickRange.value = '' // 清除快捷选择状态
        pagination.value.page = 1
        loadOrders()
    }
    
    // 设置快捷时间范围
    const setQuickDateRange = (rangeKey: string) => {
        const today = new Date()
        const formatDate = (date: Date) => {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }
        
        let startDateObj: Date
        
        switch (rangeKey) {
            case '7days':
                startDateObj = new Date()
                startDateObj.setDate(today.getDate() - 7)
                break
            case '15days':
                startDateObj = new Date()
                startDateObj.setDate(today.getDate() - 15)
                break
            case '30days':
                startDateObj = new Date()
                startDateObj.setDate(today.getDate() - 30)
                break
            default:
                return
        }
        
        startDate.value = formatDate(startDateObj)
        endDate.value = formatDate(today)
        currentQuickRange.value = rangeKey
        
        pagination.value.page = 1
        loadOrders()
    }

    const changePage = (page: number) => {
        pagination.value.page = page
        loadOrders()
    }

    const formatDate = (dateString: string) => {
        if (!dateString) return ''
        return new Date(dateString).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
        })
    }

    const viewOrderDetail = async (orderNo: string) => {
        router.push(`/user/orders/${orderNo}`)
    }

    // 切换商品信息展开/收起状态
    const toggleOrderExpansion = (orderNo: string) => {
        if (expandedOrders.value.has(orderNo)) {
            expandedOrders.value.delete(orderNo)
        } else {
            expandedOrders.value.add(orderNo)
        }
    }

    // 检查订单是否已展开
    const isOrderExpanded = (orderNo: string) => {
        return expandedOrders.value.has(orderNo)
    }

    const handleSelectionChange = (selection: any[]) => {
        // 清空当前选择
        selectedOrders.value = []
        // 添加新选择的订单
        selection.forEach(order => {
            selectedOrders.value.push(getCurrentOrderNo(order))
        })
    }

    // 处理单个订单选择
    const handleSingleOrderSelection = (orderNo: string, checked: boolean) => {
        if (checked) {
            // 添加到选择列表
            if (!selectedOrders.value.includes(orderNo)) {
                selectedOrders.value.push(orderNo)
            }
        } else {
            // 从选择列表中移除
            const index = selectedOrders.value.indexOf(orderNo)
            if (index > -1) {
                selectedOrders.value.splice(index, 1)
            }
        }
    }

    // 移除了 handleSelectAllChange 方法，因为已经移除了批量操作区域的全选复选框
    const handleBatchCancel = async () => {
        if (!isAnyOrderSelected.value) return
        try {
            const { value: reason } = await ElMessageBox.prompt(
                t('orders.confirm.batchCancelMessageWithReason', { count: selectedOrders.value.length }, `您确定要取消选中的 ${selectedOrders.value.length} 个订单吗？请输入原因：`),
                t('orders.confirm.title', '确认操作'),
                {
                    confirmButtonText: t('common.confirm', '确认'),
                    cancelButtonText: t('common.cancel', '取消'),
                    inputPlaceholder: t('orders.confirm.reasonPlaceholder', '例如：商品缺货、信息填写错误等'),
                    inputPattern: /.+/,
                    inputErrorMessage: t('orders.error.reasonRequired', '取消原因是必填的'),
                    type: 'warning',
                }
            )
            const orderNos = [...selectedOrders.value]
            const cancelPromises = orderNos.map(orderNo => orderApi.cancelOrder(orderNo, reason))
            await Promise.all(cancelPromises)
            ElMessage.success(t('orders.success.batchCancelled', '批量取消订单成功'))
            selectedOrders.value = []
            // 清空表格选择状态
            if (ordersTableRef.value) {
                ordersTableRef.value.clearSelection()
            }
            loadOrders()
        } catch (error: any) {
            if (error !== 'cancel') {
                console.error('批量取消订单失败:', error)
                const errorMessage = error?.response?.data?.message || error?.message || t('orders.error.batchCancelFailed', '批量取消订单失败')
                ElMessage.error(errorMessage)
            }
        }
    }

    // 添加取消订单对话框相关的响应式变量
    const cancelDialogVisible = ref(false)
    const selectedCancelReason = ref('')
    const otherCancelReason = ref('')
    const currentCancelOrderNo = ref('')
    
    // 将 cancelReasons 改为计算属性以支持国际化
    const cancelReasons = computed(() => [
        t('orders.cancel.reasons.outOfStock', '商品缺货'),
        t('orders.cancel.reasons.tooHighPrice', '价格太高'),
        t('orders.cancel.reasons.informationError', '信息填写错误'),
        t('orders.cancel.reasons.duplicateOrder', '重复下单'),
        t('orders.cancel.reasons.other', '其他')
    ])

    const cancelOrder = async (orderNo: string) => {
        currentCancelOrderNo.value = orderNo
        selectedCancelReason.value = ''
        otherCancelReason.value = ''
        cancelDialogVisible.value = true
    }

    const handleCancelConfirm = async () => {
        if (!selectedCancelReason.value) {
            ElMessage.error(t('orders.cancel.selectReason', '请选择取消原因'))
            return
        }
        
        if (selectedCancelReason.value === t('orders.cancel.reasons.other', '其他') && !otherCancelReason.value.trim()) {
            ElMessage.error(t('orders.cancel.otherReason', '请输入其他原因'))
            return
        }
        
        let reason = selectedCancelReason.value
        if (selectedCancelReason.value === t('orders.cancel.reasons.other', '其他')) {
            reason = otherCancelReason.value.trim()
        }
        
        try {
            actionLoading.value = true
            await orderApi.cancelOrder(currentCancelOrderNo.value, reason)
            ElMessage.success(t('orders.success.cancelled', '订单取消成功'))
            cancelDialogVisible.value = false
            loadOrders() // 重新加载订单列表
        } catch (error: any) {
            console.error('取消订单失败:', error.response || error)
            const errorMessage = error?.response?.data?.message || error?.message || t('orders.error.cancelFailed', '取消订单失败')
            ElMessage.error(errorMessage)
        } finally {
            actionLoading.value = false
        }
    }

    const payOrder = async (orderNo: string) => {
        router.push(`/user/orders/${orderNo}/pay`)
    }

    // 申请退款
    const refundOrder = async (orderNo: string) => {
        try {
            const { value: reason } = await ElMessageBox.prompt(
                t('orders.refund.confirmMessage', '请输入退款原因：'),
                t('orders.refund.title', '申请退款'),
                {
                    confirmButtonText: t('common.confirm', '确认'),
                    cancelButtonText: t('common.cancel', '取消'),
                    inputPlaceholder: t('orders.refund.reasonPlaceholder', '例如：商品质量问题、不满意等'),
                    inputPattern: /.+/,
                    inputErrorMessage: t('orders.refund.reasonRequired', '退款原因是必填的'),
                    type: 'warning',
                }
            )

            actionLoading.value = true
            // TODO: 调用退款API
            // await orderApi.applyRefund(orderNo, reason)
            
            // 暂时显示成功消息，实际应该调用API
            ElMessage.success(t('orders.refund.success', '退款申请已提交，请等待处理'))
            
            // 重新加载订单列表
            loadOrders()
        } catch (error: any) {
            if (error !== 'cancel') {
                console.error('申请退款失败:', error)
                const errorMessage = error?.response?.data?.message || error?.message || t('orders.refund.failed', '申请退款失败')
                ElMessage.error(errorMessage)
            }
        } finally {
            actionLoading.value = false
        }
    }

    // 获取订单商品数据
    const getOrderProducts = (orderNo: string) => {
        const orderData = orderProductsData.value.get(orderNo)
        // 根据API响应结构，商品数据在items字段中
        return orderData?.items || orderData?.orderItems || []
    }

    // 生命周期
    onMounted(() => {
        // 设置默认时间范围：7天内（从7天前到今天）
        const today = new Date()
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(today.getDate() - 7)
        
        // 格式化日期为 YYYY-MM-DD
        const formatDate = (date: Date) => {
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
        }
        
        // 设置默认7天时间范围
        startDate.value = formatDate(sevenDaysAgo)
        endDate.value = formatDate(today)
        
        // 加载订单数据
        loadOrders()
    })

    // 计算单个订单金额的计算属性
    const calculateOrderAmount = (order: UserPurchaseOrderListVO) => {
        
        // 总金额
        const totalAmountUsd = computed(() =>
            order.totalAmountUsd || 0
        )
        const totalAmountCny = computed(() =>
            order.totalAmount || 0
        )
        return {
            totalAmountUsd,
            totalAmountCny
        }
    }

    // 处理图片加载错误
    const handleImageError = (event: Event) => {
        const target = event.target as HTMLImageElement
        target.src = '/placeholder-image.png'
    }

    // 获取当前订单号
    const getCurrentOrderNo = (order: any) => {
        return order?.orderNo || order?.id || 'N/A'
    }

    // 查看商品详情
    const viewProduct = (product: any) => {
        // 这里可以跳转到商品详情页面或打开商品详情弹窗
        console.log('查看商品:', product)
        // 示例：跳转到商品详情页
        // router.push(`/product/${product.productId || product.id}`)
    }

    // 再次购买
    const buyAgain = (product: any) => {
        // 这里可以将商品添加到购物车或直接购买
        console.log('再次购买:', product)
        // 示例：添加到购物车
        // addToCart(product)
    }

    // 复制SKU ID到剪贴板
    const copySkuId = async (skuId: string) => {
        if (!skuId || skuId === '-') {
            ElMessage.warning(t('orders.copySkuError', 'SKU ID不存在，无法复制'))
            return
        }
        
        try {
            // 使用现代浏览器的Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(skuId)
            } else {
                // 降级方案：使用传统的document.execCommand方法
                const textArea = document.createElement('textarea')
                textArea.value = skuId
                textArea.style.position = 'fixed'
                textArea.style.left = '-999999px'
                textArea.style.top = '-999999px'
                document.body.appendChild(textArea)
                textArea.focus()
                textArea.select()
                document.execCommand('copy')
                textArea.remove()
            }
            
            ElMessage.success(t('orders.copySkuSuccess', `SKU ID "${skuId}" 已复制到剪贴板`))
        } catch (error) {
            console.error('复制SKU ID失败:', error)
            ElMessage.error(t('orders.copySkuFailed', '复制失败，请手动复制'))
        }
    }

    // 复制订单号到剪贴板
    const copyOrderNo = async (orderNo: string) => {
        if (!orderNo) {
            ElMessage.warning(t('orders.copyOrderError', '订单号不存在，无法复制'))
            return
        }
        try {
            // 使用现代浏览器的Clipboard API
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(orderNo)
            } else {
                // 降级方案：使用传统的document.execCommand方法
                const textArea = document.createElement('textarea')
                textArea.value = orderNo
                textArea.style.position = 'fixed'
                textArea.style.left = '-999999px'
                textArea.style.top = '-999999px'
                document.body.appendChild(textArea)
                textArea.focus()
                textArea.select()
                document.execCommand('copy')
                textArea.remove()
            }
            ElMessage.success(t('orders.copyOrderSuccess', `订单号 "${orderNo}" 已复制到剪贴板`))
        } catch (error) {
            console.error('复制订单号失败:', error)
            ElMessage.error(t('orders.copyOrderFailed', '复制失败，请手动复制'))
        }
    }

    // 图片预览功能
    const previewImage = (imageUrl: string, productName: string) => {
        if (!imageUrl || imageUrl === '/placeholder-image.png') {
            ElMessage.warning(t('orders.noImageToPreview', '暂无图片可预览'))
            return
        }
        previewImageUrl.value = imageUrl
        previewImageTitle.value = productName || t('orders.productImage', '商品图片')
        imagePreviewVisible.value = true
    }

    // 处理预览图片加载错误
    const handlePreviewImageError = (event: Event) => {
        const target = event.target as HTMLImageElement
        target.src = '/placeholder-image.png'
        ElMessage.error(t('orders.imageLoadError', '图片加载失败'))
    }
</script>

<style lang="scss">
    .orders-container {
        min-height: 100vh;
        width: 75%;
        margin: 0 auto;
        padding: 1rem 20px;
        box-sizing: border-box;

        @media (min-width: 768px) {
            padding: 2rem 20px;
        }
    }

    .page-header {
        text-align: left;
        margin-bottom: 2rem;

        .page-title {
            font-size: 2.25rem;
            font-weight: 700;
            color: #1e293b;
            background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .page-subtitle {
            color: #64748b;
            font-size: 1rem;
            margin-top: 0.5rem;
        }
    }

    /* 筛选和订单区域样式 - 统一设计 */
    .filters-and-orders-section {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        overflow: hidden;
        margin: 1.5rem 0;
        padding: 1.5rem 0;
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        position: relative;
    }

    .batch-operations-row {
        padding-bottom: 1rem;
        border-bottom: 1px solid #f1f5f9;
        margin-bottom: 0.5rem;
    }

    .batch-operations {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        justify-content: flex-start;
        flex-wrap: wrap;

        @media (max-width: 768px) {
            justify-content: center;
            gap: 1rem;
        }

        @media (max-width: 480px) {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;
        }
    }

    /* 统一筛选行布局 */
    .unified-filters-row {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        padding: 1.25rem;

        .filters-content {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            flex-wrap: wrap;

            @media (max-width: 1200px) {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
            }
        }

        @media (max-width: 768px) {
            padding: 1rem;
            gap: 0.75rem;
        }
    }

    /* 搜索组 */
    .search-group {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
        min-width: 300px;

        @media (max-width: 1200px) {
            min-width: 100%;
        }

        .search-input-wrapper {
            position: relative;
            flex: 1;
            display: flex;
            align-items: center;
        }

        .search-input {
            width: 100%;
            padding: 0.875rem 1.25rem;
            padding-right: 3rem;
            border: 2px solid transparent;
            border-radius: 1rem;
            outline: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            font-size: 0.9rem;
            font-weight: 500;
            color: #1e293b;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05), inset 0 1px 3px rgba(0, 0, 0, 0.02);
            position: relative;
            
            &:focus {
                border-color: #f97316;
                background: #ffffff;
                box-shadow: 0 0 0 4px rgba(249, 115, 22, 0.1), 0 8px 20px rgba(0, 0, 0, 0.08);
                transform: translateY(-1px);
            }
            
            &:hover {
                background: #ffffff;
                box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08), inset 0 1px 3px rgba(0, 0, 0, 0.02);
                transform: translateY(-0.5px);
            }

            &::placeholder {
                color: #94a3b8;
                font-size: 0.875rem;
                font-weight: 400;
                transition: color 0.3s ease;
            }
            
            &:focus::placeholder {
                color: #cbd5e1;
            }
        }

        .clear-search-btn {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            width: 1.5rem;
            height: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            font-weight: 600;
            color: #94a3b8;
            border-radius: 50%;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            background: rgba(248, 250, 252, 0.8);
            backdrop-filter: blur(8px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            
            &:hover {
                background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
                color: #dc2626;
                transform: translateY(-50%) scale(1.1);
                box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
            }
            
            &:active {
                transform: translateY(-50%) scale(0.95);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
            }
        }

        .search-btn {
            padding: 0.875rem 1.25rem;
            background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
            color: white;
            border: 2px solid transparent;
            border-radius: 1rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 600;
            flex-shrink: 0;
            min-width: 52px;
            box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25), 0 2px 6px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
            
            /* 按钮光效 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }
            
            &:hover::before {
                left: 100%;
            }

            &:hover {
                background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                transform: translateY(-2px);
                box-shadow: 0 8px 20px rgba(249, 115, 22, 0.35), 0 4px 12px rgba(0, 0, 0, 0.08);
                border-color: rgba(255, 255, 255, 0.2);
            }
            
            &:active {
                transform: translateY(0);
                box-shadow: 0 4px 12px rgba(249, 115, 22, 0.25), 0 2px 6px rgba(0, 0, 0, 0.05);
            }

            svg {
                width: 1.125rem;
                height: 1.125rem;
                position: relative;
                z-index: 1;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
            }
        }
    }

    /* 状态筛选组 */
    .status-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-shrink: 0;
        min-width: 200px;

        @media (max-width: 1200px) {
            width: 100%;
            min-width: auto;
        }

        .filter-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #475569;
            white-space: nowrap;
        }

        .status-select {
            flex: 1;
            min-width: 150px;
        }
    }

    /* 时间筛选组 */
    .time-group {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex-shrink: 0;

        @media (max-width: 1200px) {
            width: 100%;
            flex-direction: column;
            align-items: stretch;
            gap: 0.5rem;
        }

        .filter-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: #475569;
            white-space: nowrap;

            @media (max-width: 1200px) {
                align-self: flex-start;
            }
        }

        .time-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
            flex-wrap: wrap;

            @media (max-width: 1200px) {
                flex-direction: column;
                align-items: stretch;
                gap: 0.75rem;
            }

            .quick-buttons {
                display: flex;
                gap: 0.5rem;
                flex-wrap: wrap;

                @media (max-width: 1200px) {
                    justify-content: center;
                }

                /* 快捷时间按钮 - 现代化美观设计 */
                .quick-btn {
                    padding: 0.625rem 1.25rem;
                    border: 2px solid rgba(226, 232, 240, 0.6);
                    border-radius: 0.75rem;
                    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                    color: #475569;
                    font-size: 0.875rem;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.04);
                    position: relative;
                    overflow: hidden;
                    
                    /* 按钮光效 */
                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
                        transition: left 0.4s ease;
                    }
                    
                    &:hover::before {
                        left: 100%;
                    }

                    &:hover {
                        border-color: rgba(249, 115, 22, 0.4);
                        background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                        color: #f97316;
                        transform: translateY(-1px);
                        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.15);
                    }

                    &.active {
                        border-color: #ea580c;
                        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
                        color: white;
                        box-shadow: 0 4px 12px rgba(249, 115, 22, 0.3), 0 2px 6px rgba(0, 0, 0, 0.05);
                        transform: translateY(-1px);
                        
                        &::before {
                            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                        }
                        
                        &:hover {
                            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                            box-shadow: 0 6px 16px rgba(249, 115, 22, 0.4), 0 3px 8px rgba(0, 0, 0, 0.08);
                            transform: translateY(-2px);
                        }
                    }
                }
            }

            .date-range {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                flex-wrap: wrap;

                @media (max-width: 1200px) {
                    justify-content: center;
                }

                .date-picker {
                    width: 140px;

                    @media (max-width: 768px) {
                        width: 120px;
                    }
                }

                .date-separator {
                    font-size: 0.875rem;
                    color: #64748b;
                    font-weight: 500;
                    margin: 0 0.25rem;
                }
            }
        }
    }

    // 空状态样式
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        background: white;
        border-radius: 1rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
        margin-bottom: 2rem;
        border: 1px solid rgba(255, 255, 255, 0.8);

        .empty-icon {
            margin-bottom: 1.5rem;
            color: #cbd5e1;
        }

        h3 {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.75rem;
        }

        p {
            color: #64748b;
            margin-bottom: 2rem;
            font-size: 1rem;
        }

        .btn {
            padding: 0.75rem 2rem;
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            text-decoration: none;
            border-radius: 0.75rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(249, 115, 22, 0.2);

            &:hover {
                background: linear-gradient(135deg, #ea580c, #dc2626);
                transform: translateY(-1px);
                box-shadow: 0 6px 12px rgba(249, 115, 22, 0.3);
            }
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #2563eb);
            box-shadow: 0 4px 8px rgba(59, 130, 246, 0.2);

            &:hover {
                background: linear-gradient(135deg, #2563eb, #1d4ed8);
                box-shadow: 0 6px 12px rgba(59, 130, 246, 0.3);
            }
        }
    }

    // 分页样式
    .pagination-container {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: 2rem;

        .el-pagination {
            background: white;
            padding: 1rem;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.8);
        }
    }

    .cancel-reason-form {
        .form-item {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
    }

    // 取消订单对话框样式
    :deep(.el-dialog) {
        border-radius: 1rem;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        
        .el-dialog__header {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-bottom: 1px solid #e2e8f0;
            padding: 1rem 1.5rem;
            border-radius: 1rem 1rem 0 0;
            
            .el-dialog__title {
                font-weight: 600;
                color: #1e293b;
            }
        }
        
        .el-dialog__body {
            padding: 1.5rem;
        }
        
        .el-dialog__footer {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border-top: 1px solid #e2e8f0;
            padding: 1rem 1.5rem;
            border-radius: 0 0 1rem 1rem;
            
            .dialog-footer {
                display: flex;
                justify-content: flex-end;
                gap: 0.75rem;
            }
        }
    }

    // 复选框样式优化 - 现代化设计
    :deep(.el-checkbox) {
        .el-checkbox__input {
            .el-checkbox__inner {
                width: 18px;
                height: 18px;
                border-radius: 0.375rem;
                border: 2px solid #d1d5db;
                background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                position: relative;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
                
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%) scale(0);
                    width: 10px;
                    height: 10px;
                    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                    border-radius: 0.125rem;
                    transition: all 0.2s ease;
                }
                
                &:hover {
                    border-color: #3b82f6;
                    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.15);
                    transform: translateY(-0.5px);
                }
            }
            
            &.is-checked .el-checkbox__inner {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border-color: #1d4ed8;
                box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                
                &::after {
                    transform: translate(-50%, -50%) scale(1);
                    background: white;
                    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }
                
                &:hover {
                    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                    box-shadow: 0 3px 10px rgba(59, 130, 246, 0.4);
                }
            }
            
            &.is-indeterminate .el-checkbox__inner {
                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                border-color: #d97706;
                
                &::after {
                    transform: translate(-50%, -50%) scale(1);
                    background: white;
                    width: 8px;
                    height: 2px;
                    border-radius: 1px;
                }
            }
        }
        
        .el-checkbox__label {
            font-size: 0.875rem;
            color: #374151;
            font-weight: 500;
            margin-left: 0.5rem;
            transition: color 0.2s ease;
        }
        
        &:hover .el-checkbox__label {
            color: #1f2937;
        }
        
        &.is-checked .el-checkbox__label {
            color: #1f2937;
            font-weight: 600;
        }
    }
    
    // 日期选择器样式优化
    :deep(.el-date-editor) {
        border: none !important;
        box-shadow: none !important;
        background: transparent !important;
        
        .el-input__wrapper {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
            padding: 0.25rem 0.5rem;
            
            .el-input__inner {
                border: none !important;
                background: transparent !important;
                font-size: 0.875rem;
                color: #374151;
                
                &::placeholder {
                    color: #9ca3af;
                    font-size: 0.875rem;
                }
            }
            
            .el-input__prefix {
                color: #6b7280;
            }
        }
        
        &:hover .el-input__wrapper {
            background-color: #f8fafc !important;
            border-radius: 0.375rem;
        }
        
        &.is-focus .el-input__wrapper {
            background-color: #f8fafc !important;
            border-radius: 0.375rem;
            box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.1) !important;
        }
    }
    
    // 下拉选择器样式优化
    :deep(.el-select) {
        .el-select__wrapper {
            border: 1px solid #e2e8f0;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            
            &:hover {
                border-color: #f97316;
                box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
            }
            
            &.is-focus {
                border-color: #f97316;
                box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
            }
            
            .el-select__selected-item {
                font-size: 0.875rem;
                color: #374151;
            }
            
            .el-select__placeholder {
                color: #9ca3af;
                font-size: 0.875rem;
            }
        }
    }

    // 新的订单详情容器样式
    .order-detail-container {
        padding: 1rem;
        background: #ffffff;
        border-radius: 0.75rem;
        border: 1px solid #e5e7eb;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        
        // 订单头部信息
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
            transition: all 0.3s ease;
            border-radius: 0.5rem;
            padding: 0.75rem;
            margin: -0.75rem -0.75rem 1rem -0.75rem;
            
            &:hover {
                background-color: #f8fafc;
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
                border-bottom-color: #e2e8f0;
            }
            
            .order-header-left,
            .order-header-center,
            .order-header-right {
                display: flex;
                flex-direction: row; // 改为水平排列
                align-items: center; // 垂直居中对齐
                gap: 0.5rem; // 调整间距
            }
            
            .order-header-center {
                flex: 1;
                justify-content: center;
            }
            
            .order-header-right {
                flex: 0 0 auto;
            }
            
            .order-header-left {
                display: flex;
                flex-direction: row;
                align-items: center; // 改为居中对齐
                gap: 0.75rem;
                
                .order-checkbox {
                    // 移除margin-top，因为现在是居中对齐
                    
                    :deep(.el-checkbox__input) {
                        .el-checkbox__inner {
                            width: 18px;
                            height: 18px;
                            border: 2px solid #d1d5db;
                            border-radius: 0.375rem;
                            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
                            
                            &:hover {
                                border-color: #3b82f6;
                                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                                transform: translateY(-1px) scale(1.05);
                                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
                            }
                            
                            &::after {
                                width: 4px;
                                height: 8px;
                                border: 2px solid #ffffff;
                                border-left: 0;
                                border-top: 0;
                                transform: rotate(45deg) scaleY(0);
                                transition: transform 0.15s ease-in 0.05s;
                                transform-origin: center;
                            }
                        }
                        
                        &.is-checked .el-checkbox__inner {
                            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                            border-color: #1d4ed8;
                            
                            &::after {
                                transform: rotate(45deg) scaleY(1);
                            }
                            
                            &:hover {
                                background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                                border-color: #1e40af;
                            }
                        }
                        
                        &.is-indeterminate .el-checkbox__inner {
                            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                            border-color: #d97706;
                            
                            &::before {
                                content: '';
                                position: absolute;
                                display: block;
                                background-color: #ffffff;
                                height: 2px;
                                transform: scale(0.5);
                                left: 0;
                                right: 0;
                                top: 5px;
                            }
                        }
                    }
                }
                
                .order-info {
                    display: flex;
                    flex-direction: row; // 改为水平排列
                    align-items: center; // 垂直居中对齐
                    gap: 0.5rem; // 调整间距
                }
                
                // 复制订单号按钮样式
                .copy-order-btn {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 1.5rem;
                    height: 1.5rem;
                    margin-left: 0.5rem;
                    padding: 0;
                    background: transparent;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    color: #6b7280;
                    
                    &:hover {
                        background: #f3f4f6;
                        border-color: #52018d;
                        color: #52018d;
                        transform: translateY(-1px);
                        box-shadow: 0 2px 4px rgba(82, 1, 141, 0.1);
                    }
                    
                    &:active {
                        transform: translateY(0);
                        box-shadow: 0 1px 2px rgba(82, 1, 141, 0.1);
                    }
                    
                    .copy-icon {
                        width: 0.875rem;
                        height: 0.875rem;
                        stroke-width: 2;
                    }
                }
            }
            
            .order-label {
                font-size: 0.75rem;
                color: #6b7280;
                font-weight: 500;
                letter-spacing: 0.05em;
            }
            
            .order-number {
                font-size: 1rem;
                color: #52018d;
                font-weight: 800;
                word-break: break-all;
                transition: color 0.2s ease;
                
                // 可点击订单号的样式
                &.clickable-order-number {
                    cursor: pointer;
                    text-decoration: underline;
                    text-decoration-color: transparent;
                    transition: all 0.2s ease;
                    
                    &:hover {
                        color: #7c3aed;
                        text-decoration-color: #7c3aed;
                        transform: translateY(-1px);
                    }
                    
                    &:active {
                        transform: translateY(0);
                        color: #5b21b6;
                    }
                }
            }
            
            .order-time {
                font-size: 0.875rem;
                color: #111827;
                font-weight: 500;
            }
        }
        
        // 商品信息表格
        .order-products-table {
            margin-bottom: 1rem;
            
            .table-header {
                display: grid;
                grid-template-columns: 1fr 2fr 1.5fr 0.8fr 1fr 1.2fr 1.2fr 1fr;
                gap: 0.75rem;
                padding: 0.75rem;
                background: #52018d;
                border-radius: 0.5rem;
                border: 1px solid #e5e7eb;
                font-size: 0.75rem;
                font-weight: 600;
                color: #f9fafb;
                letter-spacing: 0.05em;
                
                > div {
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    
                    &.col-product-name {
                        text-align: left;
                        justify-content: flex-start;
                    }
                    
                    &.col-product-image {
                        text-align: center;
                        justify-content: center;
                    }
                }
            }
            
            .table-row {
                display: grid;
                grid-template-columns: 1fr 2fr 1.5fr 0.8fr 1fr 1.2fr 1.2fr 1fr;
                gap: 0.75rem;
                padding: 0.75rem;
                border: 1px solid #e5e7eb;
                border-top: none;
                align-items: center;
                
                &.loading-row {
                    grid-template-columns: 1fr;
                    text-align: center;
                    color: #6b7280;
                    font-style: italic;
                }
                
                .col-product-name {
                    text-align: left;
                    
                    .product-name {
                        font-size: 0.875rem;
                        color: #111827;
                        font-weight: 800;
                        margin-bottom: 0.25rem;
                        line-height: 1.4;
                    }
                    
                    .more-products {
                        font-size: 0.75rem;
                        color: #6b7280;
                        font-style: italic;
                    }
                }
                
                .col-product-image {
                    text-align: center;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    
                    .product-image-small {
                        width: 40px;
                        height: 40px;
                        object-fit: cover;
                        border-radius: 0.375rem;
                        border: 1px solid #e5e7eb;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            transform: scale(1.1);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                        }
                    }
                }
                
                .col-specification {
                    text-align: center;
                    
                    .specification-text {
                        font-size: 0.75rem;
                        color: #6b7280;
                        line-height: 1.4;
                    }
                }
                
                .col-quantity,
                .col-unit-price,
                .col-sku,
                .col-order-status,
                .col-total {
                    text-align: center;
                    
                    .quantity-text,
                    .price-text,
                    .sku-text,
                    .status-text,
                    .total-text {
                        font-size: 0.875rem;
                        color: #111827;
                        font-weight: 500;
                    }
                    
                    .sku-text {
                        color: #3b82f6;
                        cursor: pointer;
                        
                        &:hover {
                            text-decoration: underline;
                        }
                    }
                    
                    .total-text {
                        color: #ea580c;
                        font-weight: 600;
                    }
                }
            }
        }
        
        // 订单底部区域
        .order-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            border-radius: 0 0 0.75rem 0.75rem;
            gap: 1rem;
        }
        
        // 订单汇总信息
        .order-summary {
            flex: 1;
            
            .summary-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.375rem 0;
                
                &:not(:last-child) {
                    border-bottom: 1px solid #e5e7eb;
                }
                
                &.total-row {
                    border-top: 2px solid #d1d5db;
                    margin-top: 0.5rem;
                    padding-top: 0.75rem;
                    font-weight: 600;
                    
                    .summary-label {
                        font-size: 1rem;
                        color: #111827;
                    }
                    
                    .total-price {
                        font-size: 1.125rem;
                        color: #dc2626;
                        font-weight: 700;
                    }
                }
                
                .summary-label {
                    font-size: 1rem;
                    font-weight: 500;
                }
                
                .summary-value {
                    font-size: 1rem;
                    color: #111827;
                    font-weight: 800;
                }
            }
        }
        
        // 操作按钮
        .order-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: flex-end;
            flex-wrap: wrap;
            flex: 0 0 auto;
            
            .el-button {
                font-size: 0.75rem;
                padding: 0.375rem 0.75rem;
                height: 28px;
                min-width: 80px;
                border-radius: 0.375rem;
                font-weight: 500;
                transition: all 0.3s ease;
                white-space: nowrap;
                
                &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                }
            }
        }
    }
    
    // 响应式设计
    @media (max-width: 1024px) {
        .order-detail-container {
            .order-products-table {
                .table-header,
                .table-row {
                    grid-template-columns: 0.8fr 1.5fr 1fr 0.6fr 0.8fr 1fr 1fr 0.8fr;
                    gap: 0.5rem;
                    padding: 0.5rem;
                    font-size: 0.75rem;
                }
            }
        }
    }
    
    @media (max-width: 768px) {
        .order-detail-container {
            padding: 0.75rem;
            
            .order-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
                
                .order-header-left,
                .order-header-center,
                .order-header-right {
                    width: 100%;
                }
                
                .order-header-center {
                    justify-content: flex-start;
                }
                
                .order-header-right {
                    justify-content: flex-end;
                }
            }
            
            .order-footer {
                flex-direction: column;
                align-items: stretch;
                gap: 1rem;
                
                .order-summary {
                    .summary-row {
                        justify-content: center;
                        text-align: center;
                    }
                }
                
                .order-actions {
                    justify-content: center;
                }
            }
            
            .order-products-table {
                .table-header {
                    display: none; // 隐藏表头，使用卡片式布局
                }
                
                .table-row {
                    display: block;
                    padding: 1rem;
                    border-radius: 0.5rem;
                    margin-bottom: 0.75rem;
                    background: #ffffff;
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
                    
                    > div {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 0.25rem 0;
                        
                        &:before {
                            content: attr(data-label);
                            font-weight: 600;
                            color: #6b7280;
                            font-size: 0.75rem;
                            text-transform: uppercase;
                            letter-spacing: 0.05em;
                        }
                    }
                    
                    .col-product-image {
                        flex-direction: column;
                        align-items: center;
                        
                        &:before {
                            content: 'Image';
                            margin-bottom: 0.25rem;
                        }
                    }
                    
                    .col-product-name {
                        flex-direction: column;
                        align-items: flex-start;
                        
                        &:before {
                            content: 'Product Name';
                            margin-bottom: 0.25rem;
                        }
                    }
                }
            }
            
            .order-actions {
                justify-content: center;
                
                .el-button {
                    font-size: 0.75rem;
                    padding: 0.375rem 0.75rem;
                    height: 28px;
                    min-width: 80px;
                    border-radius: 0.375rem;
                    font-weight: 500;
                    white-space: nowrap;
                    flex: 0 0 auto;
                }
            }
        }
    }

    .toggle-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #52018d;
        font-size: 1.25rem;
        width: 2rem;
        height: 2rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
            background: rgba(82, 1, 141, 0.1);
            border-color: rgba(82, 1, 141, 0.3);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(82, 1, 141, 0.15);
        }
        
        &:active {
            transform: translateY(0);
        }
        
        &.expanded {
            transform: rotate(180deg);
            
            &:hover {
                transform: rotate(180deg) translateY(-1px);
            }
        }
    }

    /* 商品详细信息表格展开动画 */
    .order-products-table {
        animation: slideDown 0.3s ease-out;
        overflow: hidden;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            max-height: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            max-height: 1000px;
            transform: translateY(0);
        }
    }

    /* 可点击的SKU样式 */
    .clickable-sku {
        cursor: pointer;
        transition: all 0.2s ease;
        user-select: none;
        position: relative;
        
        &:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        /* 添加复制图标提示 */
        &::after {
            content: '📋';
            font-size: 0.6rem;
            margin-left: 0.25rem;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }
        
        &:hover::after {
            opacity: 1;
        }
    }

    .product-price-detail {
        font-size: 0.875rem;
        font-weight: 500;
    }

    .product-quantity-detail {
        font-size: 0.875rem;
    }

    /* 商品操作按钮区域 - 现代化美观设计 */
    .product-actions-detail {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.75rem;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        max-width: 140px;
        margin: 0 auto !important;
        padding: 0.75rem 0.5rem;
        
        &:hover {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }
        
        .el-button {
            width: 100% !important;
            height: 36px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            padding: 8px 12px !important;
            border-radius: 0.5rem !important;
            white-space: nowrap;
            text-align: center !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 100px !important;
            max-width: 130px !important;
            box-sizing: border-box !important;
            margin: 0 !important;
            border: 1px solid transparent !important;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
            position: relative !important;
            overflow: hidden !important;
            
            /* 按钮内部光效 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }
            
            &:hover::before {
                left: 100%;
            }
            
            /* 主要按钮样式 */
            &.el-button--primary {
                background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
                border-color: #2563eb !important;
                color: white !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.25) !important;
                
                &:hover {
                    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
                    border-color: #1d4ed8 !important;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35) !important;
                    transform: translateY(-2px) !important;
                }
                
                &:active {
                    transform: translateY(0) !important;
                    box-shadow: 0 2px 6px rgba(59, 130, 246, 0.25) !important;
                }
            }
            
            /* 成功按钮样式 */
            &.el-button--success {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                border-color: #059669 !important;
                color: white !important;
                box-shadow: 0 2px 6px rgba(16, 185, 129, 0.25) !important;
                
                &:hover {
                    background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
                    border-color: #047857 !important;
                    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.35) !important;
                    transform: translateY(-2px) !important;
                }
                
                &:active {
                    transform: translateY(0) !important;
                    box-shadow: 0 2px 6px rgba(16, 185, 129, 0.25) !important;
                }
            }
            
            /* Plain 按钮样式 */
            &.is-plain {
                background: rgba(255, 255, 255, 0.9) !important;
                backdrop-filter: blur(10px) !important;
                
                &.el-button--primary {
                    color: #2563eb !important;
                    border-color: rgba(37, 99, 235, 0.3) !important;
                    background: rgba(59, 130, 246, 0.05) !important;
                    
                    &:hover {
                        background: rgba(59, 130, 246, 0.1) !important;
                        border-color: rgba(37, 99, 235, 0.5) !important;
                        color: #1d4ed8 !important;
                    }
                }
                
                &.el-button--success {
                    color: #059669 !important;
                    border-color: rgba(5, 150, 105, 0.3) !important;
                    background: rgba(16, 185, 129, 0.05) !important;
                    
                    &:hover {
                        background: rgba(16, 185, 129, 0.1) !important;
                        border-color: rgba(5, 150, 105, 0.5) !important;
                        color: #047857 !important;
                    }
                }
            }
            
            /* 按钮文字样式 */
            span {
                position: relative;
                z-index: 1;
                font-weight: 500;
                letter-spacing: 0.025em;
            }
        }
    }

    .loading-products {
        text-align: center;
        padding: 1rem 0;
    }

    .loading-text {
        font-size: 0.875rem;
        color: #6b7280;
    }

    /* 订单号列样式 - 基础布局 */
    .order-number-cell {
        display: flex;
        flex-direction: column;
        /* clickable样式已在order-basic-info中定义 */
    }

    /* 新的订单信息容器样式 */
    .order-info-container {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        padding: 0.5rem 0;
    }
    
    /* 基本订单信息样式 */
    .order-basic-info {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        
        &.clickable {
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 0.375rem;
            transition: all 0.2s ease;
            
            &:hover {
                background-color: #f8fafc;
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                
                .order-number {
                    color: #31018d;
                }
            }
            
            &:active {
                transform: translateY(0);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
        }
        
        .order-number {
            font-weight: 700;
            color: #52018d;
            word-break: break-all;
            transition: color 0.2s ease;
        }
        
        .order-quantity {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .order-date {
            font-size: 0.75rem;
            color: #6b7280;
        }
    }
    
    /* 订单金额行样式 */
    .order-amount-row {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.5rem 0;
        border-top: 1px solid #e5e7eb;
        
        .order-amount {
            font-size: 1.125rem;
            font-weight: 700;
            color: #059669;
        }
        
        .order-update-time {
            font-size: 0.75rem;
            color: #6b7280;
        }
    }
    
    /* 操作按钮行样式 */
    .order-actions-row {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        padding: 0.5rem 0;
        border-top: 1px solid #e5e7eb;
        
        .el-button {
            font-size: 0.75rem !important;
            padding: 0.375rem 0.75rem !important;
            height: auto !important;
            min-height: 28px !important;
            border-radius: 0.375rem !important;
            margin: 0 !important;
        }
    }

    /* 订单金额列样式 */
    .order-amount-cell {
        text-align: right;
    }
    
    /* 操作列样式 */
    .order-actions-cell {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.5rem;
        align-items: center !important;
        justify-content: center !important;
        min-height: 80px;
        padding: 0.5rem !important;
        text-align: center !important;
    }

    .action-buttons-row {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.5rem;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        max-width: 110px;
        margin: 0 auto !important;
        
        .el-button {
            width: 100% !important;
            height: 32px !important;
            font-size: 12px !important;
            padding: 6px 8px !important;
            border-radius: 4px;
            white-space: nowrap;
            text-align: center !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-width: 90px !important;
            max-width: 110px !important;
            box-sizing: border-box !important;
            margin: 0 !important;
        }
    }

    /* 简化的表格样式覆盖 */
    .el-table .el-table__cell {
        &:has(.order-actions-cell) {
            padding: 0 !important;
            text-align: center !important;
            vertical-align: middle !important;
        }
        
        &:has(.product-actions-detail) {
            text-align: center !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
    }

    /* 按钮间距重置 */
    .action-buttons-row .el-button,
    .product-actions-detail .el-button {
        margin: 0 !important;
    }

    /* 移动端响应式设计 */
    @media (max-width: 768px) {
        /* 整体容器适配 */
        .orders-container {
            padding: 0.75rem;
            background: #f8fafc;
        }

        /* 页面标题适配 */
        .page-header {
            text-align: center;
            margin-bottom: 1.5rem;
            
            .page-title {
                font-size: 1.75rem;
                line-height: 1.2;
            }
            
            .page-subtitle {
                font-size: 0.875rem;
                margin-top: 0.25rem;
            }
        }

        /* 筛选区域移动端适配 */
        .filters-section {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 0.75rem;
        }

        /* 批量操作区域适配 */
        .batch-operations {
            justify-content: center;
            gap: 0.5rem;
            
            .el-checkbox {
                font-size: 0.875rem;
            }
            
            .el-button {
                font-size: 0.875rem;
                padding: 0.5rem 1rem;
            }
        }

        /* 搜索组移动端优化 */
        .search-group {
            min-width: 100%;
            
            .search-input {
                font-size: 0.875rem;
                padding: 0.625rem 0.875rem;
                padding-right: 2.25rem;
            }
            
            .search-btn {
                padding: 0.625rem 0.875rem;
                min-width: 44px;
            }
        }

        /* 状态筛选移动端优化 */
        .status-group {
            .filter-label {
                font-size: 0.875rem;
                margin-bottom: 0.25rem;
            }
        }

        /* 时间筛选移动端优化 */
        .time-group {
            .filter-label {
                font-size: 0.875rem;
                margin-bottom: 0.25rem;
            }
            
            .quick-buttons {
                justify-content: flex-start;
                
                /* 移动端快捷时间按钮 - 现代化美观设计 */
                .quick-btn {
                    font-size: 0.75rem;
                    padding: 0.5rem 0.875rem;
                    border: 1.5px solid rgba(226, 232, 240, 0.6);
                    border-radius: 0.625rem;
                    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                    color: #475569;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                    white-space: nowrap;
                    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
                    position: relative;
                    overflow: hidden;
                    min-height: 36px;
                    
                    /* 移动端按钮光效 */
                    &::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: -100%;
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
                        transition: left 0.4s ease;
                    }
                    
                    &:hover::before {
                        left: 100%;
                    }

                    &:hover {
                        border-color: rgba(249, 115, 22, 0.4);
                        background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                        color: #f97316;
                        transform: translateY(-0.5px);
                        box-shadow: 0 3px 8px rgba(249, 115, 22, 0.15);
                    }

                    &.active {
                        border-color: #ea580c;
                        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
                        color: white;
                        box-shadow: 0 3px 8px rgba(249, 115, 22, 0.3), 0 1px 4px rgba(0, 0, 0, 0.05);
                        transform: translateY(-0.5px);
                        
                        &::before {
                            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                        }
                        
                        &:hover {
                            background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                            box-shadow: 0 4px 10px rgba(249, 115, 22, 0.4), 0 2px 6px rgba(0, 0, 0, 0.08);
                            transform: translateY(-1px);
                        }
                    }
                }
            }
            
            .date-range {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
                flex-wrap: nowrap;
                
                .date-picker {
                    width: 140px;
                    min-width: 140px;
                    flex-shrink: 0;
                    height: 44px;
                    font-size: 0.875rem;
                    
                    /* 移动端日期选择器内部样式优化 */
                    :deep(.el-input__inner) {
                        height: 44px;
                        font-size: 0.875rem;
                        padding: 0 12px;
                        border-radius: 0.5rem;
                        border: 1.5px solid #e2e8f0;
                        transition: all 0.2s ease;
                        
                        &:focus {
                            border-color: #3b82f6;
                            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                        }
                    }
                    
                    /* 移动端日期选择器图标优化 */
                    :deep(.el-input__prefix) {
                        left: 8px;
                    }
                    
                    :deep(.el-input__icon) {
                        font-size: 16px;
                        color: #6b7280;
                    }
                }
                
                .date-separator {
                    font-size: 0.75rem;
                    color: #64748b;
                    font-weight: 500;
                    margin: 0 0.25rem;
                    flex-shrink: 0;
                }
            }
        }

        /* 订单金额列移动端适配 */
        .order-amount-cell {
            .order-amount {
                font-size: 1rem;
                font-weight: 600;
            }
            
            .order-update-time {
                font-size: 0.6875rem;
                margin-top: 0.125rem;
            }
        }

        /* 状态标签移动端适配 */
        .el-tag {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* 移动端商品图片适配 */
        .product-image-small {
            width: 2rem;
            height: 2rem;
        }
        
        .product-image-large {
            width: 3rem;
            height: 3rem;
            display: block;
            margin: 0 auto;
        }

        /* 商品详情移动端适配 */
        .product-name-detail {
            font-size: 0.875rem;
            line-height: 1.3;
        }
        
        .product-specs-detail {
            font-size: 0.6875rem;
        }
        
        .product-sku-detail {
            font-size: 0.6875rem;
        }
        
        .product-price-detail {
            font-size: 0.8125rem;
        }
        
        .product-quantity-detail {
            font-size: 0.8125rem;
        }

        /* 商品操作按钮移动端适配 - 现代化设计 */
        .product-actions-detail {
            flex-direction: column !important;
            gap: 0.5rem;
            max-width: 120px;
            padding: 0.5rem 0.375rem;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border-radius: 0.5rem;
            border: 1px solid rgba(226, 232, 240, 0.5);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
            
            &:hover {
                background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
                transform: translateY(-0.5px);
            }
            
            .el-button {
                width: 100% !important;
                height: 30px !important;
                font-size: 0.75rem !important;
                font-weight: 500 !important;
                padding: 4px 6px !important;
                border-radius: 0.375rem !important;
                min-width: 85px !important;
                max-width: 110px !important;
                line-height: 1.2 !important;
                border: 1px solid transparent !important;
                transition: all 0.25s ease !important;
                position: relative !important;
                overflow: hidden !important;
                
                /* 移动端按钮光效 */
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    transition: left 0.4s ease;
                }
                
                &:hover::before {
                    left: 100%;
                }
                
                /* 移动端按钮样式继承桌面端样式，只调整阴影强度 */
                &.el-button--primary,
                &.el-button--success {
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
                    
                    &:hover {
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
                        transform: translateY(-1px) !important;
                    }
                }

            }
        }

        /* 订单操作按钮移动端适配 */
        .order-actions-cell {
            gap: 0.375rem;
            min-height: 65px;
            padding: 0.375rem 0.25rem;
        }

        .action-buttons-row {
            flex-direction: column !important;
            gap: 0.375rem;
            align-items: center !important;
            max-width: 90px;
            margin: 0 auto !important;
            
            .el-button {
                width: 100% !important;
                height: 26px !important;
                font-size: 0.6875rem !important;
                padding: 3px 5px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                min-width: 75px !important;
                max-width: 90px !important;
                box-sizing: border-box !important;
                margin: 0 !important;
                line-height: 1.2 !important;
            }
        }

        /* 展开内容移动端适配 */
        .expanded-content {
            padding: 0.5rem;
        }
        
        .product-details-table {
            font-size: 0.8125rem;
            
            .el-table__cell {
                padding: 0.375rem 0.25rem;
            }
        }

        /* 空状态移动端适配 */
        .empty-state {
            padding: 2rem 1rem;
            
            .empty-icon svg {
                width: 3rem;
                height: 3rem;
            }
            
            h3 {
                font-size: 1.125rem;
                margin: 1rem 0 0.5rem;
            }
            
            p {
                font-size: 0.875rem;
                margin-bottom: 1.5rem;
            }
        }

        /* 分页器移动端适配 */
        .pagination-container {
            margin-top: 1rem;
            
            .el-pagination {
                justify-content: center;
                
                .el-pagination__total,
                .el-pagination__jump {
                    display: none; /* 移动端隐藏总数和跳转 */
                }
            }
        }

        /* 对话框移动端适配 */
        .el-dialog {
            width: 95% !important;
            margin: 5vh auto !important;
        }
        
        .cancel-reason-form {
            .form-item {
                margin-bottom: 1rem;
                
                .form-label {
                    font-size: 0.875rem;
                    margin-bottom: 0.5rem;
                }
            }
        }
    }

    /* 移动端toggle-icon适配 */
    @media (max-width: 768px) {
        .toggle-icon {
            width: 1.75rem;
            height: 1.75rem;
            font-size: 1.125rem;
        }
        
        /* 移动端复制按钮适配 */
        .copy-order-btn {
            width: 1.25rem;
            height: 1.25rem;
            margin-left: 0.375rem;
            
            .copy-icon {
                width: 0.75rem;
                height: 0.75rem;
            }
        }
    }

    /* 超小屏幕适配 (480px以下) */
    @media (max-width: 480px) {
        .orders-container {
            padding: 0.5rem;
        }
        
        .page-header {
            .page-title {
                font-size: 1.5rem;
            }
        }
        
        .toggle-icon {
            width: 1.5rem;
            height: 1.5rem;
            font-size: 1rem;
        }
        
        /* 超小屏幕复制按钮适配 */
        .copy-order-btn {
            width: 1rem;
            height: 1rem;
            margin-left: 0.25rem;
            border-radius: 0.25rem;
            
            .copy-icon {
                width: 0.625rem;
                height: 0.625rem;
            }
        }
        
        .filters-section {
            padding: 0.75rem;
        }
        
        .orders-table {
            min-width: 700px; /* 超小屏幕减少最小宽度 */
        }
        
        .batch-operations {
            flex-direction: column;
            align-items: stretch;
            
            .el-button {
                width: 100%;
                justify-content: center;
            }
        }
        
        .quick-buttons {
            /* 超小屏幕快捷时间按钮 - 现代化美观设计 */
            .quick-btn {
                flex: 1;
                min-width: 0;
                font-size: 0.6875rem;
                padding: 0.375rem 0.625rem;
                border: 1px solid rgba(226, 232, 240, 0.6);
                border-radius: 0.5rem;
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                color: #475569;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                white-space: nowrap;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
                position: relative;
                overflow: hidden;
                min-height: 32px;
                
                /* 超小屏幕按钮光效 */
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: linear-gradient(90deg, transparent, rgba(249, 115, 22, 0.1), transparent);
                    transition: left 0.4s ease;
                }
                
                &:hover::before {
                    left: 100%;
                }

                &:hover {
                    border-color: rgba(249, 115, 22, 0.4);
                    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
                    color: #f97316;
                    transform: translateY(-0.5px);
                    box-shadow: 0 2px 6px rgba(249, 115, 22, 0.15);
                }

                &.active {
                    border-color: #ea580c;
                    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
                    color: white;
                    box-shadow: 0 2px 6px rgba(249, 115, 22, 0.3), 0 1px 3px rgba(0, 0, 0, 0.05);
                    transform: translateY(-0.5px);
                    
                    &::before {
                        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                    }
                    
                    &:hover {
                        background: linear-gradient(135deg, #ea580c 0%, #dc2626 100%);
                        box-shadow: 0 3px 8px rgba(249, 115, 22, 0.4), 0 2px 4px rgba(0, 0, 0, 0.08);
                        transform: translateY(-1px);
                    }
                }
            }
        }
        
        .date-range {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.375rem;
            flex-wrap: nowrap;
            
            .date-picker {
                width: 45%;
                min-width: 120px;
                flex-shrink: 0;
            }
            
            .date-separator {
                display: block;
                font-size: 0.75rem;
                color: #64748b;
                font-weight: 500;
                margin: 0 0.25rem;
                flex-shrink: 0;
            }
        }
    }

    /* 图片预览对话框样式 */
    .image-preview-dialog {
        .el-dialog__header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 0.5rem 0.5rem 0 0;
            padding: 1rem 1.5rem;
            
            .el-dialog__title {
                color: white;
                font-weight: 600;
            }
            
            .el-dialog__headerbtn {
                .el-dialog__close {
                    color: white;
                    font-size: 1.25rem;
                    
                    &:hover {
                        color: #f1f5f9;
                    }
                }
            }
        }
        
        .el-dialog__body {
            padding: 0;
            background: #000;
            border-radius: 0 0 0.5rem 0.5rem;
        }
    }

    .image-preview-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60vh;
        background: #000;
        border-radius: 0 0 0.5rem 0.5rem;
        padding: 1rem;
        
        .preview-image {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 0.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
            
            &:hover {
                transform: scale(1.02);
            }
        }
    }

    /* 响应式图片预览 */
    @media (max-width: 768px) {
        .image-preview-dialog {
            .el-dialog {
                width: 95% !important;
                margin: 2.5vh auto;
            }
        }
        
        .image-preview-container {
            min-height: 50vh;
            padding: 0.5rem;
            
            .preview-image {
                max-height: 60vh;
            }
        }
    }
</style>
