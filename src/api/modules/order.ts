import api from '@/api/config/axios'
import type { ApiResponse } from '@/api/types'
import { handleApiError, normalizeResponse } from '@/api/utils/apiUtils'

// 与后端 AttrJson 匹配的规格属性接口
export interface AttrJson {
    attrId?: string
    attrKey: string
    attrValue: string
    attrKeyTrans?: string
    attrValueTrans?: string
    skuImage?: string
}

// 订单预览请求参数 - 与后端OrderPreviewReq匹配
export interface OrderPreviewRequest {
    productList: {
        skuId: number
        productQuantity: number
    }[],
    isShoppingCart: number,
    shoppingCartIds: string
}

// 订单提交请求参数 - 与后端CreateOrderSubmitReq匹配
export interface OrderSubmitRequest {
    idempotentToken: string  // 必需的幂等令牌
    productList: {
        skuId: number
        productQuantity: number
    }[]
    buyerMessage?: string    // 可选的买家留言
}

// 订单预览响应 - 与后端OrderPreviewVO完全匹配，添加令牌字段
export interface OrderPreviewResponse {
    idempotentToken: string      // 幂等令牌
    tokenExpiryTime: string      // 令牌过期时间 (ISO8601格式)
    createTime: string           // 预览创建时间 (ISO8601格式)
    orderPreviewSummary: {
        totalQuantity: number
        productTypeCount: number
        success: boolean
        hasErrors: boolean
    }
    productItems: {
        spuId: number
        specId: string
        skuId: number
        productTitle: string
        productTitleEn: string
        productImageUrl: string
        skuImage: string
        skuSpecs: AttrJson[]
        unitPrice: number         // 后端BigDecimal -> JSON number
        unitPriceUsd: number      // 后端BigDecimal -> JSON number  
        orderedQuantity: number
        lineTotalAmount: number   // 后端BigDecimal -> JSON number
        lineTotalAmountUsd: number // 后端BigDecimal -> JSON number
        unitOfMeasure: string
        available: boolean
        message: string
    }[]
    priceDetails: {
        merchandiseAmount: number     // 后端BigDecimal -> JSON number
        merchandiseAmountUsd: number  // 后端BigDecimal -> JSON number
        shippingAmount: number        // 后端BigDecimal -> JSON number
        shippingAmountUsd: number     // 后端BigDecimal -> JSON number
        serviceFee: number            // 后端BigDecimal -> JSON number
        serviceFeeUsd: number         // 后端BigDecimal -> JSON number
        serviceFeeRate: number        // 后端BigDecimal -> JSON number
        discountAmount: number        // 后端BigDecimal -> JSON number
        discountAmountUsd: number     // 后端BigDecimal -> JSON number
        totalAmount: number           // 后端BigDecimal -> JSON number
        totalAmountUsd: number        // 后端BigDecimal -> JSON number
    }
    errors: {
        errorCode: string
        errorMessage: string
        offerId?: number
        specId?: string
        errorType?: string
    }[]
}

// 订单提交响应接口
export interface OrderSubmitResponse {
    success?: boolean                // 操作是否成功
    purchaseOrderNo: string         // 采购订单编号
    purchaseOrderId: number | string // 采购订单ID
    message?: string                // 响应消息

    // 完整的OrderSubmitVO字段（如果后端返回完整对象）
    submitSummary?: {
        totalQuantity: number
        productTypeCount: number
        submitTime: string
    }
    priceDetails?: {
        merchandiseAmount: number
        merchandiseAmountUsd: number
        shippingAmount: number
        shippingAmountUsd: number
        serviceFee: number
        serviceFeeUsd: number
        discountAmount: number
        discountAmountUsd: number
        totalAmount: number
        totalAmountUsd: number
    }
}

// 订单列表请求参数
export interface OrderListRequest {
    page?: number
    size?: number
    status?: number
    keyword?: string        // 支持订单号、商品名称搜索
    skuId?: string          // 专门用于SKU ID搜索
    productName?: string    // 商品名称搜索
    consigneeName?: string  // 收货人姓名搜索
    startTime?: string
    endTime?: string
}

// 分页响应接口
export interface PageDTO<T> {
    records: T[]
    total: number
    pageIndex: number
    pageSize: number
}

// 用户订单列表项 - 与后端UserPurchaseOrderListVO匹配
export interface UserPurchaseOrderListVO {
    id: number                       // 采购单ID
    orderNo: string                  // 订单号
    orderStatus: number              // 订单状态枚举值
    orderStatusText: string          // 订单状态显示名称
    orderStatusDescription: string   // 订单状态描述
    orderStatusIcon: string          // 订单状态图标
    orderStatusColor: string         // 订单状态颜色
    orderStatusTagType: string       // 订单状态标签类型
    progressPercentage: number       // 订单进度百分比
    totalAmount: number              // 总金额
    totalAmountUsd: number           // 美元总金额
    shippingFee: number              // 运费
    shippingFeeUsd: number           // 美元运费
    serviceFeeRate: number           // 服务费率
    currency: string                 // 币种
    totalQuantity: number            // 订单商品总数量
    productTypeCount: number         // 商品种类数
    createTime: string               // 创建时间 (ISO8601格式)
    updateTime: string               // 更新时间 (ISO8601格式)
    payTime?: string                 // 支付时间 (ISO8601格式)
    deliveryTime?: string            // 发货时间 (ISO8601格式)
    completedTime?: string           // 完成时间 (ISO8601格式)
    mainProductTitle: string         // 主要商品标题
    mainProductTitleEn: string       // 主要商品英文标题
    mainProductImageUrl: string      // 主要商品图片
    buyerMessage?: string            // 买家留言

    // 操作权限字段
    canCancel: boolean               // 可否取消
    canPay: boolean                  // 可否支付
    canConfirmReceipt: boolean       // 可否确认收货
    canApplyRefund: boolean          // 可否申请退款
    canViewTracking: boolean         // 可否查看物流

    // 订单商品信息（预览用）
    orderItems?: OrderItemInfo[]     // 订单商品信息
}

// 订单商品信息
export interface OrderItemInfo {
    id: string                       // 订单项ID
    purchaseOrderId: number          // 采购订单ID
    supplierOrderId: number          // 供应商订单ID
    productId: number                // 商品ID
    skuId: number                    // SKU ID
    productImage: string             // 商品图片
    productTitle: string             // 商品标题
    productTitleEn: string           // 商品标题(英文)
    skuSpecs: AttrJson[]             // 商品规格
    orderedQuantity: number          // 商品数量
    unitOfMeasure: string            // 计量单位
    unitPrice: number                // 商品单价
    unitPriceUsd: number             // 商品单价(美元)
    lineTotalAmount: number          // 行总金额
    lineTotalAmountUsd: number       // 行总金额(美元)
    itemStatus: string               // 商品状态
    itemStatusName: string           // 商品状态描述
    available: boolean               // 商品是否可用
    message: string                  // 商品消息
}

export const orderApi = {
    /**
     * 订单预览
     * @param data 订单预览请求参数
     * @returns 订单预览响应数据（包含幂等令牌）
     */
    async previewOrder(data: OrderPreviewRequest): Promise<OrderPreviewResponse> {
        try {
            const response = await api.post<ApiResponse<OrderPreviewResponse>>('/api/orders/preview', data)
            return normalizeResponse<OrderPreviewResponse>(response.data)
        } catch (error) {
            handleApiError(error, 'order preview failed')
        }
    },

    /**
     * 提交订单
     * @param data 订单提交请求参数（包含幂等令牌）
     * @returns 订单提交响应数据
     */
    async submitOrder(data: OrderSubmitRequest): Promise<OrderSubmitResponse> {
        console.log('API请求数据:', data)

        const response = await api.post<ApiResponse<OrderSubmitResponse>>('/api/orders/submit', data)
        console.log('API原始响应:', response.data)

        const result = normalizeResponse<OrderSubmitResponse>(response.data)
        console.log('处理后的结果:', result)

        return result
    },

    /**
     * 根据令牌查询预览信息
     * @param idempotentToken 幂等令牌
     * @returns 订单预览响应数据
     */
    async getPreviewByToken(idempotentToken: string): Promise<OrderPreviewResponse> {
        try {
            const response = await api.get<ApiResponse<OrderPreviewResponse>>(`/api/orders/preview/${idempotentToken}`)
            return normalizeResponse<OrderPreviewResponse>(response.data)
        } catch (error) {
            handleApiError(error, 'get preview by token failed')
        }
    },

    /**
     * 获取订单列表
     * @param params 查询参数
     * @returns 分页订单列表
     */
    async getOrderList(params: OrderListRequest = {}): Promise<PageDTO<UserPurchaseOrderListVO>> {
        try {
            const { page = 1, size = 10, status, keyword, skuId, startTime, endTime } = params

            const queryParams = new URLSearchParams()

            queryParams.append('page', page.toString())
            queryParams.append('size', size.toString())

            if (status !== undefined) {
                queryParams.append('status', status.toString())
            }

            if (keyword) {
                queryParams.append('keyword', keyword)
            }

            if (skuId) {
                queryParams.append('skuId', skuId)
            }

            if (startTime) {
                queryParams.append('startTime', startTime)
            }

            if (endTime) {
                queryParams.append('endTime', endTime)
            }

            const requestUrl = `/api/orders?${queryParams.toString()}`

            const response = await api.get<ApiResponse<PageDTO<UserPurchaseOrderListVO>>>(requestUrl)
            return normalizeResponse<PageDTO<UserPurchaseOrderListVO>>(response.data)
        } catch (error) {
            handleApiError(error, 'get order list failed')
        }
    },

    /**
     * 获取订单详情
     * @param orderNo 订单号
     * @returns 订单详情
     */
    async getOrderDetail(orderNo: string): Promise<any> {
        try {
            const response = await api.get<ApiResponse<any>>(`/api/orders/${orderNo}`)
            return normalizeResponse<any>(response.data)
        } catch (error) {
            handleApiError(error, 'get order detail failed')
        }
    },

    /**
     * 取消订单
     * @param orderNo 订单号
     * @param reason 取消原因
     */
    async cancelOrder(orderNo: string, reason?: string): Promise<void> {
        try {
            await api.post(`/api/orders/${orderNo}/cancel`, { reason })
        } catch (error) {
            handleApiError(error, 'cancel order failed')
        }
    },

    /**
     * 确认收货
     * @param orderNo 订单号
     */
    async confirmReceipt(orderNo: string): Promise<void> {
        try {
            await api.post(`/api/orders/${orderNo}/confirm-receipt`, {})
        } catch (error) {
            handleApiError(error, 'confirm receipt failed')
        }
    },

    /**
     * 申请退款
     * @param orderNo 订单号
     * @param data 退款申请数据
     */
    async applyRefund(orderNo: string, data: any): Promise<any> {
        try {
            const response = await api.post<ApiResponse<any>>(`/api/orders/${orderNo}/apply-refund`, data)
            return normalizeResponse<any>(response.data)
        } catch (error) {
            handleApiError(error, 'apply refund failed')
        }
    },

    /**
     * 获取物流追踪
     * @param orderNo 订单号
     */
    async getOrderTracking(orderNo: string): Promise<any> {
        try {
            const response = await api.get<ApiResponse<any>>(`/api/orders/${orderNo}/tracking`)
            return normalizeResponse<any>(response.data)
        } catch (error) {
            handleApiError(error, 'get order tracking failed')
        }
    },

    /**
     * 创建订单 (废弃的接口，保持向后兼容)
     * @deprecated 请使用 submitOrder 替代
     */
    async createOrder(data: any): Promise<any> {
        console.warn('createOrder is deprecated, please use submitOrder instead')
        return this.submitOrder(data)
    },

    /**
     * 支付订单
     * @param orderNo 订单号
     * @param reason 支付原因
     */
    async payOrder(orderNo: string, reason?: string): Promise<void> {
        try {
            await api.post(`/api/orders/${orderNo}/pay`, { reason })
        } catch (error) {
            handleApiError(error, 'pay order failed')
        }
    }
}