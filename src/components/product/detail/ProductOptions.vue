<template>
    <div class="product-options space-y-6 mt-6">
        <!-- Toast提示 -->
        <div v-if="showToast" class="fixed top-20 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white px-6 py-3 rounded-lg shadow-lg z-50 toast-message">
            <div class="flex items-center space-x-2">
                <svg v-if="toastType === 'success'" class="w-5 h-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <svg v-if="toastType === 'warning'" class="w-5 h-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                    ></path>
                </svg>
                <span>{{ toastMessage }}</span>
            </div>
        </div>

        <!-- 主要SKU选项 -->
        <template v-if="!isNoSpecProduct() && attributes.length > 0">
            <div v-for="(attribute, attrIndex) in attributes.slice(0, -1)" :key="attribute.id" class="mb-6">
                <h3 class="font-medium text-gray-700 mb-3">{{ getAttributeDisplayName(attribute) }}</h3>

                <!-- 所有规格都使用滑动卡片UI -->
                <div class="sku-cards-scroller">
                    <div class="sku-cards-scroller-crop">
                        <div class="sku-cards-scroller-content" ref="scrollContentRef">
                            <div class="sku-cards-scroller-platter" role="list">
                                <div
                                    v-for="value in attribute.values"
                                    :key="value.id"
                                    @click="selectMainOption(attribute.id, value.id)"
                                    class="sku-cards-scroller-item"
                                    role="listitem"
                                >
                                    <div
                                        class="relative cursor-pointer transition-all border p-3 rounded-lg flex flex-col items-center justify-center h-full"
                                        :class="[
                                            isOptionSelected(attribute.id, value.id)
                                                ? 'border-orange-500 bg-orange-50 shadow-md'
                                                : 'border-gray-200 hover:border-gray-400 hover:shadow-sm',
                                        ]"
                                    >
                                        <div
                                            v-if="getOptionTotalQuantity(attribute.id, value.id) > 0"
                                            class="absolute -top-2 -right-2 bg-orange-500 text-white rounded-full min-w-[22px] h-[22px] flex items-center justify-center text-xs font-bold z-10"
                                        >
                                            ×{{ getOptionTotalQuantity(attribute.id, value.id) }}
                                        </div>
                                        <div v-if="value.image" class="w-16 h-16 md:w-20 md:h-20 overflow-hidden rounded-md mb-2">
                                            <img :src="value.image" :alt="getValueDisplayName(value)" class="w-full h-full object-cover" />
                                        </div>
                                        <span class="text-sm text-center text-gray-700 h-10 flex items-center justify-center">{{ getValueDisplayName(value) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 导航箭头 -->
                    <div class="sku-paddlenav" v-if="attribute.values.length > 3">
                        <button type="button" class="sku-paddlenav-arrow sku-paddlenav-arrow-previous" :disabled="skuScrollPosition <= 0" @click="skuScrollTo('previous')">
                            <span>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36">
                                    <path
                                        d="M21.559,12.062 L15.618,17.984 L21.5221,23.944 C22.105,24.533 22.1021,25.482 21.5131,26.065 C21.2211,26.355 20.8391,26.4999987 20.4571,26.4999987 C20.0711,26.4999987 19.6851,26.352 19.3921,26.056 L12.4351,19.034 C11.8531,18.446 11.8551,17.4999987 12.4411,16.916 L19.4411,9.938 C20.0261,9.353 20.9781,9.354 21.5621,9.941 C22.1471,10.528 22.1451,11.478 21.5591,12.062 Z"
                                    ></path>
                                </svg>
                            </span>
                        </button>
                        <button type="button" class="sku-paddlenav-arrow sku-paddlenav-arrow-next" :disabled="skuScrollPosition >= skuMaxScroll" @click="skuScrollTo('next')">
                            <span>
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36">
                                    <path
                                        d="M23.5587,16.916 C24.1447,17.4999987 24.1467,18.446 23.5647,19.034 L16.6077,26.056 C16.3147,26.352 15.9287,26.4999987 15.5427,26.4999987 C15.1607,26.4999987 14.7787,26.355 14.4867,26.065 C13.8977,25.482 13.8947,24.533 14.4777,23.944 L20.3818,17.984 L14.4408,12.062 C13.8548,11.478 13.8528,10.5279 14.4378,9.941 C15.0218,9.354 15.9738,9.353 16.5588,9.938 L23.5588,16.916 Z"
                                    ></path>
                                </svg>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </template>

        <!-- 无规格商品的简化购买界面 -->
        <div v-if="isNoSpecProduct()" class="mb-6">
            <h3 class="font-medium text-gray-700 mb-3">{{ t('product.detail.purchaseQuantity', '购买数量') }}</h3>
            <div class="bg-white border rounded-lg p-4">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div class="flex flex-col">
                        <div class="text-lg font-semibold text-red-600 mb-1">{{ formatPriceWithSymbol(getNoSpecProductPrice()) }}</div>
                        <div class="text-sm text-gray-500">{{ getNoSpecProductStock() }} {{ t('product.detail.available', '件可售') }}</div>
                    </div>
                    <div class="flex items-center gap-3">
                        <button
                            @click="decreaseNoSpecQuantity()"
                            class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                            :disabled="getNoSpecProductStock() <= 0 || noSpecQuantity <= 0"
                        >
                            <i class="fas fa-minus text-sm"></i>
                        </button>
                        <input
                            type="number"
                            v-model.number="noSpecQuantity"
                            class="w-20 h-10 text-center border border-gray-300 rounded-lg"
                            min="0"
                            :max="getNoSpecProductStock()"
                            :disabled="getNoSpecProductStock() <= 0"
                        />
                        <button
                            @click="increaseNoSpecQuantity()"
                            class="w-10 h-10 flex items-center justify-center border border-gray-300 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                            :disabled="getNoSpecProductStock() <= 0 || noSpecQuantity >= getNoSpecProductStock()"
                        >
                            <i class="fas fa-plus text-sm"></i>
                        </button>
                        <button
                            v-if="noSpecQuantity === 0 && minQuantity > 1"
                            @click="setNoSpecMinQuantity()"
                            class="px-3 py-2 border border-orange-500 text-orange-600 rounded-lg bg-orange-50 text-sm hover:bg-orange-100 transition-colors"
                            :disabled="getNoSpecProductStock() <= 0"
                        >
                            x{{ minQuantity }}
                        </button>
                    </div>
                </div>
                <div v-if="noSpecQuantity > 0" class="mt-4 pt-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-600">
                            <span class="font-medium">{{ t('product.detail.totalQuantity', '总数量') }}:</span>
                            <span class="text-orange-600 font-semibold ml-1">{{ noSpecQuantity }}</span>
                        </div>
                        <div class="text-lg font-semibold text-red-600">
                            <span class="font-medium">{{ t('product.detail.totalAmount', '总金额') }}:</span>
                            <span class="ml-1">{{ formatPriceWithSymbol(getNoSpecProductPrice() * noSpecQuantity) }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最后一个属性表格 -->
        <div v-else-if="getTableAttribute()" class="mb-6">
            <h3 class="font-medium text-gray-700 mb-3">{{ getAttributeDisplayName(getTableAttribute()!) }}</h3>
            <div class="relative">
                <div v-if="allTableSkusOutOfStock" class="absolute inset-0 bg-gray-100 bg-opacity-80 flex items-center justify-center z-10">
                    <span class="text-lg text-gray-500 font-bold">无库存</span>
                </div>
                
                <!-- PC端表格显示 -->
                <div class="hidden md:block border rounded-lg overflow-hidden">
                    <div class="bg-gray-50 py-3 px-4 text-sm font-medium text-gray-700 sticky top-0 z-10 border-b">{{ getAttributeDisplayName(getTableAttribute()!) }}</div>
                    <div class="spec-options-container" :class="{ 'spec-scroll-container': getTableAttribute()!.values.length > maxVisibleSpecOptions }">
                        <div
                            v-for="value in getTableAttribute()!.values"
                            :key="value.id"
                            class="px-4 py-3 border-b text-sm hover:bg-gray-50 transition-all"
                            :class="[getQuantity(value.id) > 0 ? 'bg-orange-50' : '', getOptionStock(value.id) <= 0 ? 'opacity-60 bg-gray-100 cursor-not-allowed' : '']"
                        >
                            <div class="flex items-center justify-between">
                                <!-- 左侧：图片 + 规格信息 -->
                                <div class="flex items-center space-x-4">
                                    <!-- 规格图片 (仅单规格时显示) -->
                                    <div v-if="value.image && attributes.length === 1" class="w-16 h-16 overflow-hidden rounded-lg flex-shrink-0 border border-gray-200">
                                        <img :src="value.image" :alt="getValueDisplayName(value)" class="w-full h-full object-cover" />
                                    </div>
                                    <!-- 规格信息 -->
                                    <div class="flex flex-col">
                                        <div class="font-medium text-gray-800 text-base">{{ getValueDisplayName(value) }}</div>
                                        <div class="text-sm text-gray-500 mt-1">{{ getOptionStock(value.id) }} {{ t('product.detail.available', '件可售') }}</div>
                                    </div>
                                </div>

                                <!-- 右侧：价格 + 数量控制 -->
                                <div class="flex flex-col items-end space-y-3">
                                    <!-- 价格 -->
                                    <div class="text-lg font-semibold text-red-600">{{ formatPriceWithSymbol(getOptionPrice(value.id)) }}</div>
                                    <!-- 数量控制器 -->
                                    <div class="flex items-center">
                                        <button
                                            @click="decreaseQuantity(value.id)"
                                            class="px-3 py-1 border border-gray-300 rounded-l-md bg-gray-100 hover:bg-gray-200"
                                            :disabled="getOptionStock(value.id) <= 0 || getQuantity(value.id) <= 0"
                                        >
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <input
                                            type="number"
                                            :value="getQuantity(value.id)"
                                            @input="updateQuantity(value.id, $event)"
                                            class="w-16 text-center border-t border-b border-gray-300 py-1"
                                            min="0"
                                            :max="getOptionStock(value.id)"
                                            :disabled="getOptionStock(value.id) <= 0"
                                        />
                                        <button
                                            @click="increaseQuantity(value.id)"
                                            class="px-3 py-1 border border-gray-300 rounded-r-md bg-gray-100 hover:bg-gray-200"
                                            :disabled="getOptionStock(value.id) <= 0 || getQuantity(value.id) >= getOptionStock(value.id)"
                                        >
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button
                                            v-if="getQuantity(value.id) === 0 && minQuantity > 1"
                                            @click="setMinQuantity(value.id)"
                                            class="ml-2 px-2 py-1 border border-orange-500 text-orange-600 rounded bg-orange-50 text-xs hover:bg-orange-100"
                                            :disabled="getOptionStock(value.id) <= 0"
                                        >
                                            x{{ minQuantity }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 移动端垂直滚动表格显示 -->
                <div class="md:hidden border rounded-lg overflow-hidden">
                    <div class="bg-gray-50 py-3 px-4 text-sm font-medium text-gray-700 sticky top-0 z-10 border-b">{{ getAttributeDisplayName(getTableAttribute()!) }}</div>
                    <div class="spec-options-container" :class="{ 'spec-scroll-container': getTableAttribute()!.values.length > maxVisibleSpecOptions }">
                        <div
                            v-for="value in getTableAttribute()!.values"
                            :key="value.id"
                            class="px-4 py-3 border-b text-sm hover:bg-gray-50 transition-all"
                            :class="[getQuantity(value.id) > 0 ? 'bg-orange-50' : '', getOptionStock(value.id) <= 0 ? 'opacity-60 bg-gray-100 cursor-not-allowed' : '']"
                        >
                            <div class="flex items-center justify-between">
                                <!-- 左侧：图片 + 规格信息 -->
                                <div class="flex items-center space-x-3">
                                    <!-- 规格图片 (仅单规格时显示) -->
                                    <div v-if="value.image && attributes.length === 1" class="w-12 h-12 overflow-hidden rounded-lg flex-shrink-0 border border-gray-200">
                                        <img :src="value.image" :alt="getValueDisplayName(value)" class="w-full h-full object-cover" />
                                    </div>
                                    <!-- 规格信息 -->
                                    <div class="flex flex-col">
                                        <div class="font-medium text-gray-800 text-sm">{{ getValueDisplayName(value) }}</div>
                                        <div class="text-xs text-gray-500 mt-1">{{ getOptionStock(value.id) }} {{ t('product.detail.available', '件可售') }}</div>
                                    </div>
                                </div>

                                <!-- 右侧：价格 + 数量控制 -->
                                <div class="flex flex-col items-end space-y-2">
                                    <!-- 价格 -->
                                    <div class="text-base font-semibold text-red-600">{{ formatPriceWithSymbol(getOptionPrice(value.id)) }}</div>
                                    <!-- 数量控制器 -->
                                    <div class="flex items-center">
                                        <button
                                            @click="decreaseQuantity(value.id)"
                                            class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                                            :disabled="getOptionStock(value.id) <= 0 || getQuantity(value.id) <= 0"
                                        >
                                            <i class="fas fa-minus text-xs"></i>
                                        </button>
                                        <input
                                            type="number"
                                            :value="getQuantity(value.id)"
                                            @input="updateQuantity(value.id, $event)"
                                            class="w-14 h-8 text-center border border-gray-300 rounded-lg text-xs"
                                            min="0"
                                            :max="getOptionStock(value.id)"
                                            :disabled="getOptionStock(value.id) <= 0"
                                        />
                                        <button
                                            @click="increaseQuantity(value.id)"
                                            class="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors"
                                            :disabled="getOptionStock(value.id) <= 0 || getQuantity(value.id) >= getOptionStock(value.id)"
                                        >
                                            <i class="fas fa-plus text-xs"></i>
                                        </button>
                                        <button
                                            v-if="getQuantity(value.id) === 0 && minQuantity > 1"
                                            @click="setMinQuantity(value.id)"
                                            class="ml-2 px-2 py-1 border border-orange-500 text-orange-600 rounded bg-orange-50 text-xs hover:bg-orange-100"
                                            :disabled="getOptionStock(value.id) <= 0"
                                        >
                                            x{{ minQuantity }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="grid grid-cols-1 sm:grid-cols-5 gap-3 mt-6">
            <button
                @click="handleAddToFavorites"
                class="sm:col-span-1 w-full flex items-center justify-center border border-gray-300 rounded-lg px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-100 transition-colors"
            >
                <i :class="isFavorited ? 'fas fa-heart text-red-500' : 'far fa-heart'"></i>
            </button>
            <button
                @click="handleAddToCart"
                class="sm:col-span-2 w-full flex items-center justify-center bg-orange-500 text-white rounded-lg px-4 py-3 font-medium hover:bg-orange-600 transition-colors relative"
            >
                <i class="fas fa-shopping-cart"></i>
                <span class="hidden lg:inline ml-2">{{ t('product.detail.addToCart', '添加到购物车') }}</span>
            </button>
            <button
                @click="handleBuyNow"
                class="sm:col-span-2 w-full flex items-center justify-center bg-red-600 text-white rounded-lg px-4 py-3 font-medium hover:bg-red-700 transition-colors"
                :disabled="props.isBuyNowLoading"
            >
                <span v-if="props.isBuyNowLoading" class="loading-spinner-sm mr-2"></span>
                {{ props.isBuyNowLoading ? t('common.processing') : t('product.detail.buyNow', '立即下单') }}
            </button>
        </div>

        <!-- 购物车折叠面板 -->
        <div v-if="cartItems.length > 0" class="mt-6 border rounded-md overflow-hidden">
            <div class="bg-gray-50 px-4 py-3 flex justify-between items-center cursor-pointer" @click="toggleCartExpand">
                <!-- 桌面端显示：图标版本 -->
                <div class="hidden md:flex items-center space-x-6 text-gray-700">
                    <!-- 总数量 -->
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-shopping-basket text-blue-600"></i>
                        <span class="font-semibold text-lg">{{ getTotalQuantity() }}</span>
                    </div>
                    <!-- 总金额 -->
                    <div class="flex items-center space-x-2">
                        <!-- <i class="fas fa-dollar-sign text-green-600"></i> -->
                        <span class="font-semibold text-lg text-red-600">{{ getTotalAmount() }}</span>
                    </div>
                    <!-- 规格数量 -->
                    <div v-if="cartItems.length > 1" class="flex items-center space-x-2">
                        <i class="fas fa-layer-group text-orange-600"></i>
                        <span class="font-medium text-orange-600">{{ cartItems.length }}</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <button class="text-gray-600 focus:outline-none"><i :class="cartExpanded ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i></button>
                </div>
            </div>
            <div v-show="cartExpanded">
                <!-- 桌面端表格显示 -->
                <div class="hidden md:block">
                    <div class="cart-scroll-container cart-table-container">
                        <table class="w-full text-sm">
                            <thead class="bg-gray-50 sticky top-0 z-10">
                                <tr>
                                    <th class="px-4 py-3 text-left">{{ t('product.detail.specification', '规格') }}</th>
                                    <th class="px-4 py-3 text-center">{{ t('product.detail.price', '单价') }}</th>
                                    <th class="px-4 py-3 text-center">{{ t('product.detail.quantity', '数量') }}</th>
                                    <th class="px-4 py-3 text-right">{{ t('product.detail.actions', '操作') }}</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                <tr v-for="(item, index) in cartItems" :key="index" class="bg-white hover:bg-gray-50">
                                    <td class="px-4 py-3 text-left">
                                        <span class="font-medium text-gray-800">{{ getSkuValueDisplayNames(item.options) }}</span>
                                    </td>
                                    <td class="px-4 py-3 text-center">{{ formatPriceWithSymbol(getItemPrice(item)) }}</td>
                                    <td class="px-4 py-3 text-center">{{ item.quantity }}</td>
                                    <td class="px-4 py-3 text-right">
                                        <button @click="removeCartItem(index)" class="text-red-600 hover:text-red-800 transition-colors"><i class="fas fa-trash"></i></button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="bg-gray-50 px-4 py-3 flex justify-between items-center">
                    <button @click="clearCart" class="border border-gray-300 text-gray-700 px-3 py-2 text-sm rounded-md hover:bg-gray-100 transition-colors">
                        {{ t('product.detail.clearCart', '清空') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import type { ProductDetailVO, ProductSkuInfoVO } from '@/api/modules/product'
    import { formatPriceWithSymbol, getCurrentPrice } from '@/utils/priceUtils'
    import { computed, onMounted, onUnmounted, ref, watch, watchEffect } from 'vue'
    import { useI18n } from 'vue-i18n'

    // i18n
    const { t, locale } = useI18n()

    // Props
    const props = defineProps<{
        product: ProductDetailVO
        selectedOptions?: Record<string, string>
        isBuyNowLoading: boolean
    }>()

    // Emits
    const emit = defineEmits<{
        (e: 'update:selectedOptions', value: Record<string, string>): void
        (e: 'addToCart', cartItems: Array<{ options: Record<string, string>; quantity: number; skuId: number }>): void
        (e: 'addToFavorites'): void
        (e: 'buyNow', buyNowData: Array<{ options: Record<string, string>; quantity: number; skuId: number }>): void
        (e: 'skuImageSelected', imageUrl: string | null): void
    }>()

    // ========== 本地响应式状态 ==========
    const localSelectedOptions = ref<Record<string, string>>(props.selectedOptions || {})
    const optionQuantities = ref<Record<string, number>>({})
    const cartItems = ref<Array<{ options: Record<string, string>; quantity: number; sku: ProductSkuInfoVO }>>([])
    const cartExpanded = ref(false)
    const noSpecQuantity = ref(0)
    const showToast = ref(false)
    const toastMessage = ref('')
    const toastType = ref<'success' | 'warning'>('success')
    const isFavorited = ref(false)

    // ========== 计算属性 ==========
    const useChineseDisplay = computed(() => locale.value.startsWith('zh'))
    const minQuantity = computed(() => props.product.minOrderQuantity || 1)
    const maxVisibleSpecOptions = computed(() => (attributes.value.length > 1 ? 6 : 10))

    // ========== 核心数据结构转换 ==========
    interface SkuAttribute {
        id: string
        name: string
        nameTrans?: string
        values: SkuValue[]
    }
    interface SkuValue {
        id: string
        name: string
        nameTrans?: string
        image?: string
    }

    const derivedSkuData = computed(() => {
        if (!props.product?.productSkuInfos) {
            return { attributes: [] as SkuAttribute[], skuMap: new Map<string, ProductSkuInfoVO>() }
        }
        const attrMap = new Map<string, { id: string; name: string; nameTrans?: string; values: Map<string, SkuValue> }>()
        const newSkuMap = new Map<string, ProductSkuInfoVO>()
        for (const sku of props.product.productSkuInfos) {
            if (!sku.specs || sku.specs.length === 0) continue
            const skuKey = sku.specs
                .map(spec => `${spec.specName}:${spec.specValue}`)
                .sort()
                .join('|')
            newSkuMap.set(skuKey, sku)
            for (const spec of sku.specs) {
                if (!attrMap.has(spec.specName)) {
                    attrMap.set(spec.specName, { id: spec.specName, name: spec.specName, nameTrans: spec.specNameEn, values: new Map() })
                }
                const attr = attrMap.get(spec.specName)!
                if (!attr.values.has(spec.specValue)) {
                    attr.values.set(spec.specValue, { id: spec.specValue, name: spec.specValue, nameTrans: spec.specValueEn, image: sku.image })
                }
            }
        }
        const finalAttributes: SkuAttribute[] = Array.from(attrMap.values()).map(a => ({ ...a, values: Array.from(a.values.values()) }))
        return { attributes: finalAttributes, skuMap: newSkuMap }
    })

    const attributes = computed(() => derivedSkuData.value.attributes)
    const skuMap = computed(() => derivedSkuData.value.skuMap)

    // ========== UI渲染辅助函数 ==========
    const isNoSpecProduct = () => !attributes.value || attributes.value.length === 0
    const getTableAttribute = () => (attributes.value.length > 0 ? attributes.value[attributes.value.length - 1] : null)
    const getAttributeDisplayName = (attribute: SkuAttribute) => (useChineseDisplay.value ? attribute.name : attribute.nameTrans || attribute.name)
    const getValueDisplayName = (value: SkuValue) => (useChineseDisplay.value ? value.name : value.nameTrans || value.name)

    // ========== 核心业务逻辑 ==========
    const findSkuByOptions = (options: Record<string, string>): ProductSkuInfoVO | null => {
        if (Object.keys(options).length < attributes.value.length) return null
        const skuKey = Object.entries(options)
            .map(([key, value]) => `${key}:${value}`)
            .sort()
            .join('|')
        return skuMap.value.get(skuKey) || null
    }

    const getOptionStock = (valueId: string): number => {
        const tableAttr = getTableAttribute()
        if (!tableAttr) return 0
        const options = { ...localSelectedOptions.value, [tableAttr.id]: valueId }
        const sku = findSkuByOptions(options)
        return sku?.amountOnSale || 0
    }

    const getOptionPrice = (valueId: string): number => {
        const tableAttr = getTableAttribute()
        const options = { ...localSelectedOptions.value, ...(tableAttr && { [tableAttr.id]: valueId }) }
        const sku = findSkuByOptions(options)
        const priceData = {
            price: sku?.fenxiaoPriceInfo?.onePiecePrice
                ? parseFloat(sku.fenxiaoPriceInfo.onePiecePrice)
                : parseFloat(String(sku?.consignPrice || sku?.price || props.product.price)),
            usdPrice: parseFloat(String(sku?.usdPrice || props.product.usdPrice)),
        }
        return getCurrentPrice(priceData)
    }

    const getQuantity = (valueId: string): number => {
        if (optionQuantities.value[valueId] !== undefined) return optionQuantities.value[valueId]
        const tableAttr = getTableAttribute()
        if (!tableAttr) return 0
        const options = { ...localSelectedOptions.value, [tableAttr.id]: valueId }
        const skuKey = Object.entries(options)
            .map(([key, value]) => `${key}:${value}`)
            .sort()
            .join('|')
        const existingItem = cartItems.value.find(item => {
            const itemKey = Object.entries(item.options)
                .map(([key, value]) => `${key}:${value}`)
                .sort()
                .join('|')
            return itemKey === skuKey
        })
        return existingItem ? existingItem.quantity : 0
    }

    // ========== 事件处理 ==========
    const isOptionSelected = (attributeId: string, valueId: string) => localSelectedOptions.value[attributeId] === valueId

    const selectMainOption = (attributeId: string, valueId: string) => {
        const isChanging = localSelectedOptions.value[attributeId] && localSelectedOptions.value[attributeId] !== valueId
        localSelectedOptions.value = { ...localSelectedOptions.value, [attributeId]: valueId }

        // 当选择第一个（带图片的）规格时，发出事件
        const firstAttribute = attributes.value[0]
        if (firstAttribute && attributeId === firstAttribute.id) {
            const selectedValue = firstAttribute.values.find(v => v.id === valueId)
            emit('skuImageSelected', selectedValue?.image || null)
        }

        if (isChanging) {
            optionQuantities.value = {}
            setTimeout(() => {
                getTableAttribute()?.values.forEach(v => {
                    const q = getQuantity(v.id)
                    if (q > 0) optionQuantities.value[v.id] = q
                })
            }, 30)
        }
        emit('update:selectedOptions', localSelectedOptions.value)
    }

    const updateQuantity = (valueId: string, event: Event) => {
        const value = parseInt((event.target as HTMLInputElement).value)
        const stock = getOptionStock(valueId)
        const newQuantity = !isNaN(value) && value >= 0 ? Math.min(value, stock) : 0
        optionQuantities.value[valueId] = newQuantity
        updateCartForOption(valueId, newQuantity)
    }

    const increaseQuantity = (valueId: string) => {
        if (attributes.value.length > 1 && Object.keys(localSelectedOptions.value).length < attributes.value.length - 1) {
            showWarning(t('product.detail.selectPreviousOption', '请先选择前置规格'))
            return
        }
        const currentQty = optionQuantities.value[valueId] || 0
        const stock = getOptionStock(valueId)
        if (currentQty < stock) {
            const newQuantity = currentQty + 1
            optionQuantities.value[valueId] = newQuantity
            updateCartForOption(valueId, newQuantity)
        }
    }

    const decreaseQuantity = (valueId: string) => {
        const currentQty = optionQuantities.value[valueId] || 0
        if (currentQty > 0) {
            const newQuantity = currentQty - 1
            optionQuantities.value[valueId] = newQuantity
            updateCartForOption(valueId, newQuantity)
        }
    }

    const setMinQuantity = (valueId: string) => {
        if (attributes.value.length > 1 && Object.keys(localSelectedOptions.value).length < attributes.value.length - 1) {
            showWarning(t('product.detail.selectPreviousOption', '请先选择前置规格'))
            return
        }
        const stock = getOptionStock(valueId)
        if (minQuantity.value <= stock) {
            optionQuantities.value[valueId] = minQuantity.value
            updateCartForOption(valueId, minQuantity.value)
        } else {
            showWarning(t('product.detail.insufficientStock', { count: minQuantity.value }))
        }
    }

    // ========== 购物车逻辑 ==========
    const updateCartForOption = (valueId: string, quantity: number) => {
        const tableAttr = getTableAttribute()
        if (!tableAttr) return
        const options = { ...localSelectedOptions.value, [tableAttr.id]: valueId }
        if (Object.keys(options).length < attributes.value.length) return
        const sku = findSkuByOptions(options)
        if (!sku) return
        const skuKey = Object.entries(options)
            .map(([key, value]) => `${key}:${value}`)
            .sort()
            .join('|')
        const existingIndex = cartItems.value.findIndex(item => {
            const itemKey = Object.entries(item.options)
                .map(([key, value]) => `${key}:${value}`)
                .sort()
                .join('|')
            return itemKey === skuKey
        })
        if (quantity > 0) {
            if (existingIndex !== -1) cartItems.value[existingIndex].quantity = quantity
            else cartItems.value.push({ options, quantity, sku })
        } else {
            if (existingIndex !== -1) cartItems.value.splice(existingIndex, 1)
        }
        saveCartToStorage()
    }

    const getOptionTotalQuantity = (attributeId: string, valueId: string) => {
        return cartItems.value.filter(item => item.options[attributeId] === valueId).reduce((sum, item) => sum + item.quantity, 0)
    }

    const getSkuValueDisplayNames = (options: Record<string, string>): string => {
        const parts: string[] = []
        if (attributes.value && attributes.value.length > 0) {
            for (const [attrId, valueId] of Object.entries(options)) {
                const attribute = attributes.value.find(a => a.id === attrId)
                const value = attribute?.values.find(v => v.id === valueId)
                if (value) {
                    parts.push(getValueDisplayName(value))
                }
            }
        }
        return parts.join(', ')
    }

    const getItemPrice = (item: { sku: ProductSkuInfoVO }) =>
        getCurrentPrice({
            price: item.sku.fenxiaoPriceInfo?.onePiecePrice ? parseFloat(item.sku.fenxiaoPriceInfo.onePiecePrice) : parseFloat(String(item.sku.consignPrice || item.sku.price)),
            usdPrice: parseFloat(String(item.sku.usdPrice)),
        })
    const removeCartItem = (index: number) => {
        const item = cartItems.value[index]
        if (!item) return
        const tableAttr = getTableAttribute()
        if (tableAttr) {
            const valueId = item.options[tableAttr.id]
            if (valueId) optionQuantities.value[valueId] = 0
        }
        cartItems.value.splice(index, 1)
        saveCartToStorage()
    }
    const getTotalQuantity = () => cartItems.value.reduce((total, item) => total + item.quantity, 0)
    const getTotalAmount = () => formatPriceWithSymbol(Number(cartItems.value.reduce((sum, item) => sum + getItemPrice(item) * item.quantity, 0).toFixed(2)))
    const clearCart = () => {
        cartItems.value = []
        optionQuantities.value = {}
        clearCartStorage()
    }
    const submitCart = () => {
        const itemsToEmit = isNoSpecProduct()
            ? noSpecQuantity.value > 0 && getNoSpecProductSku()
                ? [{ options: {}, quantity: noSpecQuantity.value, skuId: getNoSpecProductSku()!.skuId }]
                : []
            : cartItems.value.map(item => ({
                  options: item.options,
                  quantity: item.quantity,
                  skuId: item.sku.skuId,
                  specs: item.sku.specs,
              }))
        if (itemsToEmit.length === 0) {
            showWarning(t(isNoSpecProduct() ? 'product.detail.selectQuantity' : 'product.detail.selectSpecification'))
            return
        }
        emit('addToCart', itemsToEmit)
    }

    // ========== 页面事件绑定 ==========
    const toggleCartExpand = () => (cartExpanded.value = !cartExpanded.value)
    const handleAddToCart = () => submitCart()
    
    // 清零购物车数据的方法
    const resetCartData = () => {
        // 清零所有数量选择器
        optionQuantities.value = {}
        // 清零无规格商品数量
        noSpecQuantity.value = 0
        // 清空购物车项目
        cartItems.value = []
        // 清除本地存储
        clearCartStorage()
        // 折叠购物车展示
        cartExpanded.value = false
    }
    
    // 暴露方法给父组件调用
    defineExpose({
        resetCartData
    })
    
    const handleBuyNow = () => {
        // 准备购买数据
        const buyNowData = isNoSpecProduct()
            ? noSpecQuantity.value > 0 && getNoSpecProductSku()
                ? [{ options: {}, quantity: noSpecQuantity.value, skuId: getNoSpecProductSku()!.skuId }]
                : []
            : cartItems.value.map(item => ({
                  options: item.options,
                  quantity: item.quantity,
                  skuId: item.sku.skuId,
              }))

        if (buyNowData.length === 0) {
            showWarning(t(isNoSpecProduct() ? 'product.detail.selectQuantity' : 'product.detail.selectSpecification'))
            return
        }

        emit('buyNow', buyNowData)
    }
    const handleAddToFavorites = () => {
        isFavorited.value = !isFavorited.value
        emit('addToFavorites')
    }

    // ========== 无规格商品逻辑 ==========
    const getNoSpecProductSku = () => (props.product.productSkuInfos?.[0] ? props.product.productSkuInfos[0] : null)
    const getNoSpecProductStock = () => getNoSpecProductSku()?.amountOnSale || 0
    const getNoSpecProductPrice = () => {
        const sku = getNoSpecProductSku()
        const priceData = {
            price: parseFloat(String(sku?.consignPrice || sku?.price || props.product.price)),
            usdPrice: parseFloat(String(sku?.usdPrice || props.product.usdPrice)),
        }
        return getCurrentPrice(priceData)
    }
    const increaseNoSpecQuantity = () => {
        if (noSpecQuantity.value < getNoSpecProductStock()) noSpecQuantity.value++
    }
    const decreaseNoSpecQuantity = () => {
        if (noSpecQuantity.value > 0) noSpecQuantity.value--
    }
    const setNoSpecMinQuantity = () => {
        if (minQuantity.value <= getNoSpecProductStock()) noSpecQuantity.value = minQuantity.value
        else showWarning(t('product.detail.insufficientStock', { count: minQuantity.value }))
    }

    // ========== 本地存储 ==========
    const getStorageKey = () => `cart_${props.product.id}`
    const saveCartToStorage = () => {
        if (!props.product?.id) return
        try {
            sessionStorage.setItem(
                getStorageKey(),
                JSON.stringify({ cartItems: cartItems.value, quantities: optionQuantities.value, selections: localSelectedOptions.value, timestamp: Date.now() })
            )
        } catch (err) {
            console.error('保存购物车到本地存储失败:', err)
        }
    }
    const loadCartFromStorage = () => {
        if (!props.product?.id) return
        try {
            const storedData = sessionStorage.getItem(getStorageKey())
            if (storedData) {
                const parsedData = JSON.parse(storedData)
                if (Date.now() - parsedData.timestamp < 30 * 60 * 1000) {
                    cartItems.value = parsedData.cartItems || []
                    optionQuantities.value = parsedData.quantities || {}
                    if (parsedData.selections) {
                        localSelectedOptions.value = parsedData.selections
                        emit('update:selectedOptions', parsedData.selections)
                    }
                    if (cartItems.value.length > 0) cartExpanded.value = true
                } else {
                    clearCartStorage()
                }
            }
        } catch (err) {
            console.error('从本地存储加载购物车失败:', err)
            clearCartStorage()
        }
    }
    const clearCartStorage = () => {
        if (props.product?.id) sessionStorage.removeItem(getStorageKey())
    }

    // ========== Toast 与 滚动条 ==========
    const showWarning = (msg: string) => {
        toastMessage.value = msg
        toastType.value = 'warning'
        showToast.value = true
        setTimeout(() => {
            showToast.value = false
        }, 3000)
    }
    const scrollContentRef = ref<HTMLElement | HTMLElement[] | null>(null)
    const getScrollerElement = (): HTMLElement | null => {
        if (!scrollContentRef.value) return null
        return Array.isArray(scrollContentRef.value) ? scrollContentRef.value[0] : (scrollContentRef.value as HTMLElement)
    }

    const skuScrollPosition = ref(0)
    const skuMaxScroll = ref(0)
    const skuCardWidth = ref(150)
    const skuScrollTo = (direction: 'previous' | 'next') => {
        const scroller = getScrollerElement()
        if (!scroller) return
        const scrollAmount = skuCardWidth.value + 10
        const newPosition = direction === 'previous' ? scroller.scrollLeft - scrollAmount : scroller.scrollLeft + scrollAmount
        scroller.scrollTo({ left: newPosition, behavior: 'smooth' })
    }
    const handleSkuScroll = () => {
        const scroller = getScrollerElement()
        if (!scroller) return
        skuScrollPosition.value = scroller.scrollLeft
        skuMaxScroll.value = scroller.scrollWidth - scroller.clientWidth
    }
    const allTableSkusOutOfStock = computed(() => {
        const tableAttribute = getTableAttribute()
        if (!tableAttribute) return true
        return tableAttribute.values.every(v => getOptionStock(v.id) <= 0)
    })

    // ========== 生命周期钩子 ==========
    watchEffect(() => {
        if (attributes.value.length > 0) {
            attributes.value.forEach(attr => {
                if (attr.values && attr.values.length > 0 && !localSelectedOptions.value[attr.id]) {
                    localSelectedOptions.value[attr.id] = attr.values[0].id
                }
            })
            emit('update:selectedOptions', localSelectedOptions.value)
        }
    })

    watch(
        () => {
            const mainSelections: Record<string, string> = {}
            attributes.value.slice(0, -1).forEach(attr => {
                if (localSelectedOptions.value[attr.id]) mainSelections[attr.id] = localSelectedOptions.value[attr.id]
            })
            return mainSelections
        },
        () => {
            optionQuantities.value = {}
            setTimeout(() => {
                getTableAttribute()?.values.forEach(v => {
                    const q = getQuantity(v.id)
                    if (q > 0) optionQuantities.value[v.id] = q
                })
            }, 50)
        },
        { deep: true }
    )

    onMounted(() => {
        loadCartFromStorage()
        const scroller = getScrollerElement()
        if (scroller) {
            scroller.addEventListener('scroll', handleSkuScroll)
            setTimeout(() => {
                const currentScroller = getScrollerElement() // Re-check inside timeout
                if (currentScroller) {
                    skuMaxScroll.value = currentScroller.scrollWidth - currentScroller.clientWidth
                    const skuCardElement = currentScroller.querySelector('.sku-cards-scroller-item')
                    if (skuCardElement) skuCardWidth.value = skuCardElement.clientWidth
                }
            }, 500)
        }
    })
    onUnmounted(() => {
        const scroller = getScrollerElement()
        if (scroller) scroller.removeEventListener('scroll', handleSkuScroll)
    })
</script>

<style scoped>
    .toast-message {
        transition: opacity 0.3s ease-in-out;
    }
    .spec-scroll-container {
        max-height: 300px; /* 桌面端规格列表最大高度 */
        overflow-y: auto;
    }
    .cart-scroll-container {
        max-height: 400px; /* 购物车列表最大高度 */
        overflow-y: auto;
    }
    .sku-cards-scroller {
        position: relative;
    }
    .sku-cards-scroller-crop {
        overflow: hidden;
    }
    .sku-cards-scroller-content {
        overflow-x: scroll;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
    }
    .sku-cards-scroller-content::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
    .sku-cards-scroller-platter {
        display: flex;
        gap: 12px;
        padding: 4px 16px 16px 16px;
    }
    .sku-cards-scroller-item {
        flex: 0 0 auto;
        width: 132px; /* 卡片宽度 */
        scroll-snap-align: start;
    }
    .sku-card {
        border: 1px solid #d1d5db;
        border-radius: 0.5rem;
        padding: 0.75rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
        transition: all 0.2s;
    }
    .sku-card-selected {
        border-color: #f97316;
        background-color: #fff7ed;
    }
    .sku-paddlenav {
        display: none;
    }
    @media (min-width: 768px) {
        .sku-paddlenav {
            display: flex;
            justify-content: space-between;
            position: absolute;
            top: 50%;
            left: -15px;
            right: -15px;
            transform: translateY(-50%);
            pointer-events: none;
        }
    }
    .sku-paddlenav-arrow {
        width: 32px;
        height: 32px;
        background: white;
        border: 1px solid #d1d5db;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        pointer-events: auto;
        transition: all 0.2s;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .sku-paddlenav-arrow:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }
    .sku-paddlenav-arrow:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .sku-paddlenav-arrow svg {
        width: 16px;
        height: 16px;
        fill: #6b7280;
    }
    .sku-paddlenav-arrow:hover svg {
        fill: #374151;
    }
    .loading-spinner-sm {
        display: inline-block;
        width: 12px;
        height: 12px;
        border: 2px solid #ffffff;
        border-radius: 50%;
        border-top-color: transparent;
        animation: spin 1s ease-in-out infinite;
    }
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
</style>
