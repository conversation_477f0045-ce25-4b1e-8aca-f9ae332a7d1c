<template>
    <div class="product-gallery flex flex-col items-center">
        <!-- 主图 - 响应式设计 -->
        <div
            class="main-image-container mb-6 border border-gray-200 rounded-xl overflow-hidden w-full aspect-square flex items-center justify-center bg-gray-50 relative shadow-lg transition-all duration-300 hover:shadow-xl"
        >
            <!-- 视频主图 -->
            <video v-if="isVideoShowing" controls class="max-w-full max-h-full object-contain z-10">
                <source :src="currentVideo" type="video/mp4" />
                {{ t('product.detail.videoNotSupported') }}
            </video>
            <!-- 图片主图 -->
            <img v-else :src="currentImage" :alt="productTitle" class="w-[85%] h-[85%] object-contain cursor-pointer z-10" @click="openLightbox" />
        </div>

        <!-- 缩略图 - 横向滑动方案 -->
        <div class="gallery-thumbs-scroller flex items-center w-full mb-4 relative">
            <!-- 左侧箭头 -->
            <button
                class="gallery-paddlenav-arrow gallery-paddlenav-arrow-previous static mr-2"
                :disabled="thumbsScrollPosition <= 0"
                @click="slideThumbs('previous')"
                aria-label="上一个缩略图"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="24" height="24">
                    <path
                        d="M21.559,12.062 L15.618,17.984 L21.5221,23.944 C22.105,24.533 22.1021,25.482 21.5131,26.065 C21.2211,26.355 20.8391,26.4999987 20.4571,26.4999987 C20.0711,26.4999987 19.6851,26.352 19.3921,26.056 L12.4351,19.034 C11.8531,18.446 11.8551,17.4999987 12.4411,16.916 L19.4411,9.938 C20.0261,9.353 20.9781,9.354 21.5621,9.941 C22.1471,10.528 22.1451,11.478 21.5591,12.062 L21.559,12.062 Z"
                    />
                </svg>
            </button>
            <div class="gallery-thumbs-scroller-crop flex-1">
                <div class="gallery-thumbs-scroller-content" ref="thumbsScrollContent" @scroll="handleThumbsScroll">
                    <!-- 视频缩略图 -->
                    <div
                        v-if="hasMainVideo"
                        class="gallery-thumbs-scroller-item thumbnail-item border rounded-md overflow-hidden cursor-pointer transition-all relative"
                        :class="{ 'border-blue-500 shadow-md': isVideoShowing }"
                        @click="showVideo()"
                    >
                        <div class="absolute inset-0 flex items-center justify-center bg-black/20">
                            <i class="fas fa-play text-white"></i>
                        </div>
                        <img :src="videoThumbnail" :alt="`${productTitle} - 视频`" class="w-full h-full object-cover" />
                    </div>
                    <!-- 图片缩略图 -->
                    <div
                        v-for="(image, index) in allImages"
                        :key="index"
                        class="gallery-thumbs-scroller-item thumbnail-item border rounded-md overflow-hidden cursor-pointer transition-all"
                        :class="{ 'border-blue-500 shadow-md': index === currentIndex && !isVideoShowing }"
                        @click="selectImage(index)"
                    >
                        <img :src="image" :alt="`${productTitle} - 缩略图 ${index + 1}`" class="w-full h-full object-cover" />
                    </div>
                </div>
            </div>
            <!-- 右侧箭头 -->
            <button
                class="gallery-paddlenav-arrow gallery-paddlenav-arrow-next static ml-2"
                :disabled="thumbsScrollPosition >= thumbsMaxScroll"
                @click="slideThumbs('next')"
                aria-label="下一个缩略图"
            >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 36 36" width="24" height="24">
                    <path
                        d="M23.5587,16.916 C24.1447,17.4999987 24.1467,18.446 23.5647,19.034 L16.6077,26.056 C16.3147,26.352 15.9287,26.4999987 15.5427,26.4999987 C15.1607,26.4999987 14.7787,26.355 14.4867,26.065 C13.8977,25.482 13.8947,24.533 14.4777,23.944 L20.3818,17.984 L14.4408,12.062 C13.8548,11.478 13.8528,10.5279 14.4378,9.941 C15.0218,9.354 15.9738,9.353 16.5588,9.938 L23.5588,16.916 L23.5587,16.916 Z"
                    />
                </svg>
            </button>
        </div>

        <!-- 图片灯箱 -->
        <div v-if="lightboxOpen" class="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center p-4" @click.self="closeLightbox">
            <div class="relative max-w-full max-h-full">
                <!-- 图片 -->
                <img :src="currentImage" :alt="productTitle" class="max-w-full max-h-[80vh] object-contain" />

                <!-- 关闭按钮 -->
                <button @click="closeLightbox" class="absolute top-2 right-2 bg-white/20 rounded-full p-2 hover:bg-white/40 transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>

                <!-- 导航按钮 -->
                <div class="absolute inset-x-0 top-1/2 transform -translate-y-1/2 flex justify-between px-4 sm:px-6">
                    <button
                        @click.stop="prevImage"
                        class="bg-white/20 rounded-full p-2 hover:bg-white/40 transition-colors"
                        :disabled="currentIndex === 0"
                        :class="{ 'opacity-30 cursor-not-allowed': currentIndex === 0 }"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                        </svg>
                    </button>
                    <button
                        @click.stop="nextImage"
                        class="bg-white/20 rounded-full p-2 hover:bg-white/40 transition-colors"
                        :disabled="currentIndex === allImages.length - 1"
                        :class="{ 'opacity-30 cursor-not-allowed': currentIndex === allImages.length - 1 }"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import type { ProductDetailVO } from '@/api/modules/product'
    import { computed, onBeforeUnmount, onMounted, onUnmounted, ref, watch } from 'vue'
    import { useI18n } from 'vue-i18n'

    const { t, locale } = useI18n()

    // Props
    const props = defineProps<{
        product: ProductDetailVO
        selectedSkuImage?: string | null
    }>()

    // 状态
    const currentIndex = ref(0)
    const isVideoShowing = ref(false)
    const thumbsScrollPosition = ref(0)
    const userSelectedImage = ref(false)
    const lightboxOpen = ref(false)
    const userOverrodeSkuImage = ref(false)

    // 横向滑动方案相关
    const thumbsScrollContent = ref<HTMLElement | null>(null)
    const thumbsMaxScroll = ref(0)
    const thumbWidth = ref(95)

    // 当外部选择新的SKU图片时，重置用户的手动选择
    watch(
        () => props.selectedSkuImage,
        newSkuImage => {
            if (newSkuImage) {
                userOverrodeSkuImage.value = false
                const imageIndex = allImages.value.indexOf(newSkuImage)
                if (imageIndex !== -1) {
                    currentIndex.value = imageIndex
                    ensureThumbVisible(imageIndex)
                }
            }
        }
    )

    // 计算属性
    const productTitle = computed(() => {
        return locale.value === 'zh-CN' ? props.product.title : props.product.titleEn || props.product.title
    })

    const hasMainVideo = computed(() => !!props.product.mainVideo)

    const videoThumbnail = computed(() => {
        // 使用第一张商品图片作为视频缩略图
        if (props.product.images && props.product.images.length > 0) {
            return props.product.images[0]
        }
        return '/src/assets/products/default.png'
    })

    const currentVideo = computed(() => {
        return props.product.mainVideo || ''
    })

    // 包含所有可显示图片的列表（基础图片 + SKU图片）
    const allImages = computed(() => {
        const images: string[] = []

        // 添加基础图片
        if (props.product.images && props.product.images.length > 0) {
            images.push(...props.product.images)
        }
        // 添加白底图（如果存在且不重复）
        if (props.product.whiteImage && !images.includes(props.product.whiteImage)) {
            images.push(props.product.whiteImage)
        }

        // 添加所有SKU的图片
        if (props.product.productSkuInfos) {
            for (const sku of props.product.productSkuInfos) {
                if (sku.specs) {
                    for (const _spec of sku.specs) {
                        // 假设图片存在于某个特定的规格属性中
                        // 这里需要根据实际情况调整，例如通过 spec.specName 判断是否为颜色规格
                        // if (spec.imageUrl && !images.includes(spec.imageUrl)) {
                    }
                }
            }
        }

        // 如果没有图片，添加默认图片
        if (images.length === 0) {
            images.push('/src/assets/products/default.png')
        }

        // 去重
        return [...new Set(images)]
    })

    const currentImage = computed(() => {
        let imageToShow = ''

        // 如果用户没有手动覆盖，并且有来自外部的SKU图片，则优先显示SKU图片
        if (!userOverrodeSkuImage.value && props.selectedSkuImage) {
            imageToShow = props.selectedSkuImage
        }
        // 否则，使用基于当前索引的缩略图
        else if (allImages.value.length > 0) {
            imageToShow = allImages.value[currentIndex.value]
        }

        // 如果没有找到任何图片，则返回默认图片
        return imageToShow || '/src/assets/products/default.png'
    })

    // 计算可显示的缩略图总数量
    const totalThumbs = computed(() => {
        return (hasMainVideo.value ? 1 : 0) + allImages.value.length
    })

    // 计算最大起始索引
    const maxThumbStartIndex = computed(() => {
        return Math.max(0, totalThumbs.value - 5) // 最多显示5张
    })

    // 方法
    const selectImage = (index: number) => {
        userOverrodeSkuImage.value = true // 用户接管图片选择
        currentIndex.value = index
        isVideoShowing.value = false
        userSelectedImage.value = true
        ensureThumbVisible(index)
    }

    const showVideo = () => {
        isVideoShowing.value = true
        userSelectedImage.value = false // 视频显示时，重置用户选择图片的标记
    }

    // 缩略图滑动方法
    const slideThumbs = (direction: 'previous' | 'next') => {
        if (!thumbsScrollContent.value) return
        const container = thumbsScrollContent.value
        const scrollAmount = thumbWidth.value + 12
        if (direction === 'previous') {
            container.scrollTo({
                left: Math.max(container.scrollLeft - scrollAmount, 0),
                behavior: 'smooth',
            })
        } else {
            container.scrollTo({
                left: Math.min(container.scrollLeft + scrollAmount, container.scrollWidth - container.clientWidth),
                behavior: 'smooth',
            })
        }
    }

    // 选择图片时确保该图片在可视区域内
    const ensureThumbVisible = (index: number) => {
        // 如果当前图片在视野之外，调整起始索引
        if (index < thumbsScrollPosition.value) {
            thumbsScrollPosition.value = index
        } else if (index >= thumbsScrollPosition.value + 5) {
            thumbsScrollPosition.value = Math.min(maxThumbStartIndex.value, index - 4)
        }
    }

    // 灯箱相关方法
    const openLightbox = () => {
        if (!isVideoShowing.value) {
            lightboxOpen.value = true
            // 添加键盘事件监听
            document.addEventListener('keydown', handleKeyDown)
        }
    }

    const closeLightbox = () => {
        lightboxOpen.value = false
        // 移除键盘事件监听
        document.removeEventListener('keydown', handleKeyDown)
    }

    const nextImage = () => {
        if (currentIndex.value < allImages.value.length - 1) {
            currentIndex.value++
            userSelectedImage.value = true
        }
    }

    const prevImage = () => {
        if (currentIndex.value > 0) {
            currentIndex.value--
            userSelectedImage.value = true
        }
    }

    // 键盘导航
    const handleKeyDown = (e: KeyboardEvent) => {
        if (lightboxOpen.value) {
            if (e.key === 'ArrowRight') {
                nextImage()
            } else if (e.key === 'ArrowLeft') {
                prevImage()
            } else if (e.key === 'Escape') {
                closeLightbox()
            }
        }
    }

    // 组件卸载前清理事件监听
    onBeforeUnmount(() => {
        document.removeEventListener('keydown', handleKeyDown)
    })

    const handleThumbsScroll = () => {
        const scroller = thumbsScrollContent.value
        if (scroller) {
            thumbsScrollPosition.value = scroller.scrollLeft
            thumbsMaxScroll.value = scroller.scrollWidth - scroller.clientWidth
        }
    }

    onMounted(() => {
        const scroller = thumbsScrollContent.value
        if (scroller) {
            scroller.addEventListener('scroll', handleThumbsScroll)
            // Initial calculation
            setTimeout(() => {
                const currentScroller = thumbsScrollContent.value
                if (currentScroller) {
                    thumbsMaxScroll.value = currentScroller.scrollWidth - currentScroller.clientWidth
                    const thumbItem = currentScroller.querySelector('.thumbnail-item') as HTMLElement
                    if (thumbItem) {
                        thumbWidth.value = thumbItem.offsetWidth + 14 // width + gap
                    }
                }
            }, 500)
        }

        document.addEventListener('keydown', handleKeyDown)
    })

    onUnmounted(() => {
        const scroller = thumbsScrollContent.value
        if (scroller) {
            scroller.removeEventListener('scroll', handleThumbsScroll)
        }
        document.removeEventListener('keydown', handleKeyDown)
    })
</script>

<style scoped>
    /* 为灯箱添加一些过渡效果 */
    .fixed {
        transition: opacity 0.3s ease;
    }

    /* 适应各种设备的响应式样式 */
    @media (max-width: 640px) {
        .main-image-container {
            min-height: 420px;
        }

        .thumbnail-item {
            width: 75px;
            height: 75px;
        }

        .gallery-thumbs-scroller {
            padding: 0 1rem;
        }
    }

    /* 平板设备优化 */
    @media (min-width: 768px) and (max-width: 1366px) {
        .product-gallery {
            width: 100%;
            max-width: 100%;
        }

        .main-image-container {
            aspect-ratio: auto;
            width: 100%;
            max-width: 100%;
            min-height: 480px;
        }

        .gallery-thumbs-scroller {
            width: 100%;
            max-width: 100%;
            padding: 0 30px;
        }
    }

    /* 主图容器样式，确保flex布局生效 */
    .main-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .main-image-container img,
    .main-image-container video {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
    }

    /* 商品图片特别处理 */
    .main-image-container img[src*='alicdn.com'],
    .main-image-container img[src*='taobao.com'],
    .main-image-container img[src*='cbu01'],
    .main-image-container img[src*='ibank'] {
        max-width: 100%;
        height: auto;
        object-fit: contain;
    }

    /* 确保图片不变形的辅助样式 */
    .aspect-square {
        position: relative;
        width: 100%;
        overflow: hidden;
    }

    .aspect-square::before {
        content: '';
        display: block;
        padding-bottom: 100%; /* 确保是正方形 */
    }

    .aspect-square > * {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .gallery-thumbs-scroller {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 1rem;
        position: relative;
    }

    .gallery-paddlenav-arrow {
        position: static;
        top: auto;
        transform: none;
        margin: 0;
    }

    .gallery-thumbs-scroller-crop {
        overflow: hidden;
        flex: 1 1 0%;
    }

    .gallery-thumbs-scroller-content {
        display: flex;
        overflow-x: auto;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        padding: 0.5rem 0;
    }

    .gallery-thumbs-scroller-content::-webkit-scrollbar {
        display: none;
    }

    .gallery-thumbs-scroller-item {
        flex: 0 0 auto;
        width: 95px;
        margin-right: 12px;
        scroll-snap-align: start;
    }

    .main-image-container {
        min-height: 400px; /* 增加主图最小高度，让图片显示更大 */
    }
    .main-image-container img {
        transition: transform 0.3s ease-in-out;
    }
    .main-image-container:hover img {
        transform: scale(1.05);
    }
    .gallery-thumbs-scroller {
        height: 110px; /* 固定高度 */
    }
    .gallery-thumbs-scroller-crop {
        overflow: hidden;
    }
    .gallery-thumbs-scroller-content {
        display: flex;
        gap: 10px;
        overflow-x: scroll;
        scroll-snap-type: x mandatory;
        -webkit-overflow-scrolling: touch;
        -ms-overflow-style: none; /* IE and Edge */
        scrollbar-width: none; /* Firefox */
        padding-bottom: 10px; /* 为滚动条留出空间，即使隐藏 */
    }
    .gallery-thumbs-scroller-content::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
    }
    .thumbnail-item {
        flex: 0 0 95px; /* 固定缩略图宽度 */
        width: 95px;
        height: 95px;
        scroll-snap-align: start;
        position: relative;
    }
    .gallery-paddlenav-arrow {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        transition: all 0.2s;
        z-index: 10;
    }
    .gallery-paddlenav-arrow:hover:not(:disabled) {
        transform: scale(1.1);
        border-color: #9ca3af;
    }
    .gallery-paddlenav-arrow:disabled {
        opacity: 0.4;
        cursor: not-allowed;
    }
    .gallery-paddlenav-arrow.static {
        position: relative;
    }
</style>
