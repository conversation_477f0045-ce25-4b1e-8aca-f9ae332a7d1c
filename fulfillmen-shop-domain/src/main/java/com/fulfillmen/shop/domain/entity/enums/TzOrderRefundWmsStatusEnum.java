/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款WMS状态枚举
 *
 * <AUTHOR>
 * @date 2025/08/08 15:10
 * @description: WMS退款状态：未处理、处理中、已完成、失败
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum TzOrderRefundWmsStatusEnum implements IEnum<Integer> {

    /**
     * 未处理
     */
    UNPROCESSED(0, "未处理"),

    /**
     * 处理中
     */
    PROCESSING(1, "处理中"),

    /**
     * 已完成
     */
    COMPLETED(2, "已完成"),

    /**
     * 失败
     */
    FAILED(3, "失败");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return this.code;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TzOrderRefundWmsStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TzOrderRefundWmsStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态
     *
     * @return true表示终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == FAILED;
    }
}
