/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单请求
 *
 * <pre>
 * 1. 订单预览
 * 2. 订单创建
 * 3. 订单支付
 * 4. 订单取消
 * 5. 订单退款
 * 6. 订单退货
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/6/26
 * @description 订单请求
 */
public class OrderReq {

    /**
     * 订单预览请求
     */
    @Data
    @Schema(name = "OrderPreviewReq", description = "订单预览请求")
    public static class OrderPreviewReq {

        /**
         * 产品列表
         */
        @Valid
        @Schema(name = "productList", description = "产品列表")
        @NotNull(message = "{validation.order.preview.productList.notNull}")
        @Size(min = 1, max = 50, message = "{validation.order.preview.productList.size}")
        private List<CreateOrderReq> productList;

        /**
         * 是否来自购物车 0: 否 , 1: 是
         */
        @Schema(description = "是否来自购物车 1: 是 0: 否")
        private Integer isShoppingCart;

        /**
         * 购物车ID 多个ID用逗号分隔，需要删除的购物车 Id 列表。如果 null 则不删除。
         */
        @Schema(description = "购物车ID", example = "1,2,3")
        private String shoppingCartIds;
    }

    /**
     * CreateOrderParam 创建订单参数
     */
    @Data
    @Schema(name = "CreateOrderParam", description = "创建订单参数")
    public static class CreateOrderReq {

        /**
         * sku id
         */
        @NotNull(message = "{validation.order.skuId.notNull}")
        @Schema(name = "skuId", description = " sku id")
        private Long skuId;
        /**
         * 产品数量
         */
        @NotNull(message = "{validation.order.productQuantity.notNull}")
        @Min(value = 1, message = "{validation.order.productQuantity.min}")
        @Schema(name = "productQuantity", description = "产品数量")
        private Integer productQuantity;
    }

    /**
     * 订单提交请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateOrderSubmitReq {

        /**
         * 幂等令牌（预览时生成）
         */
        @NotBlank(message = "幂等令牌不能为空")
        @Schema(name = "idempotentToken", description = "幂等令牌")
        private String idempotentToken;

        /**
         * 收货地址信息
         * 暂不需要，后续再添加
         */
        // @NotNull(message = "收货地址信息不能为空")
        // @Valid
        // private OrderAddressReq addressInfo;

        /**
         * 买家留言
         */
        @Schema(name = "buyerMessage", description = "买家留言")
        private String buyerMessage;

        /**
         * 发票信息
         */
        @Schema(name = "invoiceInfo", description = "发票信息")
        private OrderInvoiceReq invoiceInfo;

        /**
         * 是否使用预览价格（防止价格变动）
         */
        @Schema(name = "usePreviewPrice", description = "是否使用预览价格")
        private Boolean usePreviewPrice;
    }

    /**
     * 收货地址请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderAddressReq {

        @NotBlank(message = "收货人姓名不能为空")
        @Schema(name = "consigneeName", description = "收货人姓名")
        private String consigneeName;

        @NotBlank(message = "收货人电话不能为空")
        @Schema(name = "consigneePhone", description = "收货人电话")
        private String consigneePhone;

        @Schema(name = "consigneeMobile", description = "收货人手机号")
        private String consigneeMobile;

        @NotBlank(message = "省份不能为空")
        @Schema(name = "province", description = "省份")
        private String province;

        @NotBlank(message = "城市不能为空")
        @Schema(name = "city", description = "城市")
        private String city;

        @NotBlank(message = "区县不能为空")
        @Schema(name = "district", description = "区县")
        private String district;

        @NotBlank(message = "详细地址不能为空")
        @Schema(name = "detailAddress", description = "详细地址")
        private String detailAddress;

        @Schema(name = "postalCode", description = "邮政编码")
        private String postalCode;

        @Schema(name = "districtCode", description = "区县编码")
        private String districtCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderPageRequest {

        @Schema(name = "page", description = "页码")
        private int page;

        @Schema(name = "size", description = "每页大小")
        private int size;

        @Schema(name = "status", description = "订单状态")
        private Integer status;

        @Schema(name = "keyword", description = "搜索关键词")
        private String keyword;

        @Schema(name = "skuId", description = "SKU ID")
        private Long skuId;

        @Schema(name = "startTime", description = "开始时间")
        private LocalDateTime startTime;

        @Schema(name = "endTime", description = "结束时间")
        private LocalDateTime endTime;
    }

    /**
     * 发票信息请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderInvoiceReq {

        @NotNull(message = "发票类型不能为空")
        @Schema(name = "invoiceType", description = "发票类型")
        private Integer invoiceType;

        @NotBlank(message = "发票抬头不能为空")
        @Schema(name = "invoiceTitle", description = "发票抬头")
        private String invoiceTitle;

        @Schema(name = "taxpayerNumber", description = "纳税人识别号")
        private String taxpayerNumber;

        @Schema(name = "invoiceContent", description = "发票内容")
        private String invoiceContent;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderPayRequest {

        @NotBlank(message = "订单号不能为空")
        @Schema(name = "orderNo", description = "订单号")
        private String orderNo;

        @NotBlank(message = "支付方式不能为空")
        @Schema(name = "payType", description = "支付方式")
        private String payType;

        @Schema(name = "payAmount", description = "支付金额")
        private BigDecimal payAmount;

    }

    /**
     * 确认收货请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ConfirmReceiptRequest {

        @Schema(name = "feedback", description = "反馈")
        private String feedback;

        @Schema(name = "rating", description = "评分")
        private Integer rating;
    }

    /**
     * 取消订单请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CancelOrderRequest {

        @Schema(name = "reason", description = "原因")
        private String reason;

        @Schema(name = "description", description = "描述")
        private String description;
    }

    /**
     * 退款申请请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundApplicationRequest {

        @Schema(name = "reason", description = "原因")
        private String reason;

        @Schema(name = "description", description = "描述")
        private String description;

    }

    /**
     * 退款申请响应
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundApplicationVO {

        @Schema(name = "refundNo", description = "退款单号")
        private String refundNo;

        @Schema(name = "status", description = "状态")
        private String status;

        @Schema(name = "message", description = "消息")
        private String message;
    }
}
