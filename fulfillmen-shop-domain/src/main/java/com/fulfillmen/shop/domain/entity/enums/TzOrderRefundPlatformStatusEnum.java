/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款平台状态枚举
 *
 * <AUTHOR>
 * @date 2025/08/08 15:10
 * @description: 外部平台退款状态：未同步、同步中、已同步、失败
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum TzOrderRefundPlatformStatusEnum implements IEnum<Integer> {

    /**
     * 未同步
     */
    NOT_SYNCED(0, "未同步"),

    /**
     * 同步中
     */
    SYNCING(1, "同步中"),

    /**
     * 已同步
     */
    SYNCED(2, "已同步"),

    /**
     * 失败
     */
    FAILED(3, "失败");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return this.code;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TzOrderRefundPlatformStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TzOrderRefundPlatformStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态
     *
     * @return true表示终态
     */
    public boolean isFinalStatus() {
        return this == SYNCED || this == FAILED;
    }

    /**
     * 判断是否需要重试
     *
     * @return true表示可以重试
     */
    public boolean canRetry() {
        return this == NOT_SYNCED || this == FAILED;
    }
}
