/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款发起方式枚举
 *
 * <AUTHOR>
 * @date 2025/08/08 15:10
 * @description: 退款发起方式：主动申请、被动触发
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum TzOrderRefundInitiatorEnum implements IEnum<Integer> {

    /**
     * 主动申请
     */
    ACTIVE_APPLY(1, "主动申请"),

    /**
     * 被动触发
     */
    PASSIVE_TRIGGER(2, "被动触发");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return this.code;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TzOrderRefundInitiatorEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TzOrderRefundInitiatorEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
