/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款原因枚举
 *
 * <AUTHOR>
 * @date 2025/08/08 15:10
 * @description: 退款原因：质量问题、商品缺货、客户取消等
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum TzOrderRefundReasonEnum implements IEnum<Integer> {

    /**
     * 质量问题
     */
    QUALITY_ISSUE(1, "质量问题"),

    /**
     * 商品缺货
     */
    OUT_OF_STOCK(2, "商品缺货"),

    /**
     * 客户取消
     */
    CUSTOMER_CANCEL(3, "客户取消"),

    /**
     * 超时未付
     */
    TIMEOUT_UNPAID(4, "超时未付"),

    /**
     * 卖家取消
     */
    SELLER_CANCEL(5, "卖家取消"),

    /**
     * 物流问题
     */
    LOGISTICS_ISSUE(6, "物流问题");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return this.code;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TzOrderRefundReasonEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TzOrderRefundReasonEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
