/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.util;

import com.fulfillmen.shop.domain.enums.CurrencyCodeEnum;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 货币转换工具类
 *
 * <p>提供高性能的货币转换服务，基于内存缓存</p>
 * <p>支持外部服务定时更新汇率缓存，确保数据新鲜度</p>
 *
 * <AUTHOR>
 * @date 2025/6/6
 * @since 1.0.0
 */
public class CurrencyConversionUtils {

    private static final Logger log = LoggerFactory.getLogger(CurrencyConversionUtils.class);

    /**
     * 实时汇率缓存（由外部服务定时更新） key: "源货币-目标货币"（如 "CNY-USD"），value: 汇率
     */
    private static final Map<String, BigDecimal> RATE_CACHE = new ConcurrentHashMap<>();

    /**
     * 默认汇率缓存（用于服务不可用时的兜底） 以CNY为基准货币，key: 货币代码, value: 1 CNY = ? 外币
     */
    private static final Map<String, BigDecimal> DEFAULT_RATES = new ConcurrentHashMap<>();

    /**
     * 缓存更新时间
     */
    private static volatile long lastUpdateTime = 0L;

    static {
        // 初始化默认汇率（兜底数据）
        // 1 CNY = 0.13 USD
        DEFAULT_RATES.put("USD", new BigDecimal("0.13"));
        // 1 CNY = 0.13 EUR
        DEFAULT_RATES.put("EUR", new BigDecimal("0.11"));
        // 1 CNY = 20.8 JPY
        DEFAULT_RATES.put("JPY", new BigDecimal("20.8"));
        // 1 CNY = 185.2 KRW
        DEFAULT_RATES.put("KRW", new BigDecimal("185.2"));
        // 1 CNY = 11.6 INR
        DEFAULT_RATES.put("INR", new BigDecimal("11.6"));
        // 1 CNY = 1.0 CNY
        DEFAULT_RATES.put("CNY", new BigDecimal("1.0"));
    }

    private CurrencyConversionUtils() {
        // 工具类，禁止实例化
    }

    /**
     * 将CNY转换为USD（保持向后兼容）
     * <pre>
     * 默认开启价格保护，转换的目标丢弃四舍五入后。如果是 0.00 则自动设置 0.01
     * </pre>
     *
     * @param cnyAmount CNY金额
     * @return USD金额，如果转换失败则返回null
     */
    public static BigDecimal cnyToUsd(BigDecimal cnyAmount) {
        return cnyToUsd(cnyAmount, true);
    }

    /**
     * 将CNY转换为USD（保持向后兼容）
     *
     * @param cnyAmount                     CNY金额
     * @param isApplyMinimumPriceProtection 是否开启最小价格保护，开启后。如果转到目标价格小于0.01，则会自动设置为0.01
     * @return USD金额，如果转换失败则返回null
     */
    public static BigDecimal cnyToUsd(BigDecimal cnyAmount, boolean isApplyMinimumPriceProtection) {
        return convertCurrency(cnyAmount, "CNY", "USD", isApplyMinimumPriceProtection);
    }

    /**
     * 将USD转换为CNY（保持向后兼容）
     *
     * @param usdAmount USD金额
     * @return CNY金额，如果转换失败则返回null
     */
    public static BigDecimal usdToCny(BigDecimal usdAmount) {
        return convertCurrency(usdAmount, "USD", "CNY");
    }

    /**
     * 通用货币转换方法
     *
     * @param amount                        金额
     * @param fromCurrency                  源货币代码
     * @param toCurrency                    目标货币代码
     * @param isApplyMinimumPriceProtection 是否开启价格保护，开启后。如果转到目标价格小于0.01，则会自动设置为0.01
     * @return 转换后的金额，如果转换失败则返回null
     */
    public static BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency, boolean isApplyMinimumPriceProtection) {
        if (amount == null || fromCurrency == null || toCurrency == null) {
            return null;
        }

        // 验证货币代码是否支持
        if (!CurrencyCodeEnum.isSupported(fromCurrency) || !CurrencyCodeEnum.isSupported(toCurrency)) {
            log.warn("不支持的货币代码: {} -> {}", fromCurrency, toCurrency);
            return null;
        }

        // 相同货币直接返回
        if (fromCurrency.equals(toCurrency)) {
            return amount;
        }

        try {
            BigDecimal exchangeRate = getExchangeRate(fromCurrency, toCurrency);
            if (exchangeRate == null) {
                log.warn("无法获取汇率: {} -> {}", fromCurrency, toCurrency);
                return null;
            }

            // 进行汇率转换，保持截断逻辑（直接截断，不四舍五入）
            BigDecimal convertedAmount = amount.multiply(exchangeRate).setScale(2, RoundingMode.DOWN);
            if (isApplyMinimumPriceProtection) {
                // 应用最小价格保护机制
                BigDecimal result = applyMinimumPriceProtection(convertedAmount, toCurrency);
                if (log.isDebugEnabled() && !convertedAmount.equals(result)) {
                    log.debug("应用最小价格保护: {} {} -> {} {} (原始转换结果: {})",
                        amount, fromCurrency, result, toCurrency, convertedAmount);
                }
                return result;
            }

            return convertedAmount;
        } catch (Exception e) {
            log.warn("货币转换失败: {} {} -> {}", amount, fromCurrency, toCurrency, e);
            return null;
        }
    }

    /**
     * 通用货币转换方法
     *
     * @param amount       金额
     * @param fromCurrency 源货币代码
     * @param toCurrency   目标货币代码
     * @return 转换后的金额，如果转换失败则返回null
     */
    public static BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency) {
        return convertCurrency(amount, fromCurrency, toCurrency, true);
    }

    /**
     * 获取 CNY —> USD 汇率
     *
     * @return 汇率，如果获取失败则返回null
     */
    public static BigDecimal getExchangeRateCnyToUsd() {
        // 基于CNY基础汇率计算
        return calculateRateBasedOnCNY("CNY", "USD");
    }

    /**
     * 获取汇率
     *
     * @param fromCurrency 源货币代码
     * @param toCurrency   目标货币代码
     * @return 汇率，如果获取失败则返回null
     */
    public static BigDecimal getExchangeRate(String fromCurrency, String toCurrency) {
        // 相同货币汇率为1
        if (fromCurrency.equals(toCurrency)) {
            return BigDecimal.ONE;
        }

        // 基于CNY基础汇率计算
        return calculateRateBasedOnCNY(fromCurrency, toCurrency);
    }

    /**
     * 基于CNY基础汇率计算其他货币对汇率
     *
     * @param fromCurrency 源货币代码
     * @param toCurrency   目标货币代码
     * @return 汇率，如果计算失败则使用默认汇率
     */
    private static BigDecimal calculateRateBasedOnCNY(String fromCurrency, String toCurrency) {
        // 情况1: CNY -> 其他货币，直接从缓存获取
        if ("CNY".equals(fromCurrency)) {
            String cacheKey = "CNY-" + toCurrency;
            BigDecimal rate = RATE_CACHE.get(cacheKey);
            if (rate != null) {
                return rate;
            }
        }

        // 情况2: 其他货币 -> CNY，使用CNY基础汇率的倒数
        if ("CNY".equals(toCurrency)) {
            String cacheKey = "CNY-" + fromCurrency;
            BigDecimal rate = RATE_CACHE.get(cacheKey);
            if (rate != null && rate.compareTo(BigDecimal.ZERO) > 0) {
                return BigDecimal.ONE.divide(rate, 6, RoundingMode.HALF_UP);
            }
        }

        // 情况3: 其他货币A -> 其他货币B，通过CNY中转
        // 汇率 = (A -> CNY) * (CNY -> B) = (1 / CNY->A) * (CNY->B)
        String fromToCnyKey = "CNY-" + fromCurrency;
        String cnyToTargetKey = "CNY-" + toCurrency;

        BigDecimal cnyToFromRate = RATE_CACHE.get(fromToCnyKey);
        BigDecimal cnyToTargetRate = RATE_CACHE.get(cnyToTargetKey);

        if (cnyToFromRate != null && cnyToTargetRate != null && cnyToFromRate.compareTo(BigDecimal.ZERO) > 0) {
            // A -> B = (1 / CNY->A) * (CNY->B) = CNY->B / CNY->A
            return cnyToTargetRate.divide(cnyToFromRate, 6, RoundingMode.HALF_UP);
        }

        // 缓存未命中，使用默认汇率
        log.debug("CNY基础汇率缓存未命中，使用默认汇率: {} -> {}", fromCurrency, toCurrency);
        return calculateWithDefaultRates(fromCurrency, toCurrency);
    }

    /**
     * 更新汇率缓存（由外部服务调用）
     *
     * @param fromCurrency 源货币代码
     * @param toCurrency   目标货币代码
     * @param rate         汇率
     */
    public static void updateExchangeRate(String fromCurrency, String toCurrency, BigDecimal rate) {
        if (fromCurrency == null || toCurrency == null || rate == null) {
            return;
        }
        String cacheKey = fromCurrency + "-" + toCurrency;
        RATE_CACHE.put(cacheKey, rate);
        lastUpdateTime = System.currentTimeMillis();
        log.debug("更新汇率缓存: {} = {}", cacheKey, rate);
    }

    /**
     * 批量更新汇率缓存（由外部服务调用）
     *
     * @param rates 汇率映射，key为"源货币-目标货币"，value为汇率
     */
    public static void updateExchangeRates(Map<String, BigDecimal> rates) {
        if (rates == null || rates.isEmpty()) {
            return;
        }
        RATE_CACHE.putAll(rates);
        lastUpdateTime = System.currentTimeMillis();
        log.info("批量更新汇率缓存，共 {} 条记录", rates.size());
    }

    /**
     * 清空汇率缓存
     */
    public static void clearCache() {
        RATE_CACHE.clear();
        log.info("汇率缓存已清空");
    }

    /**
     * 获取缓存更新时间
     *
     * @return 最后更新时间戳
     */
    public static long getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存条目数
     */
    public static int getCacheSize() {
        return RATE_CACHE.size();
    }

    /**
     * 使用默认汇率计算汇率
     *
     * @param fromCurrency 源货币
     * @param toCurrency   目标货币
     * @return 汇率
     */
    private static BigDecimal calculateWithDefaultRates(String fromCurrency, String toCurrency) {
        BigDecimal fromRate = DEFAULT_RATES.get(fromCurrency);
        BigDecimal toRate = DEFAULT_RATES.get(toCurrency);

        if (fromRate == null || toRate == null) {
            log.warn("默认汇率不存在: {} -> {}", fromCurrency, toCurrency);
            return null;
        }

        // 通过CNY作为中间货币进行转换
        // 例如：USD -> EUR = (USD -> CNY) -> (CNY -> EUR) = (1/fromRate) * toRate
        if ("CNY".equals(fromCurrency)) {
            return toRate;
        } else if ("CNY".equals(toCurrency)) {
            return BigDecimal.ONE.divide(fromRate, 6, RoundingMode.HALF_UP);
        } else {
            return toRate.divide(fromRate, 6, RoundingMode.HALF_UP);
        }
    }

    /**
     * 应用最小价格保护机制
     *
     * <p>当转换结果为0.00时，自动设置为最小默认价格0.01</p>
     * <p>确保所有货币转换结果都不会出现0.00的价格</p>
     *
     * @param convertedAmount 转换后的金额
     * @param toCurrency      目标货币代码
     * @return 应用最小价格保护后的金额
     */
    private static BigDecimal applyMinimumPriceProtection(BigDecimal convertedAmount, String toCurrency) {
        if (convertedAmount == null) {
            return null;
        }

        // 定义最小价格（目标货币的最小货币单位：0.01）
        BigDecimal minimumPrice = new BigDecimal("0.01");

        // 如果转换结果小于等于0.00，则设置为最小价格0.01
        if (convertedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.debug("转换结果为零或负数，应用最小价格保护: {} -> {} {}",
                convertedAmount, minimumPrice, toCurrency);
            return minimumPrice;
        }

        // 如果转换结果大于0但小于0.01，则设置为最小价格0.01
        if (convertedAmount.compareTo(minimumPrice) < 0) {
            log.debug("转换结果小于最小价格，应用最小价格保护: {} -> {} {}",
                convertedAmount, minimumPrice, toCurrency);
            return minimumPrice;
        }

        // 转换结果正常，直接返回
        return convertedAmount;
    }

    /**
     * 获取当前CNY到USD的汇率（保持向后兼容）
     *
     * @return CNY到USD的汇率
     */
    public static BigDecimal getCnyToUsdRate() {
        return getExchangeRate("CNY", "USD");
    }

    /**
     * 获取当前USD到CNY的汇率（保持向后兼容）
     *
     * @return USD到CNY的汇率
     */
    public static BigDecimal getUsdToCnyRate() {
        return getExchangeRate("USD", "CNY");
    }

    /**
     * 获取所有支持的货币代码
     *
     * @return 货币代码数组
     */
    public static String[] getSupportedCurrencies() {
        return DEFAULT_RATES.keySet().toArray(new String[0]);
    }

    /**
     * 格式化货币转换结果
     *
     * @param originalAmount  原始金额
     * @param fromCurrency    源货币代码
     * @param convertedAmount 转换后金额
     * @param toCurrency      目标货币代码
     * @return 格式化字符串（如：10.00 CNY = 1.40 USD）
     */
    public static String formatConversionResult(BigDecimal originalAmount,
        String fromCurrency,
        BigDecimal convertedAmount,
        String toCurrency) {
        // 由于ICurrencyRateService没有格式化方法，直接使用默认格式化

        // 兜底格式化
        if (originalAmount == null || convertedAmount == null) {
            return null;
        }

        // 获取货币符号
        CurrencyCodeEnum fromEnum = CurrencyCodeEnum.fromCode(fromCurrency);
        CurrencyCodeEnum toEnum = CurrencyCodeEnum.fromCode(toCurrency);

        String fromSymbol = fromEnum != null ? fromEnum.getSymbol() : fromCurrency;
        String toSymbol = toEnum != null ? toEnum.getSymbol() : toCurrency;

        return String.format("%.2f %s = %.2f %s", originalAmount, fromSymbol, convertedAmount, toSymbol);
    }
}
