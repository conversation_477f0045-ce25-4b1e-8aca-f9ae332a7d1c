/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户订单详情视图对象
 *
 * <AUTHOR>
 * @date 2025/6/27
 * @description 用户端订单详情显示的数据
 */
@Data
@Schema(description = "用户订单详情")
public class UserOrderDetailVO {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单状态")
    private int status;

    @Schema(description = "订单状态名称")
    private String statusName;

    @Schema(description = "订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    @Schema(description = "发货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shipTime;

    @Schema(description = "完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    @Schema(description = "订单商品列表")
    private List<OrderItemDetailInfo> items;

    @Schema(description = "价格信息")
    private PriceInfo priceInfo;

    @Schema(description = "收货地址信息")
    private DeliveryInfo deliveryInfo;

    @Schema(description = "物流追踪信息")
    private TrackingInfo trackingInfo;

    @Schema(description = "操作按钮状态")
    private ActionButtons actionButtons;

    @Schema(description = "订单时间线")
    private List<OrderTimeline> timeline;

    /**
     * 订单商品详情信息
     */
    @Data
    @Schema(description = "订单商品详情")
    public static class OrderItemDetailInfo {

        @Schema(description = "商品图片")
        private String productImage;

        @Schema(description = "商品标题")
        private String productTitle;

        @Schema(description = "商品标题(英文)")
        private String productTitleEn;

        @Schema(description = "商品规格")
        private String productSpecs;

        @Schema(description = "商品规格(英文)")
        private String productSpecsEn;

        @Schema(description = "商品ID")
        private Long productId;

        @Schema(description = "SKU ID")
        private Long skuId;

        @Schema(description = "商品数量")
        private Integer quantity;

        @Schema(description = "已到货数量")
        private Integer arrivedQuantity;

        @Schema(description = "商品单价")
        private BigDecimal price;

        @Schema(description = "商品单价(美元)")
        private BigDecimal priceUsd;

        @Schema(description = "商品单位")
        private String unit;

        @Schema(description = "商品单位(英文)")
        private String unitEn;

        @Schema(description = "小计金额")
        private BigDecimal subtotal;

        @Schema(description = "商品状态")
        private String itemStatus;

        @Schema(description = "商品状态描述")
        private String itemStatusName;
    }

    /**
     * 价格信息
     */
    @Data
    @Schema(description = "价格信息")
    public static class PriceInfo {

        @Schema(description = "商品总价")
        private BigDecimal subtotal;

        @Schema(description = "商品总价（美元）")
        private BigDecimal subtotalUsd;

        @Schema(description = "运费")
        private BigDecimal shippingFee;

        @Schema(description = "运费（美元）")
        private BigDecimal shippingFeeUsd;

        @Schema(description = "服务费")
        private BigDecimal serviceFee;

        @Schema(description = "服务费（美元）")
        private BigDecimal serviceFeeUsd;

        @Schema(description = "订单总金额")
        private BigDecimal totalAmount;

        @Schema(description = "订单总金额（美元）")
        private BigDecimal totalAmountUsd;

        @Schema(description = "实付金额")
        private BigDecimal paidAmount;

        @Schema(description = "实付金额（美元）")
        private BigDecimal paidAmountUsd;

        @Schema(description = "服务费费率")
        private BigDecimal serviceFeeRate;
    }

    /**
     * 收货地址信息
     */
    @Data
    @Schema(description = "收货地址信息")
    public static class DeliveryInfo {

        @Schema(description = "收货人姓名")
        private String receiverName;

        @Schema(description = "收货人电话")
        private String receiverPhone;

        @Schema(description = "收货地址")
        private String address;

        @Schema(description = "国家")
        private String country;

        @Schema(description = "省/州")
        private String province;

        @Schema(description = "城市")
        private String city;

        @Schema(description = "邮编")
        private String zipCode;
    }

    /**
     * 物流追踪信息
     */
    @Data
    @Schema(description = "物流追踪信息")
    public static class TrackingInfo {

        @Schema(description = "物流公司")
        private String carrier;

        @Schema(description = "物流单号")
        private String trackingNo;

        @Schema(description = "当前状态")
        private String currentStatus;

        @Schema(description = "预计送达时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime estimatedDeliveryTime;

        @Schema(description = "物流轨迹")
        private List<TrackingEvent> events;

        /**
         * 物流轨迹事件
         */
        @Data
        @Schema(description = "物流轨迹事件")
        public static class TrackingEvent {

            @Schema(description = "事件时间")
            @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
            private LocalDateTime eventTime;

            @Schema(description = "事件描述")
            private String description;

            @Schema(description = "事件地点")
            private String location;
        }
    }

    /**
     * 操作按钮状态
     */
    @Data
    @Schema(description = "操作按钮状态")
    public static class ActionButtons {

        @Schema(description = "是否可以取消")
        private Boolean canCancel;

        @Schema(description = "是否可以确认收货")
        private Boolean canConfirmReceipt;

        @Schema(description = "是否可以申请退款")
        private Boolean canApplyRefund;

        @Schema(description = "是否可以查看物流")
        private Boolean canViewTracking;

        @Schema(description = "是否可以重新购买")
        private Boolean canReorder;
    }

    /**
     * 订单时间线
     */
    @Data
    @Schema(description = "订单时间线")
    public static class OrderTimeline {

        @Schema(description = "状态名称")
        private String statusName;

        @Schema(description = "状态描述")
        private String description;

        @Schema(description = "时间")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime time;

        @Schema(description = "是否完成")
        private Boolean completed;

        @Schema(description = "是否当前状态")
        private Boolean current;
    }
}
