/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.domain.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 退款状态枚举
 *
 * <AUTHOR>
 * @date 2025/08/08 15:10
 * @description: 退款状态：申请中、已审批、已退款、已拒绝、已取消
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum TzOrderRefundStatusEnum implements IEnum<Integer> {

    /**
     * 申请中
     */
    APPLYING(0, "申请中"),

    /**
     * 已审批
     */
    APPROVED(1, "已审批"),

    /**
     * 已退款
     */
    REFUNDED(2, "已退款"),

    /**
     * 已拒绝
     */
    REJECTED(3, "已拒绝"),

    /**
     * 已取消
     */
    CANCELLED(4, "已取消");

    @EnumValue
    @JsonValue
    private final Integer code;
    private final String desc;

    @Override
    public Integer getValue() {
        return this.code;
    }

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static TzOrderRefundStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (TzOrderRefundStatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断是否为终态
     *
     * @return true表示终态
     */
    public boolean isFinalStatus() {
        return this == REFUNDED || this == REJECTED || this == CANCELLED;
    }

    /**
     * 判断是否可以修改
     *
     * @return true表示可以修改
     */
    public boolean isModifiable() {
        return this == APPLYING || this == APPROVED;
    }
}
