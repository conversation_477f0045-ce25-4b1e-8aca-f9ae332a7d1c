/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.config;

import com.fulfillmen.support.common.webclient.WebClientBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;

/**
 * 统一配置 WebClient
 *
 * <AUTHOR>
 * @date 2025/4/27 12:49
 * @description: todo
 * @since 1.0.0
 */
@Slf4j
@Configuration
public class WebClientConfig {

    /**
     * Fulfillmen Shop 通用的 WebClient
     * <p>
     * 第三方接口定义解码器不同，需使用注入的，已定制的ObjectMapper来配置解码器
     */
    @Deprecated
    @Bean(name = "fmWebClient")
    @ConditionalOnMissingBean(name = "fmWebClient")
    public WebClient fmWebClient() {
        // return WebClientBuilder
        return WebClientBuilder.getWebClientBuilder().build();
    }

}
