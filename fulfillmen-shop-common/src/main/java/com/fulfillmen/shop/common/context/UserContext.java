/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.common.context;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Builder;

/**
 * 用户上下文
 *
 * @param id                     用户 id
 * @param username               用户名
 * @param wmsCusCode             wms code 客户编码
 * @param wmsApiKey              wms 用户api key
 * @param wmsServiceFee          wms 服务费
 * @param tenantId               所属租户 ID
 * @param pwdResetTime           最后一次修改密码时间
 * @param passwordExpirationDays 登录时系统设置的密码过期天数
 *
 * <AUTHOR>
 * @date 2025/4/27 14:17
 * @description: todo
 * @since 1.0.0
 */
@Builder
public record UserContext(
        Long id,
        String username,
        String wmsCusCode,
        String wmsApiKey,
        BigDecimal wmsServiceFee,
        Long tenantId,
        LocalDateTime pwdResetTime,
        Integer passwordExpirationDays) implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    // 编写重载
    public UserContext(Long id, String username) {
        this(id, username, null, null, null, 0L, null, null);
    }

    public UserContext(Long id, String username, String wmsCusCode, String wmsApiKey, Long tenantId,
            BigDecimal wmsServiceFee) {
        this(id, username, wmsCusCode, wmsApiKey, wmsServiceFee, tenantId, null, null);
    }

}
