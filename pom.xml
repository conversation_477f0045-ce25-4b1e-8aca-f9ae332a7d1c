<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fulfillmen.starter</groupId>
    <artifactId>fulfillmen-starter-parent</artifactId>
    <version>1.2.7-SNAPSHOT</version>
    <relativePath/>
  </parent>
  <groupId>com.fulfillmen.shop</groupId>
  <artifactId>fulfillmen-shop</artifactId>
  <version>${revision}</version>
  <name>Fulfillmen Shop</name>
  <url>https:/shop.fulfillmen.com</url>
  <packaging>pom</packaging>

  <description>
    Fulfillmen 采购系统
    前端采用 vue3 + vite + ts 编写
  </description>

  <modules>
    <!-- 构建工具 -->
    <module>build-tools</module>
    <!-- 公共类库 -->
    <module>fulfillmen-shop-common</module>
    <!-- 通用业务管理、对接开放 API 接口 -->
    <module>fulfillmen-shop-manager</module>
    <!-- 数据访问层 -->
    <module>fulfillmen-shop-dao</module>
    <!-- 模型 包含 数据库实体类、数据传输对象、视图对象 -->
    <module>fulfillmen-shop-domain</module>
    <!-- 启动 + 前端页面 -->
    <module>fulfillmen-shop-bootstrap</module>
    <!-- 接口层 提供开放接口 -->
    <module>fulfillmen-shop-api/fulfillmen-shop-openapi</module>
    <module>fulfillmen-shop-api/fulfillmen-shop-openapi-service</module>
    <!-- 业务系统 -->
    <module>fulfillmen-shop-system</module>
  </modules>

  <properties>
    <revision>1.0.0-SNAPSHOT</revision>
    <fulfillmen-support.version>1.2.2-SNAPSHOT</fulfillmen-support.version>
    <fulfillmen.version>1.2.3-SNAPSHOT</fulfillmen.version>
    <spring-boot-starter.revision>3.3.11</spring-boot-starter.revision>

    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!-- jdk version 21 -->
    <java.version>21</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven.compiler.compilerVersion>${java.version}</maven.compiler.compilerVersion>
    <!-- retrofit 一个基于 okhttp ，声明式请求工具。主打一个优雅 -->
    <retrofit.version>2.9.0</retrofit.version>
    <!-- MapStruct -->
    <mapstruct.version>1.6.3</mapstruct.version>
    <lombok.version>1.18.36</lombok.version>
    <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    <resource.delimiter>@</resource.delimiter>

    <maven.javadoc.failOnError>false</maven.javadoc.failOnError>
    <project.build.sourseceEncoding>UTF-8</project.build.sourseceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <maven-compiler-plugin.version>3.14.0</maven-compiler-plugin.version>
    <maven-checkstyle-plugin.version>3.6.0</maven-checkstyle-plugin.version>
    <!-- kryo5 序列化 -->
    <kryo5.version>5.5.0</kryo5.version>
    <maven.build.timestamp.format>yyyy</maven.build.timestamp.format>
    <project.inceptionYear>${maven.build.timestamp}</project.inceptionYear>
    <maven.compiler.useIncrementalCompilation>true</maven.compiler.useIncrementalCompilation>
    <maven.compiler.meminitial>512m</maven.compiler.meminitial>
    <maven.compiler.maxmem>1024m</maven.compiler.maxmem>
    <maven.compiler.parallel>true</maven.compiler.parallel>

    <netty.version>4.2.1.Final</netty.version>
    <!-- 轻量级 限流 重试 熔断 组件 -->
    <resilience4j-spring-boot3.version>2.3.0</resilience4j-spring-boot3.version>
    <bucket4j-spring-boot-starter.version>0.12.8</bucket4j-spring-boot-starter.version>
  </properties>

  <!-- 依赖的版本 -->
  <dependencyManagement>
    <dependencies>

      <!-- 项目内部模块依赖管理 -->
      <!-- 公共类库 -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-common</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- 实体库 - 包含 do dto po vo -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-domain</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- 数据库访问层 -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-dao</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- 通用业务层、缓存、MQ 中间件 -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-manager</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- ERP 业务层 & Web -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>frontend-service</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>frontend-web</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- API 服务 -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-openapi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-openapi-service</artifactId>
        <version>${project.version}</version>
      </dependency>
      <!-- 引导层 -->
      <dependency>
        <groupId>com.fulfillmen.shop</groupId>
        <artifactId>fulfillmen-shop-bootstrap</artifactId>
        <version>${project.version}</version>
      </dependency>

      <!-- 第三方依赖版本管理 -->
      <!-- 声明式 API 请求 -->
      <dependency>
        <groupId>com.squareup.retrofit2</groupId>
        <artifactId>retrofit</artifactId>
        <version>${retrofit.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.retrofit2</groupId>
        <artifactId>converter-jackson</artifactId>
        <version>${retrofit.version}</version>
      </dependency>
      <dependency>
        <groupId>com.squareup.retrofit2</groupId>
        <artifactId>adapter-rxjava2</artifactId>
        <version>${retrofit.version}</version>
      </dependency>

      <!-- 接入 alibaba api -->
      <dependency>
        <groupId>com.fulfillmen.support</groupId>
        <artifactId>fulfillmen-support-alibaba</artifactId>
        <version>${fulfillmen-support.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fulfillmen.support</groupId>
        <artifactId>fulfillmen-support-common</artifactId>
        <version>${fulfillmen-support.version}</version>
      </dependency>

      <dependency>
        <groupId>com.fulfillmen.support</groupId>
        <artifactId>fulfillmen-support-wms</artifactId>
        <version>${fulfillmen-support.version}</version>
      </dependency>


      <!-- 使用 kryo5 序列化 -->
      <dependency>
        <groupId>com.esotericsoftware.kryo</groupId>
        <artifactId>kryo5</artifactId>
        <version>${kryo5.version}</version>
      </dependency>
      <!-- mapstruct -->
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct-processor</artifactId>
        <version>${mapstruct.version}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.38</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok-mapstruct-binding</artifactId>
        <version>${lombok-mapstruct-binding.version}</version>
      </dependency>

      <!-- bucket4j 限流组件 -->
      <dependency>
        <groupId>com.giffing.bucket4j.spring.boot.starter</groupId>
        <artifactId>bucket4j-spring-boot-starter</artifactId>
        <version>${bucket4j-spring-boot-starter.version}</version>
      </dependency>

      <dependency>
        <groupId>com.bucket4j</groupId>
        <artifactId>bucket4j-redis</artifactId>
        <version>8.10.1</version>
      </dependency>

      <!-- Resilience4J 限流组件 -->
      <!-- <dependency>
         <groupId>io.github.resilience4j</groupId>
         <artifactId>resilience4j-spring-boot3</artifactId>
         <version>${resilience4j-spring-boot3.version}</version>
       </dependency>

       <dependency>
         <groupId>io.github.resilience4j</groupId>
         <artifactId>resilience4j-ratelimiter</artifactId>
         <version>${resilience4j-spring-boot3.version}</version>
       </dependency>-->

    </dependencies>
  </dependencyManagement>


  <dependencies>
    <!-- Hutool（小而全的 Java 工具类库，通过静态方法封装，降低相关 API 的学习成本，提高工作效率，使 Java 拥有函数式语言般的优雅，让 Java
    语言也可以"甜甜的"） -->
    <dependency>
      <groupId>cn.hutool</groupId>
      <artifactId>hutool-all</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
      <version>${lombok-mapstruct-binding.version}</version>
      <scope>provided</scope>
    </dependency>

    <!-- MapStruct -->
    <dependency>
      <groupId>org.mapstruct</groupId>
      <artifactId>mapstruct</artifactId>
    </dependency>

    <!-- 日期时间类型 -->
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
    </dependency>

    <!-- SLF4J API -->
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
    </dependency>

    <!-- Spring Boot Starter -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>

    <!-- AOP 支持 -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>

  </dependencies>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>com.google.cloud.tools</groupId>
          <artifactId>jib-maven-plugin</artifactId>
          <version>3.4.4</version>
        </plugin>
        <!-- 编译插件 -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <parameters>true</parameters>
            <compilerArgs>
              <arg>-parameters</arg>
            </compilerArgs>
            <useIncrementalCompilation>true</useIncrementalCompilation>
            <maxmem>1024M</maxmem>
            <meminitial>512M</meminitial>
            <compilerVersion>${java.version}</compilerVersion>
            <source>${java.version}</source>
            <target>${java.version}</target>
            <encoding>${project.build.sourceEncoding}</encoding>
            <!-- See https://maven.apache.org/plugins/maven-compiler-plugin/compile-mojo.html -->
            <!-- Classpath elements to supply as annotation processor path. If specified, the compiler   -->
            <!-- will detect annotation processors only in those classpath elements. If omitted, the     -->
            <!-- default classpath is used to detect annotation processors. The detection itself depends -->
            <!-- on the configuration of annotationProcessors.                                           -->
            <!--                                                                                         -->
            <!-- According to this documentation, the provided dependency processor is not considered!   -->
            <annotationProcessorPaths>
              <path>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
              </path>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok-mapstruct-binding</artifactId>
                <version>${lombok-mapstruct-binding.version}</version>
              </path>
            </annotationProcessorPaths>
          </configuration>
        </plugin>

        <!-- 代码格式化插件 -->
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>2.40.0</version>
        </plugin>
      </plugins>

    </pluginManagement>
    <plugins>

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
      <!-- 单元测试相关插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <!-- 跳过单元测试 -->
          <skip>true</skip>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>3.3.1</version>
      </plugin>
      <!-- 代码格式化插件 -->
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <version>2.40.0</version>
        <inherited>true</inherited>
        <dependencies>
          <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
          </dependency>
          <dependency>
            <groupId>com.fulfillmen.shop</groupId>
            <artifactId>build-tools</artifactId>
            <version>1.0-SNAPSHOT</version>
          </dependency>
        </dependencies>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>apply</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <java>
            <removeUnusedImports/>
            <eclipse>
              <file>config/FulfillmenJavaStyle.xml</file>
            </eclipse>
          </java>
        </configuration>
      </plugin>
    </plugins>

    <resources>
      <!-- 一定要声明如下配置-->
      <resource>
        <directory>src/main/java</directory>
        <includes>
          <include>**/*.xml</include>
          <include>**/*.yml</include>
          <include>**/*.sql</include>
          <include>**/*.key</include>
        </includes>
        <filtering>true</filtering>
      </resource>

      <!--指定资源的位置-->
      <resource>
        <directory>src/main/resources</directory>
      </resource>
    </resources>
  </build>

  <profiles>
    <!-- 依赖管理 Profile -->
    <profile>
      <id>dependency-management</id>
      <build>
        <plugins>
          <!-- 依赖分析插件 -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>

            <executions>
              <execution>
                <id>analyze-dependencies</id>
                <goals>
                  <goal>analyze</goal>
                </goals>
                <configuration>
                  <failOnWarning>false</failOnWarning>
                  <ignoreNonCompile>true</ignoreNonCompile>
                </configuration>
              </execution>
              <execution>
                <id>tree-dependencies</id>
                <goals>
                  <goal>tree</goal>
                </goals>
                <configuration>
                  <outputFile>${project.build.directory}/dependency-tree.txt</outputFile>
                </configuration>
              </execution>
            </executions>
          </plugin>

          <!-- 强制规则插件 -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-enforcer-plugin</artifactId>
            <version>3.4.1</version>
            <executions>
              <execution>
                <id>enforce-dependency-rules</id>
                <goals>
                  <goal>enforce</goal>
                </goals>
                <configuration>
                  <rules>
                    <!-- 禁止循环依赖 -->
                    <banCircularDependencies/>
                    <!-- 要求 Maven 版本 -->
                    <requireMavenVersion>
                      <version>[3.6.0,)</version>
                    </requireMavenVersion>
                    <!-- 要求 Java 版本 -->
                    <requireJavaVersion>
                      <version>[17,)</version>
                    </requireJavaVersion>
                    <!-- 禁止重复依赖 -->
                    <banDuplicatePomDependencyVersions/>
                    <!-- 要求依赖收敛 -->
                    <dependencyConvergence/>
                  </rules>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>

    <!-- macos arm 环境特定 自动激活-->
    <profile>
      <id>macos-m1</id>
      <activation>
        <os>
          <family>mac</family>
          <arch>aarch64</arch>
        </os>
      </activation>
      <dependencies>
        <dependency>
          <groupId>io.netty</groupId>
          <artifactId>netty-resolver-dns-native-macos</artifactId>
          <classifier>osx-aarch_64</classifier>
          <version>${netty.version}</version>
          <scope>runtime</scope>
        </dependency>
      </dependencies>
    </profile>

    <!-- 代码风格检查 -->
    <profile>
      <id>checkstyle</id>
      <build>
        <plugins>
          <!-- 代码风格检查 -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>${maven-checkstyle-plugin.version}</version>
            <dependencies>
              <dependency>
                <groupId>com.fulfillmen.shop</groupId>
                <artifactId>build-tools</artifactId>
                <version>1.0-SNAPSHOT</version>
              </dependency>
            </dependencies>
            <configuration>
              <configLocation>config/checkstyle.xml</configLocation>
              <suppressionsLocation>config/checkstyle-suppressions.xml</suppressionsLocation>
              <suppressionsFileExpression>checkstyle.suppressions.file</suppressionsFileExpression>
              <!-- 排除部分 -->
              <excludes>**/build/**</excludes>
              <!--<excludes>**/build/**,**/generated/**,**/target/**,**/test/**,**/*.json</excludes>-->
              <excludes>**/generated/**</excludes>
              <excludes>**/target/**</excludes>
              <excludes>**/test/**</excludes>
              <excludes>**/*.json</excludes>
              <!-- 仅扫描此目录 -->
              <includes>**/jxc-*/src/main/java/**</includes>
            </configuration>
            <executions>
              <execution>
                <id>verify-style</id>
                <phase>verify</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <consoleOutput>true</consoleOutput>
                  <failsOnError>true</failsOnError>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>

      <reporting>
        <!--所有子模块都要执行的报告-->
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-checkstyle-plugin</artifactId>
            <version>${maven-checkstyle-plugin.version}</version>
          </plugin>
        </plugins>
      </reporting>
    </profile>

    <!--<profile>
        <id>project-package</id>
        <build>
            <plugins>

            </plugins>
        </build>
    </profile>-->
    <profile>
      <id>test</id>
      <properties>
        <skipTests>false</skipTests>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-surefire-plugin</artifactId>
            <configuration combine.self="override">
              <skipTests>false</skipTests>
              <includes>
                <include>**/*Test.java</include>
              </includes>
              <excludes>
                <exclude>none</exclude>
              </excludes>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

</project>
