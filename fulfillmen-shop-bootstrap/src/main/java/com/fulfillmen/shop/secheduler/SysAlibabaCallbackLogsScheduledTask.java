/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fulfillmen.shop.dao.mapper.SysAlibabaCallbackLogsMapper;
import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 系统阿里巴巴回调日志定时任务
 *
 * <pre>
 * - 支持通过配置清理历史日志：shop.alibaba.callback-log.retention-days（默认 30 天）
 * - 以 gmt_created 为基准时间清理早于阈值的数据
 * - 分布式锁防并发：仅一个实例执行
 * - 分批删除：每批 1000 条，直到不足一批
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/8/8
 * @since 1.0.0
 */
@Slf4j
@Profile({"!dev", "!local"})
@Component
@RequiredArgsConstructor
public class SysAlibabaCallbackLogsScheduledTask {

    private static final String CLEANUP_LOCK_KEY = "scheduled:alibaba:callback-log:cleanup";
    private static final int DEFAULT_RETENTION_DAYS = 30;
    private static final int BATCH_SIZE = 1000;

    private final SysAlibabaCallbackLogsMapper callbackLogsMapper;

    @Value("${shop.alibaba.callback-log.retention-days:" + DEFAULT_RETENTION_DAYS + "}")
    private int retentionDays;

    /**
     * 每日 0 点执行清理
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void scheduledCleanupOldLogs() {
        if (retentionDays <= 0) {
            log.warn("retentionDays 配置无效: {}，重置为默认值 {} 天", retentionDays, DEFAULT_RETENTION_DAYS);
            retentionDays = DEFAULT_RETENTION_DAYS;
        }

        // 分布式锁，避免多实例并发执行
        boolean locked = RedisUtils.tryLock(CLEANUP_LOCK_KEY, 600_000L, 0L);
        if (!locked) {
            log.info("阿里巴巴回调日志清理任务跳过：未获取到分布式锁");
            return;
        }

        long start = System.currentTimeMillis();
        LocalDateTime cutoff = LocalDateTime.now().minusDays(retentionDays);
        int totalDeleted = 0;
        int batchCount = 0;

        try {
            while (true) {
                // 先查一批 ID，避免大事务与过多内存占用
                LambdaQueryWrapper<SysAlibabaCallbackLogs> selectWrapper = Wrappers
                    .<SysAlibabaCallbackLogs>lambdaQuery()
                    .select(SysAlibabaCallbackLogs::getId, SysAlibabaCallbackLogs::getGmtCreated)
                    .le(SysAlibabaCallbackLogs::getGmtCreated, cutoff)
                    .orderByAsc(SysAlibabaCallbackLogs::getGmtCreated)
                    .last("LIMIT " + BATCH_SIZE);

                List<SysAlibabaCallbackLogs> candidates = callbackLogsMapper.selectList(selectWrapper);
                if (candidates == null || candidates.isEmpty()) {
                    break;
                }

                List<Long> ids = candidates.stream().map(SysAlibabaCallbackLogs::getId).collect(Collectors.toList());

                int deleted = callbackLogsMapper.delete(
                    Wrappers.<SysAlibabaCallbackLogs>lambdaQuery().in(SysAlibabaCallbackLogs::getId, ids));
                totalDeleted += deleted;
                batchCount++;

                if (deleted < BATCH_SIZE) {
                    // 当前批次小于批大小，说明已清理完毕
                    break;
                }
            }

            log.info("阿里巴巴回调日志清理完成：阈值时间={}, 删除条数={}, 批次数={}, 耗时={}ms",
                cutoff, totalDeleted, batchCount, System.currentTimeMillis() - start);
        } catch (Exception e) {
            log.error("阿里巴巴回调日志清理失败：阈值时间={}", cutoff, e);
        } finally {
            RedisUtils.unlock(CLEANUP_LOCK_KEY);
        }
    }

}
