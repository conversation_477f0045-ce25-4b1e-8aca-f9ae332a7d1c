/*
 * Copyright (c) 2022-present fulfillmen.com Org. All Rights Reserved.
 */

package com.fulfillmen.shop.secheduler;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.fulfillmen.shop.domain.entity.SysAlibabaCallbackLogs;
import com.fulfillmen.shop.manager.core.repository.SysAlibabaCallbackLogsRepository;
import com.fulfillmen.shop.manager.support.alibaba.webhook.service.OrderWebhookService;
import com.fulfillmen.shop.manager.support.notification.service.WeChatNotificationService;
import com.fulfillmen.starter.cache.redisson.util.RedisUtils;
import com.fulfillmen.support.alibaba.enums.OrderMessageTypeEnums;
import com.fulfillmen.support.alibaba.webhook.MessageEvent;
import com.fulfillmen.support.alibaba.webhook.data.OrderMessage;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里巴巴回调重试定时任务
 *
 * <pre>
 * 功能说明：
 * 1. 定期扫描失败的回调记录（最多重试3次）
 * 2. 重新执行失败的回调处理逻辑
 * 3. 达到最大重试次数后发送企业微信通知
 * 4. 使用分布式锁防止多实例并发执行
 *
 * 配置项：
 * - shop.alibaba.callback-retry.max-retry-count: 最大重试次数（默认3次）
 * - shop.alibaba.callback-retry.batch-size: 每批处理数量（默认20条）
 * - shop.alibaba.callback-retry.time-window-hours: 时间窗口小时数（默认24小时）
 * </pre>
 *
 * <AUTHOR>
 * @date 2025/08/09 12:10
 * @since 1.0.0
 */
@Slf4j
@Profile({ "!dev", "!local" })
@Component
@RequiredArgsConstructor
public class SysAlibabaCallbackRetryScheduledTask {

    private static final String RETRY_LOCK_KEY = "scheduled:alibaba:callback:retry";
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final SysAlibabaCallbackLogsRepository callbackLogsRepository;
    private final OrderWebhookService orderWebhookService;
    private final WeChatNotificationService weChatNotificationService;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Value("${shop.alibaba.callback-retry.max-retry-count:3}")
    private int maxRetryCount;

    @Value("${shop.alibaba.callback-retry.batch-size:20}")
    private int batchSize;

    @Value("${shop.alibaba.callback-retry.time-window-hours:24}")
    private int timeWindowHours;

    @Value("${shop.alibaba.callback-retry.enabled:true}")
    private boolean retryEnabled;

    /**
     * 每15分钟执行一次重试任务
     */
    @Scheduled(cron = "0 */15 * * * ?")
    public void scheduledRetryFailedCallbacks() {
        if (!retryEnabled) {
            log.debug("阿里巴巴回调重试功能已禁用");
            return;
        }

        // 分布式锁，避免多实例并发执行
        boolean locked = RedisUtils.tryLock(RETRY_LOCK_KEY, 600_000L, 0L);
        if (!locked) {
            log.info("阿里巴巴回调重试任务跳过：未获取到分布式锁");
            return;
        }

        long startTime = System.currentTimeMillis();
        int totalRetried = 0;
        int totalFinalFailed = 0;

        try {
            log.info("开始执行阿里巴巴回调重试任务，配置：maxRetryCount={}, batchSize={}, timeWindowHours={}",
                    maxRetryCount, batchSize, timeWindowHours);

            // 查询需要重试的失败记录
            List<SysAlibabaCallbackLogs> failedLogs = callbackLogsRepository.findFailedLogsForRetry(
                    maxRetryCount, timeWindowHours, batchSize);

            if (failedLogs.isEmpty()) {
                log.info("没有需要重试的失败记录");
                return;
            }

            log.info("找到 {} 条需要重试的失败记录", failedLogs.size());

            // 并行处理重试任务
            @SuppressWarnings("unchecked")
            CompletableFuture<Void>[] retryTasks = failedLogs.stream()
                    .map(this::processRetryAsync)
                    .toArray(CompletableFuture[]::new);

            // 等待所有重试任务完成
            CompletableFuture.allOf(retryTasks).join();

            // 统计结果
            for (SysAlibabaCallbackLogs log : failedLogs) {
                if (log.getRetryCount() >= maxRetryCount) {
                    totalFinalFailed++;
                } else {
                    totalRetried++;
                }
            }

            long duration = System.currentTimeMillis() - startTime;
            log.info("阿里巴巴回调重试任务完成：重试成功={}, 最终失败={}, 耗时={}ms",
                    totalRetried, totalFinalFailed, duration);

        } catch (Exception e) {
            log.error("阿里巴巴回调重试任务执行异常", e);
            // 发送系统异常通知
            weChatNotificationService.sendSystemErrorNotification(
                    "回调重试任务异常",
                    "阿里巴巴回调重试定时任务执行异常：" + e.getMessage());
        } finally {
            RedisUtils.unlock(RETRY_LOCK_KEY);
        }
    }

    /**
     * 异步处理单个回调记录的重试
     */
    private CompletableFuture<Void> processRetryAsync(SysAlibabaCallbackLogs callbackLog) {
        return CompletableFuture.runAsync(() -> {
            try {
                processRetry(callbackLog);
            } catch (Exception e) {
                log.error("处理回调记录重试异常: logId={}, orderId={}",
                        callbackLog.getId(), callbackLog.getOrderId(), e);
            }
        }, threadPoolTaskExecutor);
    }

    /**
     * 处理单个回调记录的重试
     */
    private void processRetry(SysAlibabaCallbackLogs callbackLog) {
        Long logId = callbackLog.getId();
        Long orderId = callbackLog.getOrderId();
        String eventType = callbackLog.getEventType();
        int currentRetryCount = callbackLog.getRetryCount() != null ? callbackLog.getRetryCount() : 0;

        log.info("开始重试回调记录: logId={}, orderId={}, eventType={}, currentRetryCount={}",
                logId, orderId, eventType, currentRetryCount);

        try {
            // 更新重试次数并标记为处理中
            int newRetryCount = currentRetryCount + 1;
            callbackLogsRepository.updateRetryCount(logId, newRetryCount);

            // 从元数据中重构消息和事件
            MessageEvent<OrderMessage> messageEvent = reconstructMessageEvent(callbackLog);
            OrderMessage orderMessage = messageEvent.getData();
            OrderMessageTypeEnums messageTypeEnum = OrderMessageTypeEnums.fromMessageType(eventType);

            if (messageTypeEnum == null) {
                log.warn("不支持的消息类型，跳过重试: eventType={}, orderId={}", eventType, orderId);
                callbackLogsRepository.markFailed(logId, "不支持的消息类型: " + eventType);
                return;
            }

            // 重新执行订单处理逻辑
            orderWebhookService.processOrderWebhook(orderMessage, messageEvent, messageTypeEnum);

            // 标记成功
            callbackLogsRepository.markSuccess(logId);
            log.info("回调记录重试成功: logId={}, orderId={}, retryCount={}", logId, orderId, newRetryCount);

        } catch (Exception e) {
            log.error("回调记录重试失败: logId={}, orderId={}, error={}", logId, orderId, e.getMessage(), e);

            int newRetryCount = currentRetryCount + 1;
            String failureReason = "重试失败: " + e.getMessage();
            callbackLogsRepository.markFailed(logId, failureReason);

            // 检查是否达到最大重试次数
            if (newRetryCount >= maxRetryCount) {
                log.warn("回调记录达到最大重试次数，发送通知: logId={}, orderId={}, maxRetryCount={}",
                        logId, orderId, maxRetryCount);

                // 发送企业微信通知
                sendRetryFailedNotification(callbackLog, newRetryCount, failureReason);
            }
        }
    }

    /**
     * 从回调日志重构消息事件
     */
    private MessageEvent<OrderMessage> reconstructMessageEvent(SysAlibabaCallbackLogs callbackLog) {
        try {
            // 尝试从元数据中解析原始事件
            String metadata = callbackLog.getMetadata();
            if (metadata != null && metadata.contains("message=")) {
                // 简化的事件重构，实际可能需要更复杂的解析逻辑
                MessageEvent<OrderMessage> event = new MessageEvent<>();
                event.setMsgId("retry-" + callbackLog.getId());
                // 简化处理，使用一个通用的消息类型
                event.setType(OrderMessageTypeEnums.fromMessageType(callbackLog.getEventType()));
                event.setReceivedAt(callbackLog.getReceivedTimestamp());

                // 构建基本的订单消息
                OrderMessage orderMessage = new OrderMessage();
                orderMessage.setOrderId(callbackLog.getOrderId());
                // 这里可能需要从数据库或缓存中获取更完整的订单信息

                event.setData(orderMessage);
                return event;
            }
        } catch (Exception e) {
            log.warn("重构消息事件失败: logId={}", callbackLog.getId(), e);
        }

        // 返回基本的事件对象
        MessageEvent<OrderMessage> event = new MessageEvent<>();
        event.setMsgId("retry-" + callbackLog.getId());
        event.setType(OrderMessageTypeEnums.fromMessageType(callbackLog.getEventType()));
        event.setReceivedAt(callbackLog.getReceivedTimestamp());

        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderId(callbackLog.getOrderId());
        event.setData(orderMessage);

        return event;
    }

    /**
     * 发送重试失败通知
     */
    private void sendRetryFailedNotification(SysAlibabaCallbackLogs callbackLog, int retryCount, String failureReason) {
        try {
            String lastFailedTime = callbackLog.getGmtModified() != null
                    ? callbackLog.getGmtModified().format(FORMATTER)
                    : "未知";

            weChatNotificationService.sendCallbackRetryFailedNotification(
                    callbackLog.getOrderId(),
                    callbackLog.getEventType(),
                    retryCount,
                    failureReason,
                    lastFailedTime);
        } catch (Exception e) {
            log.error("发送重试失败通知异常: logId={}", callbackLog.getId(), e);
        }
    }
}
