/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS采购订单详情更新
 *
 * <p>定义WMS采购订单详情的响应结构，包含订单基本信息和商品详情。
 * 使用JsonAlias注解支持多种字段名格式的兼容性。</p>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WmsPurchaseOrderDetailReq {

    /**
     * 采购单号
     * <pre>
     * 此采购单号指的是wms 的采购单号 它是唯一的
     * </pre>
     */
    private String purchaseNo;

    /**
     * 店铺订单 id
     */
    private String shopOrderId;

    /**
     * 卖家id
     */
    private String sellerOpenId;

    /**
     * 店铺 id
     */
    private String storeId;

    /**
     * 交易流水号
     */
    private String outTradeNo;

    /**
     * 订单 id —> 1688 orderId
     */
    private Long orderId;

    /**
     * 客户内部id
     */
    private Integer customerId;

    /**
     * 创建订单用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 平台标识
     */
    private String platform;

    /**
     * 支付类型
     */
    private WmsPayTypeEnum payType;

    /**
     * 支付链接
     */
    private String payUrl;

    /**
     * 支付表单数据
     */
    private String payBody;

    /**
     * 平台支付类型
     */
    private String platformPayType;

    /**
     * 运费
     */
    private BigDecimal shippingFee;

    /**
     * 原始运费
     */
    @JsonProperty("originalShppingfee")
    private BigDecimal originalShippingFee;

    /**
     * 商品原始总金额
     */
    private BigDecimal productOriginalTotalAmount;

    /**
     * 客户支付的总金额
     */
    private BigDecimal total;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 商品销售总金额
     */
    private BigDecimal productSalesTotalAmount;

    /**
     * 商品最终总金额
     * <pre>
     * 商品最终总金额 = 商品销售总金额 productSalesTotalAmount - 折扣金额 discount - Plus折扣 plusDiscount - 优惠券折扣 couponDiscount
     * </pre>
     */
    private BigDecimal productFinalTotalAmount;

    /**
     * 最终运费
     */
    private BigDecimal finalShoppingFee;

    /**
     * 原始总价
     */
    private BigDecimal originalTotalPrice;

    /**
     * 1688最终金额
     * <pre>
     * 支付后显示此字段
     * </pre>
     */
    private BigDecimal alibabaFinalAmount;

    /**
     * 1688总金额
     * <pre>
     * 改价字段修改此字段
     * </pre>
     */
    private BigDecimal alibabaTotalAmount;

    /**
     * 折扣金额
     */
    private BigDecimal discount;

    /**
     * Plus折扣
     */
    private BigDecimal plusDiscount;

    /**
     * 优惠券折扣
     */
    private BigDecimal couponDiscount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 状态
     */
    private WmsOrderStatusEnum status;

    /**
     * 是否询价
     */
    private Boolean isRequestQuote;

    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 发货时间
     */
    private LocalDateTime shippingTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 商品链接
     */
    private String link;

    /**
     * 物流单号
     */
    private String trackingNo;

    /**
     * 平台状态
     */
    private String platformStatus;

    /**
     * 平台备注
     */
    private String platformRemark;

    // ==================== 商品详情字段 ====================

    private List<WmsPurchaseOrderDetailsReq> orderDetails;
}