/*
 * Copyright (c) 2024-present Fulfillmen Org. All Rights Reserved.
 */

package com.fulfillmen.support.wms.dto.request;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fulfillmen.support.wms.dto.enums.WmsOrderStatusEnum;
import com.fulfillmen.support.wms.dto.enums.WmsPayTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WMS 采购订单请求
 *
 * <pre>
 * 定义WMS采购订单的详细信息，包含订单基本信息、金额信息、状态信息等。
 * 支持完整的订单生命周期管理。
 * </pre>
 *
 * <AUTHOR>
 * @created 2025-01-21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WmsPurchaseOrderReq {

    /**
     * 外部订单号
     */
    private String purchaseNo;

    /**
     * 采购单号
     */
    private String nayaPurchaseNo;

    /**
     * 店铺订单 id
     */
    private String shopOrderId;

    /**
     * 客户内部id
     */
    @JsonProperty("gFF_CustomerID")
    private Integer gffCustomerId;

    /**
     * 订单id
     */
    @JsonProperty("orderID")
    private Long orderId;

    /**
     * 卖家id
     */
    private String sellerOpenId;

    /**
     * 店铺id 默认值536
     * <p>
     * wms 仓库 ，warehouseId
     * </p>
     */
    @JsonProperty("storeID")
    private String storeId;

    /**
     * 创建订单用户
     */
    private String createUser;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 备注 默认填写 nayasource
     */
    private String remark;

    /**
     * 平台标识
     */
    private String platform;

    /**
     * 支付类型, 0: balance, 1: alipay 2: creditCard
     */
    private WmsPayTypeEnum payType;

    /**
     * 交易流水号
     */
    private String outTradeNo;

    /**
     * 支付链接
     */
    private String payUrl;

    /**
     * 支付表单数据
     */
    private String payBody;

    /**
     * 平台支付类型
     */
    private String platformPayType;

    /**
     * 运费
     */
    private BigDecimal shippingFee;

    /**
     * 原始运费
     */
    private BigDecimal originalShippingFee;

    /**
     * 商品原始总金额
     */
    private BigDecimal productOriginalTotalAmount;

    /**
     * 客户支付的总金额
     * <p>
     * 商品金额 + 运费总计
     * </p>
     */
    private BigDecimal total;

    /**
     * 服务费，平台默认获取 租户服务费率。
     * <p>
     * wms 根据 wms 客户服务费率收取
     * </p>
     */
    private BigDecimal serviceFee;

    /**
     * 商品销售总金额 = 总采购费用
     * <p>
     * 销售总金额 = 商品原价总金额 customerPaidTotal + 服务费 serviceFee
     * </p>
     */
    @JsonAlias("productSalesTotalAmount")
    private BigDecimal salesTotalAmount;

    /**
     * 商品最终总金额
     * <p>
     * 商品最终总金额 = 商品销售总金额 productSalesTotalAmount - 折扣金额 discount - Plus折扣 plusDiscount - 优惠券折扣 couponDiscount
     * </p>
     */
    private BigDecimal productFinalTotalAmount;

    /**
     * 最终运费 - 销售与供应商谈后的运费成本
     */
    private BigDecimal finalShoppingFee;

    /**
     * 原始总价
     * <p>
     * 客户支付费用 原始总价 = 商品原价总金额 total + 运费 shippingFee
     * </p>
     */
    private BigDecimal originalTotalPrice;

    /**
     * 折扣金额
     */
    private BigDecimal discount;

    /**
     * Plus折扣
     */
    private BigDecimal plusDiscount;

    /**
     * 优惠券折扣
     */
    private BigDecimal couponDiscount;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 状态
     */
    private WmsOrderStatusEnum status;

    /**
     * 是否询价
     */
    private Boolean isRequestQuote;

    /**
     * 支付时间
     * <pre>
     * "createTime": "2025-07-18T15:58:49.513",
     * "paymentTime": "1970-01-01T00:00:00",
     * "shippingTime": "1970-01-01T00:00:00",
     * "completeTime": "1970-01-01T00:00:00",
     * </pre>
     */
    private LocalDateTime paymentTime;

    /**
     * 发货时间
     */
    private LocalDateTime shippingTime;

    /**
     * 完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 商品链接
     */
    private String link;

    /**
     * 物流单号
     */
    private String trackingNo;

    /**
     * 平台状态
     */
    private String platformStatus;

    /**
     * 平台备注
     */
    private String platformRemark;

    /**
     * 订单详情
     */
    private List<WmsPurchaseOrderDetailsReq> orderDetails;
}
